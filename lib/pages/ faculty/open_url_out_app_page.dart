
import 'package:flutter/material.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:url_launcher/url_launcher.dart';

class OpenUrlOutAppPage extends StatefulWidget {
  final String? url;
  final String? title;
  OpenUrlOutAppPage({Key? key, this.url, this.title}) : super(key: key);

  @override
  State<OpenUrlOutAppPage> createState() => _OpenUrlOutAppPageState();
}

class _OpenUrlOutAppPageState extends State<OpenUrlOutAppPage> {
  //bool isLoading = true;
  final _key = UniqueKey();

  _launchURL(String strUrl) async {
    final Uri url = Uri.parse(strUrl);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.WHITE,
      appBar: AppBar(
          elevation: 0.0,

          leading: InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: Icon(
                Icons.arrow_back,
                color: Colors.black,
              )),
          title: Text('${widget.title}'.replaceAll('Singularis', '${APK_DETAILS['app_name']}'),style: Styles.semibold(color: ColorConstants.BLACK,),),
          backgroundColor: ColorConstants.WHITE
      ),
      body: _launchURL(widget.url!),

      /*Stack(
        children: <Widget>[


          *//*isLoading
              ? Center(
            child: CircularProgressIndicator(),
          )
              : Stack(),*//*
        ],
      ),*/
    );
  }
}
