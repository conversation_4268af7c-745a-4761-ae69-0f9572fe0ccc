import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:masterg/data/providers/assessment_detail_provider.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/TapWidget.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/certificate_container.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/singularis/competition/assessment_certificate.dart';
import 'package:masterg/pages/training_pages/new_screen/assessment_attempt_page.dart';
import 'package:masterg/pages/training_pages/new_screen/assessment_review_page.dart';
import 'package:masterg/utils/Strings.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/custom_progress_indicator.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:pinput/pinput.dart';
import 'package:provider/provider.dart';
import 'dart:ui' as ui;

class AssessmentDetailPage extends StatefulWidget {
  final bool fromCompetition;
  final bool fromJob;
  final bool? isCertified;
  final String? programName;
  final int? programId;
  final bool? isEvent;

  const AssessmentDetailPage(
      {super.key,
      this.fromCompetition = false,
      this.fromJob = false,
      this.isCertified,
      this.programName,
      this.programId,
      this.isEvent});

  @override
  State<AssessmentDetailPage> createState() => _AssessmentDetailPageState();
}

class _AssessmentDetailPageState extends State<AssessmentDetailPage> {
  BuildContext? mContext;

  late AssessmentDetailProvider assessmentDetailProvider;
  late final TextEditingController pinController;

  @override
  void initState() {
    super.initState();
    pinController = TextEditingController();
  }

  @override
  Widget build(BuildContext context) {
    mContext = context;

    return Consumer<AssessmentDetailProvider>(
        builder: (context, assessmentDetailConsumer, child) {
      assessmentDetailProvider = assessmentDetailConsumer;
      return Scaffold(
        appBar: AppBar(
          iconTheme: IconThemeData(
            color: Colors.black, //change your color here
          ),
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: Colors.black,
            ),
            onPressed: () {
              Navigator.pop(context, 1);
            },
          ),
          title: Text(
            'assessment',
            style: TextStyle(color: Colors.black),
          ).tr(),
          backgroundColor: Colors.white,
          elevation: 0,
        ),
        resizeToAvoidBottomInset: false,
        backgroundColor: ColorConstants.WHITE,
        body: Stack(
          children: [
            assessmentDetailProvider.assessmentResponse != null
                ? widget.fromCompetition
                    ? _buildBody()
                    : SingleChildScrollView(
                        child: _buildBody(),
                      )
                : CustomProgressIndicator(true, ColorConstants.WHITE),

            /*if (!widget.fromCompetition)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: InkWell(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => CertificateView(
                                certificateId: assessmentDetailProvider
                                    .assessmentResponse!
                                    .data!
                                    .instruction!
                                    .details!
                                    .certificate!,
                                programName: widget.programName!,
                                contentId: assessmentDetailProvider
                                    .assessments.programId!)));
                  },
                  child: CertificateContainer(),
                ),
              )*/

            assessmentDetailProvider.assessmentResponse?.data?.instruction !=
                    null
                ? assessmentDetailProvider.assessmentResponse!.data!
                            .instruction!.details!.isCertificate !=
                        0
                    ? Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: InkWell(
                          onTap: () {
                            /* Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => CertificateView(
                                certificateId: assessmentDetailProvider
                                    .assessmentResponse!
                                    .data!
                                    .instruction!
                                    .details!
                                    .certificate!,
                                programName: programName!,
                                contentId: int.parse(assessmentDetailProvider
                                    .assessmentResponse!
                                    .data!
                                    .instruction!
                                    .details!.contentId.toString()))));*/
                            Navigator.of(context).push(MaterialPageRoute(
                                builder: (context) =>
                                    EventAssessmentCertificate(
                                      contentId: int.tryParse(
                                          assessmentDetailProvider
                                                  .assessmentResponse!
                                                  .data!
                                                  .instruction!
                                                  .details!
                                                  .contentId ??
                                              ''),
                                      certificateId: assessmentDetailProvider
                                          .assessmentResponse!
                                          .data!
                                          .instruction!
                                          .details!
                                          .certificateId,
                                      htmlUrl: assessmentDetailProvider
                                          .assessmentResponse!
                                          .data!
                                          .instruction!
                                          .details!
                                          .certificateHtmlUrl,
                                    )));
                          },
                          child: CertificateContainer(),
                        ),
                      )
                    : SizedBox()
                : SizedBox()
          ],
        ),
      );
    });
  }

  _buildBody() {
    DateTime startDate = DateTime.fromMillisecondsSinceEpoch(
        assessmentDetailProvider
                .assessmentResponse!.data!.instruction!.details!.startDate! *
            1000);
    DateTime endDate = DateTime.fromMillisecondsSinceEpoch(
        assessmentDetailProvider
                .assessmentResponse!.data!.instruction!.details!.endDate! *
            1000);

    bool isReviewAllow = false;
    if (assessmentDetailProvider
            .assessmentResponse!.data!.instruction!.details!.isReviewAllowed ==
        0) {
      isReviewAllow = false;
    } else if (assessmentDetailProvider.assessmentResponse!.data!.instruction!
                .details!.attemptAllowed ==
            0 &&
        assessmentDetailProvider
                .assessmentResponse!.data!.instruction!.details!.attemptCount! >
            0 &&
        assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!
                .isReviewAllowed ==
            1) {
      isReviewAllow = true;
    } else if (assessmentDetailProvider.assessmentResponse!.data!.instruction!
                .details!.attemptAllowed !=
            0 &&
        assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!
                .attemptCount! ==
            assessmentDetailProvider.assessmentResponse!.data!.instruction!
                .details!.attemptAllowed) {
      isReviewAllow = true;
    }

    if (assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!.isReviewAllowed == 1 &&
        assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!
                .attemptAllowed! ==
            0 &&
        assessmentDetailProvider
                .assessmentResponse!.data!.instruction!.details!.attemptCount! >
            0) {
      isReviewAllow = true;
    }

    return Container(
      height: MediaQuery.of(mContext!).size.height * 1.2,
      width: MediaQuery.of(mContext!).size.width,
      margin: EdgeInsets.only(bottom: 50),
      child: Padding(
        padding:
            widget.fromCompetition ? EdgeInsets.all(14.0) : EdgeInsets.all(0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.fromCompetition) ...[
              Text(
                '${assessmentDetailProvider.assessmentResponse?.data!.instruction!.details!.title}',
                style: Styles.bold(size: 14),
              ),
              SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${tr('submit_before')}: ',
                    style: Styles.regular(size: 12, color: Color(0xff5A5F73)),
                  ),
                  Text(
                    '${Utility.convertDateFromMillis(int.parse('${assessmentDetailProvider.assessmentResponse?.data!.instruction!.details!.endDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY + ', hh:mm a')}',
                    style: Styles.semibold(size: 12, color: Color(0xff0E1638)),
                    textDirection: ui.TextDirection.ltr,
                  ),
                ],
              ),
              SizedBox(height: 8),
              if (widget.fromJob == false)
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.isEvent == true
                          ? ''
                          : '${assessmentDetailProvider.assessmentResponse?.data!.instruction!.details!.maximumMarks} ${tr('marks')} ',
                      style: Styles.regular(
                        size: 12,
                      ),
                    ),
                    Text(widget.isEvent == true ? '' : ' • ',
                        style: Styles.regular(
                            color: ColorConstants.GREY_2, size: 12)),
                    Text(
                      '${tr('level')}: ',
                      style: Styles.regular(size: 12),
                    ),
                    Text(
                      '${assessmentDetailProvider.assessmentResponse?.data!.instruction!.details!.difficultyLevel}'
                          .capital(),
                      style: Styles.regular(
                          size: 12, color: ColorConstants.GREEN_1),
                    ),
                  ],
                ),
              SizedBox(height: 8),
              Text(
                '${assessmentDetailProvider.assessmentResponse?.data!.instruction!.details!.description}',
                style: Styles.regular(size: 14, color: ColorConstants.BLACK),
              ),

              if (!widget.fromCompetition)
                Padding(
                  padding: const EdgeInsets.only(
                    right: 60,
                  ),
                  child: Wrap(
                    children: List.generate(
                        assessmentDetailProvider.assessmentResponse!.data!
                            .instruction!.statement!.length,
                        (index) => Html(
                              data: assessmentDetailProvider.assessmentResponse!
                                  .data!.instruction!.statement![index],
                              style: {
                                "p": Style(
                                  fontFamily: 'Nunito_Regular',
                                )
                              },
                            )),
                  ),
                ),

              Spacer(),

              currentIndiaTime!.isAfter(startDate)
                  ? SizedBox()
                  : Text(
                      '${tr('assessment_not_yet1')}  ${Utility.convertDateFromMillis(int.parse('${assessmentDetailProvider.assessmentResponse?.data!.instruction!.details!.startDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)} ${tr('assessment_not_yet2')}',
                      style: Styles.semibold(
                          size: 12, color: ColorConstants.BLACK),
                      textDirection: ui.TextDirection.ltr,
                      textAlign: TextAlign.center,
                    ),

              // AudioPlayer(
              //                   // source: widget.currentQuestion.question
              //                   //     ?.questionImage?.first,
              //                   source: 'https://dict.youdao.com/dictvoice?audio=hello&type=1',
              //                   isLocalFile: false,
              //                 ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // if (assessmentDetailProvider.assessmentResponse!.data!
                  //         .instruction!.details!.attemptCount! >
                  //     0)

                  if (isReviewAllow)
                    SizedBox(
                      width: width(context) * 0.45,
                      child: TapWidget(
                        onTap: () {
                          if (assessmentDetailProvider.assessmentResponse!.data!
                                  .instruction!.details!.quizType !=
                              'text')
                            Navigator.push(
                                mContext!,
                                NextPageRoute(AssessmentAttemptPage(
                                  contentId: assessmentDetailProvider
                                      .assessments.programContentId,
                                  programId: assessmentDetailProvider
                                      .assessments.programId,
                                  // isInterview: widget.fromJob,
                                  isVideoTypeQuiz: assessmentDetailProvider
                                          .assessmentResponse!
                                          .data!
                                          .instruction!
                                          .details!
                                          .quizType !=
                                      'text',
                                  isReview: true,
                                  attemptAllowed: assessmentDetailProvider
                                      .assessmentResponse
                                      ?.data
                                      ?.instruction
                                      ?.details
                                      ?.attemptAllowed,
                                )));
                          else {
                            Navigator.push(
                                mContext!,
                                NextPageRoute(AssessmentReviewPage(
                                  contentId: assessmentDetailProvider
                                      .assessments.programContentId,
                                  programId: assessmentDetailProvider
                                      .assessments.programId,
                                )));
                          }
                          // Navigator.push(
                          //     mContext!,
                          //     NextPageRoute(AssessmentReviewPage(
                          //       contentId: assessmentDetailProvider
                          //           .assessments.programContentId,
                          //       programId:
                          //           assessmentDetailProvider.assessments.programId,
                          //     )));
                        },
                        child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(colors: [
                              ColorConstants().gradientLeft(),
                              ColorConstants().gradientRight(),
                            ]),
                            color: ColorConstants().gradientRight(),
                            borderRadius: BorderRadius.all(Radius.circular(5)),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.only(
                                left: 8, right: 8, top: 4, bottom: 4),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'review',
                                  style: Styles.textExtraBold(
                                      size: 14,
                                      color: ColorConstants()
                                          .primaryForgroundColor()),
                                ).tr(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                  SizedBox(
                    width: isReviewAllow
                        ? width(context) * 0.45
                        : width(context) * 0.93,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TapWidget(
                        onTap: () async {
                          if (currentIndiaTime!.isAfter(startDate) &&
                              currentIndiaTime!.isBefore(endDate)) {
                            if (assessmentDetailProvider
                                        .assessmentResponse!
                                        .data!
                                        .instruction!
                                        .details!
                                        .attemptAllowed! !=
                                    0 &&
                                assessmentDetailProvider
                                        .assessmentResponse!
                                        .data!
                                        .instruction!
                                        .details!
                                        .attemptCount! >=
                                    assessmentDetailProvider
                                        .assessmentResponse!
                                        .data!
                                        .instruction!
                                        .details!
                                        .attemptAllowed!) {
                              Utility.showSnackBar(
                                  scaffoldContext: mContext,
                                  message: tr('maximum_attempts_reached'));
                            } else {
                              if (assessmentDetailProvider
                                          .assessmentResponse!
                                          .data!
                                          .instruction!
                                          .details!
                                          .isPassed ==
                                      1 &&
                                  assessmentDetailProvider
                                          .assessmentResponse!
                                          .data!
                                          .instruction!
                                          .details!
                                          .allowAfterPassing ==
                                      0) {
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(SnackBar(
                                  content: Text('assessment_passed').tr(),
                                ));

                                return;
                              }
                              bool isRTL = Utility().isRTL(context);

                              if (assessmentDetailProvider.assessmentResponse!
                                      .data!.instruction!.details!.passcode !=
                                  null) {
                                _showMyDialog(
                                    context,
                                    assessmentDetailProvider.assessmentResponse
                                        ?.data?.instruction?.details?.passcode,
                                    assessmentDetailProvider); //For OTP
                              } else {
                                AlertsWidget.showCustomDialog(
                                    isRTL: isRTL,
                                    context: mContext!,
                                    title: tr('confirm'),
                                    text: tr('assessment_attempt_confirm'),
                                    icon: 'assets/images/circle_alert_fill.svg',
                                    onCancelClick: () {},
                                    onOkClick: () async {
                                      await Navigator.push(
                                          mContext!,
                                          NextPageRoute(AssessmentAttemptPage(
                                            contentId: assessmentDetailProvider
                                                .assessments.programContentId,
                                            programId: assessmentDetailProvider
                                                .assessments.programId,
                                            // isInterview: widget.fromJob,
                                            isVideoTypeQuiz:
                                                assessmentDetailProvider
                                                        .assessmentResponse!
                                                        .data!
                                                        .instruction!
                                                        .details!
                                                        .quizType !=
                                                    'text',
                                            attemptAllowed:
                                                assessmentDetailProvider
                                                    .assessmentResponse
                                                    ?.data
                                                    ?.instruction
                                                    ?.details
                                                    ?.attemptAllowed,
                                          )));
                                      assessmentDetailProvider.getDetails();
                                    });
                              }
                            }
                          }
                        },
                        child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                            gradient: assessmentDetailProvider
                                        .assessmentResponse!
                                        .data!
                                        .instruction!
                                        .details!
                                        .attemptCount! >=
                                    assessmentDetailProvider
                                        .assessmentResponse!
                                        .data!
                                        .instruction!
                                        .details!
                                        .attemptAllowed!
                                ? LinearGradient(colors: [
                                    ColorConstants.GREY_3,
                                    ColorConstants.GREY_3,
                                  ])
                                : LinearGradient(colors: [
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight(),
                                  ]),
                            color: currentIndiaTime!.isAfter(startDate) &&
                                    currentIndiaTime!.isBefore(endDate)
                                ? assessmentDetailProvider
                                            .assessmentResponse!
                                            .data!
                                            .instruction!
                                            .details!
                                            .attemptCount! <=
                                        assessmentDetailProvider
                                            .assessmentResponse!
                                            .data!
                                            .instruction!
                                            .details!
                                            .attemptAllowed!
                                    ? Color(0xff0E1638)
                                    : ColorConstants.BG_GREY
                                : Color(0xff0E1638).withValues(alpha: 0.4),
                            borderRadius: BorderRadius.all(Radius.circular(5)),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.only(
                                left: 8, right: 0, top: 4, bottom: 4),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  assessmentDetailProvider
                                              .assessmentResponse!
                                              .data!
                                              .instruction!
                                              .details!
                                              .attemptCount! >
                                          0
                                      ? 'reattempt'
                                      : 'attempt',
                                  style: Styles.textExtraBold(
                                      size: 14,
                                      color: ColorConstants()
                                          .primaryForgroundColor()),
                                ).tr(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              // )
            ] else ...[
              _belowTitle(assessmentDetailProvider),
              _body(assessmentDetailProvider, isReviewAllow: isReviewAllow),
            ],
            /* if (assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!.isCertificate != 0)
              InkWell(
                onTap: () {
                  print('EventAssessmentCertificate');
                 */
            /* Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => EventAssessmentCertificate(
                            contentId: int.tryParse(assessmentDetailProvider
                                    .assessmentResponse!
                                    .data!
                                    .instruction!
                                    .details!
                                    .contentId ??
                                ''),
                            certificateId: assessmentDetailProvider
                                .assessmentResponse!
                                .data!
                                .instruction!
                                .details!
                                .certificateId,
                            htmlUrl: assessmentDetailProvider
                                .assessmentResponse!
                                .data!
                                .instruction!
                                .details!
                                .certificateHtmlUrl,
                          )));*/
            /*
                },
                child: Padding(
                  padding: const EdgeInsets.only(
                      left: 8, right: 8, top: 4, bottom: 4),
                  child: Container(
                    height: 50,
                    //width: width(context) * 0.85,
                    //margin: EdgeInsets.only(top: 10),
                    decoration: BoxDecoration(
                        border: Border.all(color: ColorConstants.PRIMARY_BLUE),
                        borderRadius: BorderRadius.circular(8)),
                    child: Center(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset('assets/images/certificate_icon.svg',
                              color: ColorConstants.PRIMARY_BLUE, height: 20),
                          SizedBox(width: 8),
                          Text(tr('view_certificate'),
                              style: Styles.bold(
                                size: 12,
                                color: ColorConstants.PRIMARY_BLUE,
                              )).tr(),
                        ],
                      ),
                    ),
                  ),
                ),
              ),*/
          ],
        ),
      ),
    );
  }

  _belowTitle(AssessmentDetailProvider assessmentDetailProvider) {
    int attempLeft = assessmentDetailProvider
            .assessmentResponse!.data!.instruction!.details!.attemptAllowed! -
        assessmentDetailProvider
            .assessmentResponse!.data!.instruction!.details!.attemptCount!;
    return Padding(
      padding: const EdgeInsets.only(left: 18, right: 18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _size(height: 30),
          Text(
            '${assessmentDetailProvider.assessmentResponse?.data!.instruction!.details!.title!}',
            style:
                Styles.bold(size: 16, color: ColorConstants().gradientRight()),
          ),
          _size(height: 15),
          Row(
            children: [
              Text('${tr('submit_before')}: ',
                  style:
                      Styles.semibold(size: 14, color: ColorConstants.BLACK)),
              Text(
                '${Utility.convertDateFromMillis(int.parse('${assessmentDetailProvider.assessmentResponse?.data!.instruction!.details!.endDate ?? ''}'), Strings.REQUIRED_DATE_DD_MMM_YYYY)}',
                style: Styles.semibold(size: 14, color: ColorConstants.BLACK),
                textDirection: ui.TextDirection.ltr,
              ),
              Spacer(),
              Text(
                '${assessmentDetailProvider.assessmentResponse?.data!.instruction!.details!.durationInMinutes} ${tr('mins')}',
                style: Styles.semibold(size: 14, color: ColorConstants.BLACK),
              )
            ],
          ),
          _size(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                '${assessmentDetailProvider.assessmentResponse?.data!.instruction!.details!.maximumMarks} ${tr('marks')} • ',
                style:
                    Styles.textExtraBold(size: 16, color: ColorConstants.BLACK),
              ),
              Text(
                assessmentDetailProvider.assessmentResponse!.data!.instruction!
                            .details!.attemptAllowed ==
                        0
                    ? tr('unlimited_attempt')
                    : '$attempLeft ${tr('attempt_available')} ',
                style:
                    Styles.textExtraBold(size: 16, color: ColorConstants.BLACK),
              )
            ],
          ),
        ],
      ),
    );
  }

  _size({double height = 20}) {
    return SizedBox(
      height: height,
    );
  }

  void _showMyDialog(BuildContext rootContext, String? passcode,
      AssessmentDetailProvider assessmentDetailProvider) {
    const focusedBorderColor = Color.fromRGBO(23, 171, 144, 1);
    const fillColor = Color.fromRGBO(243, 246, 249, 0);
    const borderColor = Color.fromRGBO(23, 171, 144, 0.4);
    final defaultPinTheme = PinTheme(
      width: 45,
      height: 45,
      textStyle: const TextStyle(
        fontSize: 20,
        color: Color.fromRGBO(30, 60, 87, 1),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: borderColor),
      ),
    );

    showDialog(
      context: rootContext,
      barrierDismissible: false,
      builder: (BuildContext context) {
        pinController.clear();
        return AlertDialog(
          title: Text(tr('enter_your_passcode')),
          icon: Icon(
            Icons.password,
            color: ColorConstants.PRIMARY_COLOR,
          ),
          content: Text(
            tr('passcode_msg_popup'),
            textAlign: TextAlign.center,
          ),
          actions: <Widget>[
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Pinput(
                  controller: pinController,
                  //focusNode: focusNode,
                  length: 6,
                  defaultPinTheme: defaultPinTheme,
                  //separatorBuilder: (index) => const SizedBox(width: 8),
                  validator: (value) {
                    return value == passcode
                        ? null
                        : '${tr('enter_valid_passcode')}';
                  },
                  hapticFeedbackType: HapticFeedbackType.lightImpact,
                  onCompleted: (pin) {
                    debugPrint('onCompleted: $pin');
                  },
                  onChanged: (value) {
                    debugPrint('onChanged: $value');
                  },
                  cursor: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(bottom: 9),
                        width: 22,
                        height: 1,
                        color: focusedBorderColor,
                      ),
                    ],
                  ),
                  focusedPinTheme: defaultPinTheme.copyWith(
                    decoration: defaultPinTheme.decoration!.copyWith(
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: focusedBorderColor),
                    ),
                  ),
                  submittedPinTheme: defaultPinTheme.copyWith(
                    decoration: defaultPinTheme.decoration!.copyWith(
                      color: fillColor,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: focusedBorderColor),
                    ),
                  ),
                  errorPinTheme: defaultPinTheme.copyBorderWith(
                    border: Border.all(color: Colors.redAccent),
                  ),
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  child: Text('Cancel'),
                  onPressed: () {
                    pinController.clear();
                    Navigator.of(context).pop(); // Closes the dialog
                  },
                ),
                TextButton(
                  child: Text('OK'),
                  onPressed: () async {
                    // Add any action here
                    if (pinController.text.length == 6) {
                      if (passcode == pinController.text) {
                        pinController.clear();
                        Navigator.of(context).pop();
                        await Navigator.push(
                            mContext!,
                            NextPageRoute(AssessmentAttemptPage(
                              contentId: assessmentDetailProvider
                                  .assessments.programContentId,
                              programId: assessmentDetailProvider
                                  .assessments.programId,
                              // isInterview: widget.fromJob,
                              isVideoTypeQuiz: assessmentDetailProvider
                                      .assessmentResponse!
                                      .data!
                                      .instruction!
                                      .details!
                                      .quizType !=
                                  'text',
                              attemptAllowed: assessmentDetailProvider
                                  .assessmentResponse
                                  ?.data
                                  ?.instruction
                                  ?.details
                                  ?.attemptAllowed,
                            )));
                        assessmentDetailProvider.getDetails();
                      } else {
                        /*ScaffoldMessenger.of(rootContext).showSnackBar(
                          SnackBar(
                            content: Text(tr('enter_valid_otp')),
                            duration: Duration(seconds: 5), // Set to a reasonable duration
                            action: SnackBarAction(
                              label: 'Dismiss', // Label for the action
                              onPressed: () {
                                // Dismiss the SnackBar when the user presses the button
                                ScaffoldMessenger.of(rootContext).hideCurrentSnackBar();
                              },
                            ),
                          ),
                        );*/
                      }
                    } else {
                      // Show a new SnackBar
                      /*ScaffoldMessenger.of(rootContext).showSnackBar(
                        SnackBar(
                          content: Text(tr('enter_otp')),
                          duration: Duration(seconds: 5), // Set to a reasonable duration
                          action: SnackBarAction(
                            label: 'Dismiss', // Label for the action
                            onPressed: () {
                              // Dismiss the SnackBar when the user presses the button
                              ScaffoldMessenger.of(rootContext).hideCurrentSnackBar();
                            },
                          ),
                        ),
                      );*/
                    }
                  },
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  _body(AssessmentDetailProvider assessmentDetailProvider,
      {required bool isReviewAllow}) {
    return Container(
      margin: EdgeInsets.only(top: 10),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(25), topLeft: Radius.circular(25))),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'instructions',
                    style: Styles.textExtraBold(
                        size: 18, color: ColorConstants().gradientRight()),
                  ).tr(),
                  Wrap(
                    children: List.generate(
                        assessmentDetailProvider.assessmentResponse!.data!
                            .instruction!.statement!.length,
                        (index) => Html(
                              data: assessmentDetailProvider.assessmentResponse!
                                  .data!.instruction!.statement![index],
                              style: {
                                "p": Style(
                                  fontFamily: 'Nunito_Regular',
                                )
                              },
                            )),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                ],
              ),
              _size(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (isReviewAllow)
                    Expanded(
                      child: TapWidget(
                        onTap: () {
                          // Navigator.push(
                          //     mContext!,
                          //     NextPageRoute(AssessmentReviewPage(
                          //       contentId: assessmentDetailProvider
                          //           .assessments.programContentId,
                          //       programId: assessmentDetailProvider
                          //           .assessments.programId,
                          //     )));

                          if (assessmentDetailProvider.assessmentResponse!.data!
                                  .instruction!.details!.quizType !=
                              'text')
                            Navigator.push(
                                mContext!,
                                NextPageRoute(AssessmentAttemptPage(
                                  contentId: assessmentDetailProvider
                                      .assessments.programContentId,
                                  programId: assessmentDetailProvider
                                      .assessments.programId,
                                  // isInterview: widget.fromJob,
                                  isVideoTypeQuiz: assessmentDetailProvider
                                          .assessmentResponse!
                                          .data!
                                          .instruction!
                                          .details!
                                          .quizType !=
                                      'text',
                                  isReview: true,
                                  attemptAllowed: assessmentDetailProvider
                                      .assessmentResponse
                                      ?.data
                                      ?.instruction
                                      ?.details
                                      ?.attemptAllowed,
                                )));
                          else {
                            Navigator.push(
                                mContext!,
                                NextPageRoute(AssessmentReviewPage(
                                  contentId: assessmentDetailProvider
                                      .assessments.programContentId,
                                  programId: assessmentDetailProvider
                                      .assessments.programId,
                                )));
                          }
                        },
                        child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(colors: [
                              ColorConstants().gradientLeft(),
                              ColorConstants().gradientRight(),
                            ]),
                            color: ColorConstants().gradientRight(),
                            borderRadius: BorderRadius.all(Radius.circular(5)),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.only(
                                left: 8, right: 8, top: 4, bottom: 4),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'review',
                                  style: Styles.textExtraBold(
                                      size: 14,
                                      color: ColorConstants()
                                          .primaryForgroundColor()),
                                ).tr(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  if (assessmentDetailProvider.assessmentResponse!.data!
                              .instruction!.details!.attemptAllowed! ==
                          0
                      ? true
                      : assessmentDetailProvider.assessmentResponse!.data!
                              .instruction!.details!.attemptCount! <=
                          assessmentDetailProvider.assessmentResponse!.data!
                              .instruction!.details!.attemptAllowed!)
                    SizedBox(width: 15.0),
                  if (assessmentDetailProvider.assessmentResponse!.data!
                              .instruction!.details!.attemptAllowed! ==
                          0
                      ? true
                      : assessmentDetailProvider.assessmentResponse!.data!
                              .instruction!.details!.attemptCount! <=
                          assessmentDetailProvider.assessmentResponse!.data!
                              .instruction!.details!.attemptAllowed!)
                    Expanded(
                      child: TapWidget(
                        onTap: () {
                          DateTime now = currentIndiaTime!;
                          switch (Utility.classStatus(
                              assessmentDetailProvider.assessmentResponse!.data!
                                  .instruction!.details!.startDate!,
                              assessmentDetailProvider.assessmentResponse!.data!
                                  .instruction!.details!.endDate!,
                              now)) {
                            case 1:
                              AlertsWidget.showCustomDialog(
                                  context: context,
                                  title: tr('assessment_not_ready_submission'),
                                  text: "",
                                  icon: 'assets/images/circle_alert_fill.svg',
                                  showCancel: false,
                                  oKText: '${tr('ok')}',
                                  onOkClick: () async {});
                              return;

                            case 2:
                              AlertsWidget.showCustomDialog(
                                  context: context,
                                  title: tr('due_data_passed'),
                                  text: "",
                                  icon: 'assets/images/circle_alert_fill.svg',
                                  showCancel: false,
                                  oKText: '${tr('ok')}',
                                  onOkClick: () async {});
                              return;
                          }

                          if (assessmentDetailProvider.assessmentResponse!.data!
                                      .instruction!.details!.attemptAllowed! !=
                                  0 &&
                              assessmentDetailProvider.assessmentResponse!.data!
                                      .instruction!.details!.attemptCount! >=
                                  assessmentDetailProvider
                                      .assessmentResponse!
                                      .data!
                                      .instruction!
                                      .details!
                                      .attemptAllowed!) {
                            Utility.showSnackBar(
                                scaffoldContext: mContext,
                                message: tr('maximum_attempts_reached'));
                          } else {
                            if (assessmentDetailProvider.assessmentResponse!
                                        .data!.instruction!.details!.isPassed ==
                                    1 &&
                                assessmentDetailProvider
                                        .assessmentResponse!
                                        .data!
                                        .instruction!
                                        .details!
                                        .allowAfterPassing ==
                                    0) {
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(SnackBar(
                                content: Text('assessment_passed').tr(),
                              ));

                              return;
                            }
                            bool isRTL = Utility().isRTL(context);

                            if (assessmentDetailProvider.assessmentResponse!
                                    .data!.instruction!.details!.passcode !=
                                null) {
                              _showMyDialog(
                                  context,
                                  assessmentDetailProvider.assessmentResponse
                                      ?.data?.instruction?.details?.passcode,
                                  assessmentDetailProvider); //For OTP
                            } else {
                              AlertsWidget.showCustomDialog(
                                  isRTL: isRTL,
                                  context: mContext!,
                                  title: tr('confirm'),
                                  text: tr('assessment_attempt_confirm'),
                                  icon: 'assets/images/circle_alert_fill.svg',
                                  onCancelClick: () {},
                                  onOkClick: () async {
                                    await Navigator.push(
                                        mContext!,
                                        NextPageRoute(AssessmentAttemptPage(
                                          contentId: assessmentDetailProvider
                                              .assessments.programContentId,
                                          programId: assessmentDetailProvider
                                              .assessments.programId,
                                          isVideoTypeQuiz:
                                              assessmentDetailProvider
                                                      .assessmentResponse!
                                                      .data!
                                                      .instruction!
                                                      .details!
                                                      .quizType !=
                                                  'text',
                                          attemptAllowed:
                                              assessmentDetailProvider
                                                  .assessmentResponse
                                                  ?.data
                                                  ?.instruction
                                                  ?.details
                                                  ?.attemptAllowed,
                                        )));
                                    assessmentDetailProvider.getDetails();
                                  });
                            }
                          }
                        },
                        child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                            gradient: (assessmentDetailProvider
                                                .assessmentResponse!
                                                .data!
                                                .instruction!
                                                .details!
                                                .isPassed ==
                                            1 &&
                                        assessmentDetailProvider
                                                .assessmentResponse!
                                                .data!
                                                .instruction!
                                                .details!
                                                .allowAfterPassing ==
                                            0) ||
                                    (assessmentDetailProvider
                                                .assessmentResponse!
                                                .data!
                                                .instruction!
                                                .details!
                                                .attemptCount! >=
                                            assessmentDetailProvider
                                                .assessmentResponse!
                                                .data!
                                                .instruction!
                                                .details!
                                                .attemptAllowed! &&
                                        assessmentDetailProvider
                                                .assessmentResponse!
                                                .data!
                                                .instruction!
                                                .details!
                                                .attemptAllowed !=
                                            0)
                                ? LinearGradient(colors: [
                                    ColorConstants.GREY_3,
                                    ColorConstants.GREY_3,
                                  ])
                                : LinearGradient(colors: [
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight(),
                                  ]),
                            color: (assessmentDetailProvider
                                        .assessmentResponse!
                                        .data!
                                        .instruction!
                                        .details!
                                        .attemptCount! >=
                                    assessmentDetailProvider
                                        .assessmentResponse!
                                        .data!
                                        .instruction!
                                        .details!
                                        .attemptAllowed!)
                                ? ColorConstants.GREY_3
                                : ColorConstants().gradientRight(),
                            borderRadius: BorderRadius.all(Radius.circular(5)),
                            // border: Border.all(color: Colors.black),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.only(
                                left: 8, right: 8, top: 4, bottom: 4),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  assessmentDetailProvider
                                              .assessmentResponse!
                                              .data!
                                              .instruction!
                                              .details!
                                              .attemptCount! >
                                          0
                                      ? 'reattempt'
                                      : 'attempt',
                                  style: Styles.textExtraBold(
                                      size: 14,
                                      color: ColorConstants()
                                          .primaryForgroundColor()),
                                ).tr(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              _size(height: 17),
              if (assessmentDetailProvider.assessmentResponse!.data!
                      .instruction!.details!.submittedOnDate! !=
                  0)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'last_attempt',
                          style: Styles.textRegular(
                              size: 16, color: ColorConstants.BLACK),
                        ).tr(),
                        Text(
                          '${assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!.submittedOnDate != null ? Utility.convertDateFromMillis(assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!.submittedOnDate!, Strings.REQUIRED_DATE_DD_MMM_YYYY_HH_MM__SS) : ''}',
                          style: Styles.textRegular(
                              size: 12, color: ColorConstants.GREY_4),
                          textDirection: ui.TextDirection.ltr,
                        ),
                      ],
                    ),
                    assessmentDetailProvider.assessmentResponse!.data!
                                .instruction!.details!.displayScorecard !=
                            1
                        ? SizedBox()
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                  '${tr('score')}: ${assessmentDetailProvider.assessmentResponse!.data!.instruction!.details!.score.toStringAsFixed(2)}'),
                            ],
                          ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}

extension on String {
  String capital() {
    return this[0].toUpperCase() + this.substring(1);
  }
}
