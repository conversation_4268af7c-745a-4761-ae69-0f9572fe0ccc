import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:masterg/data/models/response/home_response/training_module_response.dart';
import 'package:masterg/pages/training_pages/program_content/widgets/text_view.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:page_transition/page_transition.dart';
import 'package:path/path.dart' as path;
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:path_provider/path_provider.dart';

import '../../../../utils/Log.dart';
import '../../../../utils/utility.dart';
import 'learning_short_video_player.dart';
import 'learning_shorts_content_player.dart';

class LearningShortsView extends StatelessWidget {
  final LearningShots learningShorts;
  const LearningShortsView({super.key, required this.learningShorts});

  @override
  Widget build(BuildContext context) {
    return learningShorts.contentType?.toLowerCase() == 'notes'
        ? Container(
            color: ColorConstants.WHITE,
            width: double.infinity,
            height: MediaQuery.of(context).size.height * 0.24,
            margin: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            child: Stack(
              children: [
                Image.asset(
                  'assets/images/note_bg.png',
                  width: width(context),
                  fit: BoxFit.cover,
                ),
                Positioned(
                  bottom: 10,
                  left: 0,
                  right: 0,
                  child: InkWell(
                    onTap: () {
                      if(learningShorts.url.toString().toLowerCase().contains('zip')){
                        downloadFile(context: context, url: learningShorts.url, filename: learningShorts.contentType!+learningShorts.createdAt.toString()+'.zip');
                      }else if(learningShorts.url.toString().toLowerCase().contains('rar')){
                        downloadFile(context: context, url: learningShorts.url, filename: learningShorts.contentType!+learningShorts.createdAt.toString()+'.rar');
                      } else {
                        openNotes(context, isNoteView: true);
                      }
                    },
                    child: Container(
                        width: width(context),
                        height: 38,
                        margin: const EdgeInsets.symmetric(
                            horizontal: 18, vertical: 4),
                        decoration: BoxDecoration(
                            gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                  ColorConstants().gradientLeft(),
                                  ColorConstants().gradientRight()
                                ]),
                            borderRadius: BorderRadius.circular(8)),
                        child: Center(
                          child: Text(
                            learningShorts.url.toString().toLowerCase().contains('zip') ||
                                learningShorts.url.toString().toLowerCase().contains('rar') ? 'download_notes' : 'view_note',
                            style: Styles.regular(
                                size: 14,
                                color:
                                    ColorConstants().primaryForgroundColor()),
                            textAlign: TextAlign.center,
                          ).tr(),
                        )),
                  ),
                )
              ],
            ),
          )
        : learningShorts.contentType?.toLowerCase() == 'text'
            ? TextView(learningShorts: learningShorts)
            : LearningShortVideoPlayer(
                learningShorts: learningShorts,
              );
  }

  openNotes(BuildContext context, {required bool isNoteView}) {
    log('learning shoorts ${learningShorts.url}');
    Navigator.push(
        context,
        PageTransition(
            type: PageTransitionType.fade,
            child: LearningShortsContentPlayer(
              isNoteView: isNoteView,
              learningShorts: learningShorts,
            )));
  }


  Future<void> downloadFile({
    required BuildContext context,
    required String? url,
    required String? filename,}) async {
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    late AndroidDeviceInfo android;
    try {
      android = await plugin.androidInfo;
    } catch (e) {
      Log.v("exception file download $e");
    }
    // return;
    String localPath;

    final status = await Permission.storage.request();
    if (Platform.isIOS ||
        status.isGranted ||
        android.version.sdkInt >= 33 ||
        await Permission.storage.request().isGranted) {
      //  final externalDir = await getExternalStorageDirectory();
      final status = await Permission.storage.status;

      if (Platform.isAndroid) {
        localPath = '/sdcard/download/';
      } else {
        localPath = (await getApplicationDocumentsDirectory()).path;
      }
      //final file = File("$localPath/${url!.split('/').last}");
      final fileName = Uri.decodeComponent(Uri.parse(url!).pathSegments.last);
      final file = File(path.join(localPath, fileName));
      if (await file.exists()) {
        Utility.showSnackBar(scaffoldContext: context, message: '${tr('file_already_exists')}');

      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('downloading_start').tr(),
        ));

        final id = await FlutterDownloader.enqueue(
          url: url,
          savedDir: localPath,
          showNotification: true,
          saveInPublicStorage: true,
          openFileFromNotification: true,
        ).then((value) async {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('file_downloaded_successfully').tr(),
          ));
          //OpenFilex.open("$localPath/${url.split('/').last}");
        });
      }
    } else {
      launchUrl(Uri.parse(url!), mode: LaunchMode.externalApplication);
      Log.v('Permission Denied');
    }
    return;
  }


}
