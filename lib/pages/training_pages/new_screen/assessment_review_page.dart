import 'dart:async';
import 'dart:isolate';
import 'dart:ui';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_html_table/flutter_html_table.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/test_review_response.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/TapWidget.dart';
import 'package:masterg/pages/training_pages/new_screen/widget/question_option_web_view.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';

class AssessmentReviewPage extends StatefulWidget {
  final int? contentId;
  final int? programId;

  AssessmentReviewPage({this.contentId, required this.programId});

  @override
  _AssessmentReviewPageState createState() => _AssessmentReviewPageState();
  var _pageViewController = PageController();
  int? currentSection = 0;
  var _currentQuestion = 0;
  int? _currentQuestionId;
  List<TestReviewBean> _list = [];
  ScrollController? _questionController;
}

class IdMapper {
  int? questionId;
  String? color;
  int? timeTaken;

  IdMapper({this.questionId, this.color, this.timeTaken});

  IdMapper.fromJson(Map<String, dynamic> json) {
    questionId = json['questionId'];
    color = json['color'];
    timeTaken = json['timeTaken'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['questionId'] = this.questionId;
    data['color'] = this.color;
    data['timeTaken'] = this.timeTaken;
    return data;
  }
}

class _AssessmentReviewPageState extends State<AssessmentReviewPage> {
  //final GlobalKey<ScaffoldState> _key = new GlobalKey<ScaffoldState>();
  final _key = GlobalKey<ScaffoldMessengerState>();
  var _isLoading = false;
  var _scaffoldContext;
  late HomeBloc _authBloc;
  bool showSolution = false;
  late InAppWebViewController _webViewController;

  void _handleAttemptTestResponse(ReviewTestState state) {
    try {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          this.setState(() {
            _isLoading = true;
          });
          break;
        case ApiStatus.SUCCESS:
          if (state.response!.data != null) {
            widget._list.clear();
            for (int i = 0;
                i < state.response!.data!.assessmentReview!.questions!.length;
                i++) {
              widget._list.add(
                TestReviewBean(
                    question:
                        state.response!.data!.assessmentReview!.questions![i],
                    id: state.response!.data!.assessmentReview!.questions![i]
                        .questionId,
                    title: state.response!.data!.assessmentReview!.questions![i]
                        .question),
              );
            }

            if (widget._list.length > 0) {
              widget._currentQuestionId =
                  widget._list.first.question!.questionId;
            }
          }
          this.setState(() {
            _isLoading = false;
          });
          break;
        case ApiStatus.ERROR:
          this.setState(() {
            _isLoading = false;
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    } catch (e) {}
  }

  @override
  void initState() {
    super.initState();
    _downloadListener();
    widget._questionController = ScrollController();
  }

  @override
  Widget build(BuildContext context) {
    Application(context);
    _authBloc = BlocProvider.of<HomeBloc>(context);
    return BlocManager(
      initState: (context) {
        if (widget._list.length == 0)
          _authBloc.add(
            ReviewTestEvent(
              request: '${widget.contentId}?program_id=${widget.programId}',
            ),
          );
      },
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is ReviewTestState) _handleAttemptTestResponse(state);
        },
        child: Builder(builder: (_context) {
          _scaffoldContext = _context;
          return WillPopScope(
            onWillPop: () async => false,
            child: Scaffold(
              appBar: AppBar(
                leading: IconButton(
                  icon: Icon(Icons.arrow_back, color: Colors.black),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                title: Text(
                  'review',
                  style: TextStyle(color: Colors.black),
                ).tr(),
                backgroundColor: Colors.white,
                elevation: 0,
              ),
              backgroundColor: ColorConstants.WHITE,
              key: _key,
              bottomNavigationBar: widget._list.length == 0
                  ? SizedBox()
                  : BottomAppBar(
                      elevation: 10,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 0, vertical: 5),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            widget._currentQuestion == 0
                                ? SizedBox(
                                    width: 100,
                                  )
                                : TapWidget(
                                    onTap: () {
                                      widget._pageViewController.previousPage(
                                          duration: Duration(milliseconds: 200),
                                          curve: Curves.ease);
                                    },
                                    child: Container(
                                      width: 100,
                                      padding:
                                          const EdgeInsets.only(left: 20.0),
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                      child: Row(
                                        children: [
                                          SvgPicture.asset(
                                            'assets/images/prev.svg',
                                            width: 15,
                                            height: 15,
                                            allowDrawingOutsideViewBox: true,
                                          ),
                                          const SizedBox(
                                            width: 5,
                                          ),
                                          Text(
                                            'previous',
                                            style: Styles.textBold(
                                                size: 16, color: Colors.black),
                                          ).tr(),
                                        ],
                                      ),
                                    ),
                                  ),
                            Container(
                              width: 100,
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    "${(widget._currentQuestion + 1).toString() + "/" + widget._list.length.toString()}",
                                    style: Styles.textBold(
                                        size: 16, color: Colors.black),
                                  ),
                                ],
                              ),
                            ),
                            TapWidget(
                              onTap: () {
                                widget._pageViewController.nextPage(
                                    duration: Duration(milliseconds: 200),
                                    curve: Curves.ease);
                              },
                              child: Container(
                                width: 100,
                                decoration: BoxDecoration(),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      ((widget._list.length - 1) ==
                                              widget._currentQuestion)
                                          ? ""
                                          : tr('next'),
                                      style: Styles.textBold(
                                          size: 16, color: Colors.black),
                                    ),
                                    const SizedBox(
                                      width: 5,
                                    ),
                                    if ((widget._list.length - 1) !=
                                        widget._currentQuestion)
                                      SvgPicture.asset(
                                        'assets/images/next.svg',
                                        width: 15,
                                        height: 15,
                                        allowDrawingOutsideViewBox: true,
                                      ),
                                  ],
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
              body: SafeArea(
                child: ScreenWithLoader(
                  body: widget._list.length == 0
                      ? Column(
                          children: [
                            //_heading(),
                            Center(
                              child:
                                  Text(_isLoading ? 'please_wait' : 'no_data')
                                      .tr(),
                            ),
                          ],
                        )
                      : _content(),
                  isLoading: _isLoading,
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _content() {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12)),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 19),
                    child: Divider(),
                  ),
                  _pageView(),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  _pageView() {
    return Expanded(
      child: Container(
        child: PageView.builder(
          itemBuilder: (context, index) {
            return _pageItem(widget._list[index]);
          },
          onPageChanged: (pageNumber) {
            setState(() {
              widget.currentSection = widget._list[pageNumber].id;
              widget._currentQuestionId =
                  widget._list[pageNumber].question!.questionId;
              widget._currentQuestion = pageNumber;
              showSolution = false;
              int questionsLength = widget._list.length;
              Utility.waitForMili(200).then((value) {
                if (widget._currentQuestion + 2 >= questionsLength * .6) {
                  if (widget._questionController!.position.pixels !=
                      widget._questionController!.position.maxScrollExtent) {
                    widget._questionController!.jumpTo(
                        ((widget._currentQuestion + 2) * 30).toDouble());
                  }
                } else if ((widget._currentQuestion + 2) <=
                    questionsLength * .3) {
                  if (widget._questionController!.position.pixels != 0) {
                    widget._questionController!.jumpTo(0);
                  }
                }
              });
            });
          },
          controller: widget._pageViewController,
          itemCount: widget._list.length,
          physics: NeverScrollableScrollPhysics(),
        ),
      ),
    );
  }

  _questionCount() {
    return widget._list.length == 0
        ? SizedBox()
        : Padding(
            padding: const EdgeInsets.symmetric(horizontal: 19),
            child: Container(
              height: 60,
              width: MediaQuery.of(_scaffoldContext).size.width,
              child: SingleChildScrollView(
                physics: ClampingScrollPhysics(),
                scrollDirection: Axis.horizontal,
                controller: widget._questionController,
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: ClampingScrollPhysics(),
                  itemBuilder: (context, index) {
                    Questions? question = widget._list[index].question;
                    return TapWidget(
                      onTap: () {
                        if (widget._currentQuestionId ==
                            widget._list[index].question!.questionId) {
                          return;
                        }
                        for (int i = 0; i < widget._list.length; i++) {
                          if (widget._list[i].question!.questionId ==
                              widget._list[index].question!.questionId) {
                            widget._currentQuestionId =
                                widget._list[index].question!.questionId;
                            widget._currentQuestion = i;
                            widget._pageViewController.animateToPage(i,
                                duration: Duration(milliseconds: 100),
                                curve: Curves.ease);
                            break;
                          }
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(right: 32),
                        child: Container(
                          width: 35,
                          height: 35,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                  color: Color.fromRGBO(0, 0, 0, 0.05),
                                  offset: Offset(0, 8),
                                  blurRadius: 16)
                            ],
                            color: Colors.grey[300],
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            "${index + 1}",
                            style: index == widget._currentQuestion
                                ? Styles.textBold()
                                : Styles.textLight(),
                          ),
                        ),
                      ),
                    );
                  },
                  scrollDirection: Axis.horizontal,
                  itemCount: widget._list.length,
                ),
              ),
            ),
          );
  }

  _pageItem(TestReviewBean testAttemptBean) {
    return Container(
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Padding(
            //   padding: const EdgeInsets.symmetric(horizontal: 20),
            //   child: Text(
            //     testAttemptBean.question!.question ?? "",
            //     style: Styles.textRegular(size: 16),
            //   ),
            // ),

            //singh Add New 28/02-2025
            testAttemptBean.question?.questionLabel?.image != null &&
                testAttemptBean.question?.questionLabel?.image != '' ? Padding(
              padding: const EdgeInsets.all(8.0),
              child: Image.network('${testAttemptBean.question?.questionLabel?.image}',
                height: 200,
                fit: BoxFit.fitWidth,
                width: MediaQuery.of(context).size.width,
              ),
            ):SizedBox(),
            testAttemptBean.question!.questionLabel?.description != null &&
                testAttemptBean.question!.questionLabel?.description != '' ? Padding(
              padding: const EdgeInsets.all(8.0),
              child: Utility().isHtml(testAttemptBean.question!.questionLabel?.description ?? "") ?
              Utility().isMathEquation(testAttemptBean.question!.questionLabel?.description ?? '') ?  SizedBox(
                height: 150,
                child: InAppWebView(
                  initialData: InAppWebViewInitialData(
                    data: '${testAttemptBean.question!.questionLabel?.description}',
                    mimeType: "text/html",
                    encoding: "utf-8",
                  ),

                  initialOptions: InAppWebViewGroupOptions(
                    crossPlatform: InAppWebViewOptions(
                      javaScriptEnabled: true,
                      supportZoom: true,
                    ),
                  ),
                  onWebViewCreated: (controller) {
                    _webViewController = controller;
                  },
                  onLoadStart: (controller, url) {
                    _webViewController.evaluateJavascript(
                      source: """
      document.body.style.zoom = '2.5';
      """,
                    );
                  },
                ),
              ) : Html(data: """${testAttemptBean.question!.questionLabel?.description}""",
                extensions: [TableHtmlExtension(),],
                style: {
                  "table": Style(
                    //border: Border.all(color: Colors.grey, width: 0.5),
                    padding: HtmlPaddings.all(0),
                  ),
                  "td": Style(
                    border: Border.all(color: Colors.grey, width: 0.5),
                    padding: HtmlPaddings.only(left: 20, right: 20, top: 4, bottom: 4),   // small cell padding
                  ),
                },
                // customRenders: {
                //   tableMatcher(): tableRender(),
                // },
              ):Text('${testAttemptBean.question!.questionLabel?.description}'),
            ):SizedBox(),

            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Utility().isHtml(testAttemptBean.question!.question ?? "") ?
              Utility().isMathEquation(testAttemptBean.question!.question ?? '') ?  SizedBox(
                height: 150,
                child: InAppWebView(
                  initialData: InAppWebViewInitialData(
                    data: '${testAttemptBean.question!.question}',
                    mimeType: "text/html",
                    encoding: "utf-8",
                  ),

                  initialOptions: InAppWebViewGroupOptions(
                    crossPlatform: InAppWebViewOptions(
                      javaScriptEnabled: true,
                      supportZoom: true,
                    ),
                  ),
                  onWebViewCreated: (controller) {
                    _webViewController = controller;
                  },
                  onLoadStart: (controller, url) {
                    _webViewController.evaluateJavascript(
                      source: """
      document.body.style.zoom = '2.5';
      """,
                    );
                  },
                ),
              ) : Html(data: """${testAttemptBean.question!.question}""",
                extensions: [TableHtmlExtension(),],
                style: {
                  "table": Style(
                    //border: Border.all(color: Colors.grey, width: 0.5),
                    padding: HtmlPaddings.all(0),
                  ),
                  "td": Style(
                    border: Border.all(color: Colors.grey, width: 0.5),
                    padding: HtmlPaddings.only(left: 20, right: 20, top: 4, bottom: 4),   // small cell padding
                  ),
                },
                // customRenders: {
                //   tableMatcher(): tableRender(),
                // },
              )
                  : Text(
                testAttemptBean.question!.question ?? "",
                style: Styles.textExtraBold(size: 16),
              ),
            ),
            _size(height: 10),
            if (testAttemptBean.question!.questionImage != null)
              for (int i = 0;
                  i < testAttemptBean.question!.questionImage!.length;
                  i++)
                if (testAttemptBean.question!.questionImage![i]
                        .toString()
                        .contains('.mp4') ||
                    testAttemptBean.question!.questionImage![i]
                        .toString().contains('.mp3') ||
                    testAttemptBean.question!.questionImage![i].toString().contains('.ogg') ||
                    testAttemptBean.question!.questionImage![i].toString().contains('.wav'))
    Container(
                    height: testAttemptBean.question!.questionImage![i].toString().contains('.mp3') ||
    testAttemptBean.question!.questionImage![i].toString().contains('.ogg') ||
    testAttemptBean.question!.questionImage![i].toString().contains('.wav') ? 100: 200,
                    width: double.infinity,
                    alignment: Alignment.center,
                    child: Center(
                      child: InAppWebView(
                          initialOptions: InAppWebViewGroupOptions(
                              crossPlatform: InAppWebViewOptions(
                                mediaPlaybackRequiresUserGesture: true,
                                useShouldOverrideUrlLoading: true,
                              ),
                              ios: IOSInAppWebViewOptions(
                                  allowsInlineMediaPlayback: true,
                                  allowsLinkPreview: false)),
                          initialUrlRequest: URLRequest(
                              url: WebUri(testAttemptBean.question!.questionImage![i])),

                        onLoadStop: (controller, url) {
                          // Inject JavaScript to mute all video and audio elements
                          controller.evaluateJavascript(source: """
            document.querySelectorAll('video, audio').forEach(media => {
            
              document.body.style.backgroundColor = "#FFFFFF";
            });
          """);
                        },
                      ),
                    ),
                  )
                else
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Image.network(
                      testAttemptBean.question!.questionImage![i],
                      height: 200,
                      fit: BoxFit.fitWidth,
                      width: MediaQuery.of(context).size.width,
                    ),
                  ),
            _size(height: 10),
            _solutionType(testAttemptBean.question!.questionTypeId.toString(), testAttemptBean),
            _size(height: 10),
          ],
        ),
      ),
    );
  }

  _size({double height = 10}) {
    return SizedBox(
      height: height,
    );
  }

  _questionNumber(TestReviewBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Text(
            "${tr('q')}${(widget._currentQuestion + 1).toString().padLeft(2, "0")}",
            style: Styles.textBold(size: 18),
          ),
          Spacer(),
        ],
      ),
    );
  }

  String getTime({required double time}) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    return "${twoDigits((time / 60).truncate())}:${twoDigits((time % 60).truncate())}";
  }

  _solutionType(String type, TestReviewBean testAttemptBean) {
    print('type-----${type}');
    switch (type) {
      case "1":
        return _multiChoose(testAttemptBean); //MULTIPLE_CHOICE

      case "2":
        return _multiChooseMRQ(testAttemptBean); //SINGLE_INTEGER

      case "3":
        return _multiChoose(testAttemptBean); //MULTIPLE_RESPONSE

      case "4":
      //return _chooseOne(testAttemptBean); //FILL_IN_THE_BLANK

      case "5":
      //return _chooseOne(testAttemptBean); //TRUE_FALSE

      case "6":
      //  return _subjective(testAttemptBean); //SUBJECTIVE
      case "9":
        return _matchingAnswer(testAttemptBean); //DRAGON DROP MATCH ANSWER 24-Oct-2024

      case "10":
        return _fillInTheBlanks(testAttemptBean, type); //FILL IN THE BLANKS

      case "11":
        return _subjectiveText(testAttemptBean, type);

      case "12":
        return _shortAnswer(testAttemptBean, type);

      case "13":
        return _multipleMCQShortAnswer(testAttemptBean, type);

      default:
        return Container(); //MATCHING

    }
  }

  Future download2(String url, String savePath) async {
    try {
      _key.currentState!.showSnackBar(
        SnackBar(
          content: Text(
            'downloading_start',
            style: Styles.boldWhite(),
          ).tr(),
          backgroundColor: ColorConstants.BLACK,
          duration: Duration(seconds: 2),
        ),
      );
      final taskId = await FlutterDownloader.enqueue(
        url: url,
        savedDir: savePath,
        showNotification: true,
        openFileFromNotification: true,
      );
    } catch (e) {
      Log.v(e);
    }
  }

  static void downloadCallback(String id, DownloadTaskStatus status, int progress) {
    final SendPort send =
        IsolateNameServer.lookupPortByName('downloader_send_port')!;
    send.send([id, status, progress]);
  }

  // static void downloadCallback(
  //     String id, int status, int progress) {
  //   final SendPort send =
  //   IsolateNameServer.lookupPortByName('downloader_send_port')!;
  //   send.send([id, status, progress]);
  // }

  ReceivePort _port = ReceivePort();

  _downloadListener() {
    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) {
      String? id = data[0];
      DownloadTaskStatus? status = data[1];
      int? progress = data[2];
      if (status.toString() == "DownloadTaskStatus(3)" &&
          progress == 100 &&
          id != null) {
        String query = "SELECT * FROM task WHERE task_id='" + id + "'";
        var tasks = FlutterDownloader.loadTasksWithRawQuery(query: query);
        //if the task exists, open it
        FlutterDownloader.open(taskId: id);
      }
    });
    // FlutterDownloader.registerCallback(downloadCallback);
  }

  _options(TestReviewBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          Column(
            children: List.generate(
              widget._list[widget._currentQuestion].question!.questionOptions!
                  .length,
              (index) {
                Color borderColor;
                if (widget._list[widget._currentQuestion].question!
                        .questionOptions![index].optionId ==
                    int.parse(widget._list[widget._currentQuestion].question!
                        .correctOptions!.first)) {
                  borderColor = Color(0xff66bb6a);
                } else if (widget._list[widget._currentQuestion].question!
                            .questionOptions![index].optionId !=
                        int.parse(widget._list[widget._currentQuestion]
                            .question!.correctOptions!.first) &&
                    widget._list[widget._currentQuestion].question!
                            .questionOptions![index].userAnswer ==
                        1) {
                  borderColor = Colors.red;
                } else {
                  borderColor = Colors.grey;
                }
                return Column(
                  children: [
                    Container(
                      width: MediaQuery.of(_scaffoldContext).size.width,
                      height: 60,
                      child: Stack(
                        children: [
                          Positioned(
                            bottom: 0,
                            left: 0,
                            child: Card(
                              elevation: 5,
                              margin: const EdgeInsets.only(right: 20),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                                side:
                                    BorderSide(color: borderColor, width: 1.5),
                              ),
                              child: Container(
                                width:
                                    MediaQuery.of(_scaffoldContext).size.width -
                                        40,
                                height: 55,
                                alignment: Alignment.centerLeft,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 15),
                                child: Text(
                                  widget
                                      ._list[widget._currentQuestion]
                                      .question!
                                      .questionOptions![index]
                                      .optionStatement!,
                                  style: Styles.textRegular(size: 12),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            top: 15,
                            left: 30,
                            child: widget
                                        ._list[widget._currentQuestion]
                                        .question!
                                        .questionOptions![index]
                                        .userAnswer ==
                                    1
                                ? Container(
                                    color: Colors.grey[300],
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 3.0, horizontal: 8),
                                      child: Text(
                                        "Correct Answer",
                                        style: Styles.textBold(size: 10),
                                      ),
                                    ),
                                  )
                                : SizedBox(),
                          )
                        ],
                      ),
                    ),
                    _size(),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Multi-choice / MRQ review widget
  Widget _multiChooseMRQ(TestReviewBean testAttemptBean) {
    final question   = widget._list[widget._currentQuestion].question!;
    final options    = question.questionOptions!;

    final Set<int> correctOptionIds = {
      for (final raw in (question.correctOptions ?? []))
        int.tryParse(raw.replaceAll(RegExp(r'[^0-9]'), '')) ?? -1,
    };

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 15),
            child: Text(
              'question_type',
              style: Styles.textRegular(size: 12, color: Colors.grey),
            ).tr(),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              testAttemptBean.question!.questionType ?? '',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          _size(height: 20),

          // ----- options -----
          Column(
            children: List.generate(options.length, (index) {
              final option      = options[index];
              final bool isCorrect  = correctOptionIds.contains(option.optionId);
              final bool isSelected = option.userAnswer == 1;

              //  colours
              late Color bgColor, txtColor, borderColor;
              if (isCorrect && isSelected) {
                bgColor = Colors.green;
                borderColor = Colors.green;
                txtColor = Colors.white;
              } else if (isCorrect && !isSelected) {
                bgColor = Colors.white;
                borderColor = Colors.green;
                txtColor = Colors.green;
              } else if (!isCorrect && isSelected) {
                bgColor = Colors.white;
                borderColor = Colors.red;
                txtColor = Colors.red;
              } else {
                bgColor = Colors.white;
                borderColor = Colors.grey;
                txtColor = Colors.black;
              }
              final String img = option.optionImage ?? '';
              final bool hasImg   = img.isNotEmpty;
              final bool isAudio  = hasImg &&
                  (img.contains('.mp3') || img.contains('.ogg') || img.contains('.wav'));
              final bool isVideo  = hasImg && img.contains('.mp4');
              final double cardHeight = hasImg
                  ? (isAudio ? 150 : 250)
                  : 50;

              return Column(
                children: [
                  Container(
                    height: cardHeight,
                    width: MediaQuery.of(_scaffoldContext).size.width,
                    child: Stack(
                      children: [
                        Positioned(
                          bottom: 0,
                          left: 0,
                          child: Card(
                            color: bgColor,
                            elevation: 5,
                            margin: const EdgeInsets.only(right: 20),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                              side: BorderSide(color: borderColor, width: 1.5),
                            ),
                            child: Container(
                              width: MediaQuery.of(_scaffoldContext).size.width - 40,
                              height: hasImg ? (isAudio ? 150 : 250) : 45,
                              alignment: Alignment.topLeft,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 15),
                              child: Text(
                                option.optionStatement ?? '',
                                style: Styles.textRegular(size: 12, color: txtColor),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          top: 17,
                          right: 10,
                          child: isSelected
                              ? Icon(
                            isCorrect ? Icons.done_rounded : Icons.close,
                            color: isCorrect ? Colors.white : Colors.red,
                          )
                              : const SizedBox(),
                        ),

                        if (hasImg)
                          Positioned(
                            top: 50,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 0),
                              child: isVideo || isAudio
                                  ? Container(
                                height: 100,
                                width: MediaQuery.of(context).size.width - 40,
                                margin: const EdgeInsets.only(bottom: 10),
                                child: Center(
                                  child: InAppWebView(
                                    initialOptions: InAppWebViewGroupOptions(
                                      crossPlatform: InAppWebViewOptions(
                                        mediaPlaybackRequiresUserGesture: true,
                                        useShouldOverrideUrlLoading: true,
                                      ),
                                      ios: IOSInAppWebViewOptions(
                                        allowsInlineMediaPlayback: true,
                                        allowsLinkPreview: false,
                                      ),
                                    ),
                                    initialUrlRequest:
                                    URLRequest(url: WebUri(img)),
                                    onWebViewCreated: (controller) {
                                      _webViewController = controller;
                                    },
                                    onLoadStop: (controller, url) {
                                      controller.evaluateJavascript(source: '''
                                            document.querySelectorAll('video, audio')
                                                    .forEach(media => { media.muted = true; media.pause(); });
                                            document.body.style.backgroundColor = "#FFFFFF";
                                          ''');
                                    },
                                  ),
                                ),
                              )
                                  : ClipRRect(
                                borderRadius: const BorderRadius.only(
                                  bottomLeft: Radius.circular(20.0),
                                  bottomRight: Radius.circular(20.0),
                                ),
                                child: Image.network(
                                  img,
                                  fit: BoxFit.cover,
                                  width: MediaQuery.of(context).size.width - 40,
                                  height: 200,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  _size(),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }


  _multiChoose(TestReviewBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 15),
            child: Text(
              'question_type',
              style: Styles.textRegular(size: 12, color: Colors.grey),
            ).tr(),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black),
            ),
          ),
          _size(height: 20),
          Column(
            children: List.generate(
              widget._list[widget._currentQuestion].question!.questionOptions!.length,
              (index) {
                Color bgColor;
                Color txtColor;
                Color borderColor;
                //int.parse(widget._list[widget._currentQuestion].question!.correctOptions!.first)
                //String correctOption = widget._list[widget._currentQuestion].question!.correctOptions!.first.replaceAll(RegExp(r'[^0-9]'), '');
                String correctOption = '0';
                try {
                  correctOption = widget._list[widget._currentQuestion].question!.correctOptions!.first.replaceAll(RegExp(r'[^0-9]'), '');
                } catch (e) {
                  print('Error parsing integer: $e');
                }

                if (widget._list[widget._currentQuestion].question!.questionOptions![index].optionId ==
                    int.parse(correctOption)) {
                  borderColor = Colors.green;
                  bgColor = Colors.green;
                  txtColor = Colors.white;
                } else if (widget._list[widget._currentQuestion].question!
                            .questionOptions![index].optionId !=
                        int.parse(correctOption) &&
                    widget._list[widget._currentQuestion].question!
                            .questionOptions![index].userAnswer ==
                        1) {
                  borderColor = Colors.red;
                  bgColor = Colors.white;
                  txtColor = Colors.red;
                } else {
                  borderColor = Colors.grey;
                  bgColor = Colors.white;
                  txtColor = Colors.black;
                }
                return Column(
                  children: [
                    Container(
                      height: widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage != '' ? widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage.toString().contains('.mp3') ||
                          widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage.toString().contains('.ogg') ||
                          widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage.toString().contains('.wav') ? 150 :250 : 50,
                      width: MediaQuery.of(_scaffoldContext).size.width,
                      child: Stack(
                        children: [
                          Positioned(
                            bottom: 0,
                            left: 0,
                            child: Card(
                              color: bgColor,
                              elevation: 5,
                              margin: const EdgeInsets.only(right: 20),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(6),
                                ),
                                side:
                                    BorderSide(color: borderColor, width: 1.5),
                              ),
                              child: Container(
                                width: MediaQuery.of(_scaffoldContext).size.width - 40,
                                height: widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage != ''? widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage.toString().contains('.mp3') ||
                                    widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage.toString().contains('.ogg') ||
                                    widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage.toString().contains('.wav') ?150: 250 : 45,
                                alignment: Alignment.topLeft,
                                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                                child: Text(
                                  widget
                                      ._list[widget._currentQuestion]
                                      .question!
                                      .questionOptions![index]
                                      .optionStatement!,
                                  style: Styles.textRegular(
                                      size: 12, color: txtColor),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            top: 17,
                            right: 10,
                            child: widget
                                        ._list[widget._currentQuestion]
                                        .question!
                                        .questionOptions![index]
                                        .userAnswer ==
                                    1
                                ? Container(
                                    //color: Colors.grey[300],
                                    child: widget
                                                    ._list[
                                                        widget._currentQuestion]
                                                    .question!
                                                    .questionOptions![index]
                                                    .optionId !=
                                                int.parse(correctOption) &&
                                            widget
                                                    ._list[
                                                        widget._currentQuestion]
                                                    .question!
                                                    .questionOptions![index]
                                                    .userAnswer ==
                                                1
                                        ? Icon(
                                            Icons.close,
                                            color: Colors.red,
                                          )
                                        : Icon(
                                            Icons.done_rounded,
                                            color: Colors.white,
                                          ),
                                  )
                                : SizedBox(),
                          ),


                          //Text('${widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage}'),
                          Positioned(
                            top: 50,
                            child: widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage != '' ? Padding(
                              padding: EdgeInsets.only(right: 0),
                              child: widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage.toString().contains('.mp4') ||
                                  widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage.toString().contains('.mp3') ||
                                  widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage.toString().contains('.ogg') ||
                                  widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage.toString().contains('.wav') ?
                              Container(
                                height: 100,
                                width: MediaQuery.of(context).size.width -40,
                                margin: EdgeInsets.only(bottom: 10),
                                child: Center(
                                  child: InAppWebView(
                                      initialOptions: InAppWebViewGroupOptions(
                                          crossPlatform: InAppWebViewOptions(
                                            mediaPlaybackRequiresUserGesture: true,
                                            useShouldOverrideUrlLoading: true,
                                          ),

                                          ios: IOSInAppWebViewOptions(
                                              allowsInlineMediaPlayback: true,
                                              allowsLinkPreview: false),
                                      ),
                                      initialUrlRequest: URLRequest(
                                          url: WebUri('${widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage}')),

                                      onWebViewCreated: (controller) {
                                _webViewController = controller;
                                },
                                    onLoadStop: (controller, url) {
                                      // Inject JavaScript to mute all video and audio elements
                                      controller.evaluateJavascript(source: """
            document.querySelectorAll('video, audio').forEach(media => {
              media.muted = true;
              media.pause(); 
              document.body.style.backgroundColor = "#FFFFFF";
            });
          """);
                                    },
                                  ),
                                ),
                              ) :
                              ClipRRect(
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(20.0),
                                  bottomRight: Radius.circular(20.0),
                                ),
                                child: Image.network('${widget._list[widget._currentQuestion].question!.questionOptions![index].optionImage}',
                                  fit: BoxFit.cover,
                                width: MediaQuery.of(context).size.width - 40,
                                height: 200,),
                              ),

                            ):SizedBox(),
                          ),
                        ],
                      ),
                    ),
                    _size(),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  _subjectiveText(TestReviewBean testAttemptBean, String qusType) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Utility().isHtml(testAttemptBean.question!.answerStatement ?? "") ?
          Html(data: """${testAttemptBean.question!.answerStatement!.replaceAll(RegExp(r'<p[^>]*>(\s|&nbsp;|<br\s*/?>)*</p>', caseSensitive: false), '')}""",
            extensions: [TableHtmlExtension(),],
            style: {
              "table": Style(
                //border: Border.all(color: Colors.grey, width: 0.5),
                padding: HtmlPaddings.all(0),
                margin: Margins.only(top: 0),
              ),
              "td": Style(
                border: Border.all(color: Colors.grey, width: 0.5),
                padding: HtmlPaddings.only(left: 20, right: 20, top: 4, bottom: 4),   // small cell padding
              ),
              "p": Style(
                padding: HtmlPaddings.zero,
                margin: Margins.zero, // Optional: remove paragraph spacing
              ),
              "body": Style(
                margin: Margins.zero,
                padding: HtmlPaddings.zero,
              ),
            },
          ) : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                  padding: const EdgeInsets.only(top: 15),
                  child: Text(
                    'question_type',
                    style: Styles.textRegular(size: 12, color: Colors.grey),
                  ).tr()),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 1),
                child: Text(
                  testAttemptBean.question!.questionType ?? "",
                  style: Styles.textBold(size: 20),
                ),
              ),
              _size(height: 15),
              //singh
              Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                     TextField(
                      controller: TextEditingController(text: "${ testAttemptBean.question!.answerStatement!.replaceAll('&nbsp;', '')}"),
                      readOnly: true,
                      maxLines: 6,
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: Colors.white,
                        focusColor: Colors.white,
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        border: OutlineInputBorder(
                          borderSide: const BorderSide(color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        //hintText: '${tr('type_your_answer')}',
                        //hintStyle: const TextStyle(color: Colors.grey),
                      ),
                    ),
                    _size(height: 20),
                  ]
              ),
            ],
          ),
        ),

        Padding(padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 20),
          child: Container(
            width: double.infinity,
            color: Colors.grey[100],
            padding: const EdgeInsets.all(10.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(tr('faculty_feedback'), style: Styles.textBold(size: 13, color: Colors.black),),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text('${testAttemptBean.question!.teacherFeedback ?? ''}',
                      style: Styles.textRegular(size: 12, color: Colors.grey)),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  _fillInTheBlanks(TestReviewBean testAttemptBean, String qusType) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: const EdgeInsets.only(top: 15),
              child: Text(
                'question_type',
                style: Styles.textRegular(size: 12, color: Colors.grey),
              ).tr()),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          _size(height: 15),
          Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: TextEditingController(text: "${ testAttemptBean.question!.answerStatement}"),
                  readOnly: true,
                  maxLines: 6,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Colors.white,
                    focusColor: Colors.white,
                    focusedBorder: OutlineInputBorder(
                      borderSide: const BorderSide(color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    border: OutlineInputBorder(
                      borderSide: const BorderSide(color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: const BorderSide(color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    hintText: '${tr('type_your_answer')}',
                    hintStyle: const TextStyle(color: Colors.grey),
                  ),
                ),
                _size(height: 20),
              ]
          ),
        ],
      ),
    );
  }

  _matchingAnswer(TestReviewBean testAttemptBean) {
    final currentQuestion = widget._list[widget._currentQuestion].question;

    if (currentQuestion == null) {
      return Container(); // Handle null case if needed
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 15),
            child: Text(
              'question_type',
              style: Styles.textRegular(size: 12, color: Colors.grey),
            ).tr(),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              currentQuestion.questionType ?? "",
              style: Styles.textRegular(size: 18),
            ),
          ),
          _size(height: 20),
          Column(
            children: List.generate(
              currentQuestion.questionOptions!.length,
                  (index) {
                    late final selectedOptionIndex;
                    bool isSelected = false;

                    if(currentQuestion.optionSelected!.length > index){
                      selectedOptionIndex = int.tryParse(currentQuestion.optionSelected?[index] ?? "");
                      isSelected = selectedOptionIndex != null && selectedOptionIndex < currentQuestion.correctOptions!.length;
                    }

                return Column(
                  children: [
                    // Option container for List1
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey),
                      ),
                      width: MediaQuery.of(_scaffoldContext).size.width,
                      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
                      child: Text(
                        currentQuestion.questionOptions![index].optionStatement!,
                        style: Styles.textRegular(size: 14, color: Colors.black),
                      ),
                    ),
                    // Horizontal scroll for List2 and selected state
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: List.generate(
                          currentQuestion.correctOptions!.length,
                              (indexIn) => Container(
                            width: 220,
                            margin: const EdgeInsets.all(10.0),
                            decoration: BoxDecoration(
                              color: isSelected && selectedOptionIndex == indexIn
                                  ? ColorConstants.SELECTED_GREEN
                                  : Colors.white,
                              borderRadius: BorderRadius.circular(100),
                              border: Border.all(
                                color: isSelected && selectedOptionIndex == indexIn
                                    ? ColorConstants.SELECTED_GREEN
                                    : Colors.grey,
                              ),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 25),
                            child: Text(
                              '${currentQuestion.correctOptions![indexIn]}',
                              style: TextStyle(
                                color: isSelected && selectedOptionIndex == indexIn
                                    ? Colors.white
                                    : Colors.black,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    _size(height: 12),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  _shortAnswer(TestReviewBean testAttemptBean, String qusType) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: const EdgeInsets.only(top: 15),
              child: Text(
                'question_type',
                style: Styles.textRegular(size: 12, color: Colors.grey),
              ).tr()),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 1),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          _size(height: 15),
          Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: TextEditingController(text: "${ testAttemptBean.question!.answerStatement}"),
                  readOnly: true,
                  maxLines: 6,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Colors.white,
                    focusColor: Colors.white,
                    focusedBorder: OutlineInputBorder(
                      borderSide: const BorderSide(color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    border: OutlineInputBorder(
                      borderSide: const BorderSide(color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: const BorderSide(color: ColorConstants.PRIMARY_COLOR, width: 2.0),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    //hintText: '${tr('type_your_answer')}',
                    //hintStyle: const TextStyle(color: Colors.grey),
                  ),
                ),
                _size(height: 20),
              ]
          ),

          Padding(padding: const EdgeInsets.symmetric(vertical: 20,),
            child: Container(
              width: double.infinity,
              color: Colors.grey[100],
              padding: const EdgeInsets.all(10.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(tr('faculty_feedback'), style: Styles.textBold(size: 13, color: Colors.black),),
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text('${testAttemptBean.question!.teacherFeedback ?? ''}',
                        style: Styles.textRegular(size: 12, color: Colors.grey)),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }


  _chooseOne(TestReviewBean testAttemptBean) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Text(
              testAttemptBean.question!.questionType ?? "",
              style: Styles.textBold(size: 20),
            ),
          ),
          Column(
            children: List.generate(
              widget._list[widget._currentQuestion].question!.questionOptions!
                  .length,
              (index) {
                Color borderColor;
                if (widget._list[widget._currentQuestion].question!
                        .questionOptions![index].optionId ==
                    int.parse(widget._list[widget._currentQuestion].question!
                        .correctOptions!.first)) {
                  borderColor = Color(0xff66bb6a);
                } else if (widget._list[widget._currentQuestion].question!
                            .questionOptions![index].optionId !=
                        int.parse(widget._list[widget._currentQuestion]
                            .question!.correctOptions!.first) &&
                    widget._list[widget._currentQuestion].question!
                            .questionOptions![index].userAnswer ==
                        1) {
                  borderColor = Colors.red;
                } else {
                  borderColor = Colors.grey;
                }
                return Column(
                  children: [
                    Container(
                      width: MediaQuery.of(_scaffoldContext).size.width,
                      height: 80,
                      child: Stack(
                        children: [
                          Positioned(
                            bottom: 0,
                            left: 0,
                            child: Card(
                              elevation: 5,
                              margin: const EdgeInsets.only(right: 20),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                                side:
                                    BorderSide(color: borderColor, width: 1.5),
                              ),
                              child: Container(
                                width:
                                    MediaQuery.of(_scaffoldContext).size.width -
                                        40,
                                height: 55,
                                alignment: Alignment.centerLeft,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 15),
                                child: Text(
                                  widget
                                      ._list[widget._currentQuestion]
                                      .question!
                                      .questionOptions![index]
                                      .optionStatement!,
                                  style: Styles.textRegular(size: 12),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    _size(),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }


  _multipleMCQShortAnswer(
      TestReviewBean testAttemptBean, String qusType) {

    final html = testAttemptBean.question?.blankHtml ?? "";

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: QuestionOptionWebView(
        html: html,
        onValuesChanged: (values) {
          debugPrint("Question:- auto values: ${values.toString()}");
        },
      ),
    );
  }

}