import 'dart:async';
import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/faculty_response/matching_jobs_response.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/rounded_appbar.dart';
import 'package:masterg/pages/singularis/job/job_search_view_page.dart';
import 'package:masterg/pages/singularis/job/my_job_all_view_list_page.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/portfolio_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/resource/size_constants.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import '../../../blocs/bloc_manager.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/models/response/home_response/competition_response.dart';
import '../../../data/models/response/home_response/domain_filter_list.dart';
import '../../../data/models/response/home_response/domain_list_response.dart';
import '../../../data/models/response/home_response/training_module_response.dart';
import '../../../local/pref/Preference.dart';
import '../../../utils/Styles.dart';
import '../../custom_pages/custom_widgets/NextPageRouting.dart';
import 'job_details_page.dart';

class JobDashboardPage extends StatefulWidget {
  final bool? myJobEnable;

  const JobDashboardPage({Key? key, this.myJobEnable = true}) : super(key: key);

  @override
  State<JobDashboardPage> createState() => _JobDashboardPageState();
}

class _JobDashboardPageState extends State<JobDashboardPage> {
  bool? isJobLoading;
  bool? myJobLoading = true;
  bool? competitionDetailLoading = true;
  bool? domainListLoading = true;
  bool? jobApplyLoading = true;
  bool? isLoading = false;
  MatchingJobsResponse? matchingJobsResp;

  //List<ListElement>? jobList;
  CompetitionResponse? myJobResponse,
      allJobListResponse,
      recommendedJobOpportunities;
  TrainingModuleResponse? competitionDetail;
  DomainListResponse? domainList;
  DomainFilterListResponse? domainFilterList;
  int? programId;
  int selectedIndex = 0;
  String seletedIds = '';
  String domainId = '';
  List<int> selectedIdList = <int>[];
  int? applied;
  var _scaffoldKey = new GlobalKey<ScaffoldState>();
  bool myJobRecall = false;
  MenuListProvider? menuProvider;
  bool tabSelected1 = true;
  bool tabSelected2 = false;
  bool tabSelected3 = false;

  @override
  void initState() {
    super.initState();

    getMyJobList(false);
    getDomainList();
    getMatchingJobsList();
  }

  void getMyJobList(bool jobType) {
    BlocProvider.of<HomeBloc>(context).add(JobCompListEvent(
        isPopular: false,
        isFilter: false,
        isJob: 1,
        myJob: 1,
        jobTypeMyJob: jobType));
  }

  void jobApply(int jobId, int? isApplied) {
    BlocProvider.of<HomeBloc>(context).add(CompetitionContentListEvent(
        competitionId: jobId, isApplied: isApplied));
  }

  void getDomainList() {
    BlocProvider.of<HomeBloc>(context).add(DomainListEvent());
  }

  void getFilterList(String ids) {
    BlocProvider.of<HomeBloc>(context).add(DomainFilterListEvent(ids: ids));
  }

  void _handlecompetitionListResponse(JobCompListState state) {
    var jobCompState = state;
    setState(() {
      switch (jobCompState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          myJobLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("CompetitionState....................");
          if (myJobRecall == false) {
            myJobResponse = state.myJobListResponse;
            allJobListResponse = state.jobListResponse;
            recommendedJobOpportunities = state.recommendedJobOpportunities;

            print('allJobListResponse====${allJobListResponse?.data?.length}');
          } else {
            myJobResponse = state.myJobListResponse;
          }

          myJobLoading = false;
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error CompetitionListIDState .....................${jobCompState.error}");
          myJobLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'job_dashboard', parameters: {
            "ERROR": '${jobCompState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void getMatchingJobsList() {
    BlocProvider.of<HomeBloc>(context).add(MatchingJobsEvent());
  }

  void handleMatchingJobsList(MatchingJobsState state) {
    var matchingJobsListState = state;
    setState(() {
      switch (matchingJobsListState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          //isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("DashboardContentState...................${matchingJobsResp}.");
          Log.v(state.response!.data);

          matchingJobsResp = matchingJobsListState.response;
          Log.v("DashboardContentState...................${matchingJobsResp}.");

          isLoading = false;
          break;
        case ApiStatus.ERROR:
          isLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................");
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handlecompetitionDetailResponse(CompetitionDetailState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          competitionDetailLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Competition Detail State....................");
          competitionDetail = state.response;
          competitionDetailLoading = false;
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error Competition Detail IDState ..........................${competitionState.error}");
          competitionDetailLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'job_dashboard', parameters: {
            "ERROR": '${competitionState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleDomainListResponse(DomainListState state) {
    var popularCompetitionState = state;
    setState(() {
      switch (popularCompetitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("DomainListLoading....................");
          domainListLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("DomainListState....................");
          domainList = state.response;
          domainListLoading = false;

          if (domainList?.data?.list?.length != 0) {
            domainId = domainList!.data!.list![0].id.toString();
          }

          break;
        case ApiStatus.ERROR:
          Log.v(
              "DomainListError....................${popularCompetitionState.error}");
          domainListLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'job_dashboard', parameters: {
            "ERROR": '${popularCompetitionState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleJobApplyState(CompetitionContentListState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          jobApplyLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Competition Content List State....................");
          myJobRecall = true;
          getMyJobList(true);

          jobApplyLoading = false;
          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error Competition Content ..........................${competitionState.response?.error}");
          jobApplyLoading = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'job_dashboard', parameters: {
            "ERROR": '${competitionState.response?.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (context) {},
        child: Consumer<MenuListProvider>(
          builder: (context, mp, child) => BlocListener<HomeBloc, HomeState>(
            listener: (context, state) {
              if (state is JobCompListState) {
                _handlecompetitionListResponse(state);
              }
              if (state is CompetitionDetailState) {
                handlecompetitionDetailResponse(state);
              }
              if (state is DomainListState) {
                handleDomainListResponse(state);
              }
              if (state is CompetitionContentListState)
                handleJobApplyState(state);

              if (state is MatchingJobsState) {
                handleMatchingJobsList(state);
              }

              setState(() {
                menuProvider = mp;
              });
            },
            child: Scaffold(
                key: _scaffoldKey,
                appBar: widget.myJobEnable == false
                    ? AppBar(
                        backgroundColor: ColorConstants.WHITE,
                        elevation: 0.5,
                        iconTheme: IconThemeData(
                          color: Colors.black, //change your color here
                        ),
                        title: Text(
                          "job_opportunities",
                          style: TextStyle(color: Colors.black),
                        ).tr(),
                      )
                    : null,
                //endDrawer: new AppDrawer(),
                backgroundColor: ColorConstants.JOB_BG_COLOR,
                body: _makeBody()
                //  allJobListResponse?.data != null ||
                //         matchingJobsResp?.data != null
                //     ? _makeBody()
                //     : allJobListResponse?.data?.isEmpty == true &&
                //             matchingJobsResp?.data?.matchingJobs?.isEmpty == true
                //         ? Center(
                //             child: Text('no_jobs_found',
                //                     style: Styles.bold(size: 16))
                //                 .tr())
                //         : SizedBox(),
                ),
          ),
        ));
  }

  void getCompetitionList(bool isFilter, String? ids) {
    BlocProvider.of<HomeBloc>(context).add(
        CompetitionListEvent(isPopular: false, isFilter: isFilter, ids: ids));
  }

  Widget _makeBody() {
    return SingleChildScrollView(
        physics: ScrollPhysics(),
        child: Container(
          margin: EdgeInsets.only(bottom: SizeConstants.JOB_BOTTOM_SCREEN_MGN),
          width: MediaQuery.of(context).size.width,
          child: MultiProvider(
            providers: [
              ChangeNotifierProvider<CompetitionResponseProvider>(
                create: (context) =>
                    CompetitionResponseProvider(allJobListResponse?.data),
              ),
              ChangeNotifierProvider<MatchingJobsProvider>(
                  create: (context) => MatchingJobsProvider(
                      matchingJobsResp!.data!.matchingJobs))
            ],
            child: Column(
              children: [
                widget.myJobEnable == true ? _customAppBar() : SizedBox(),
                _tabBar(),
                if (allJobListResponse?.data?.isEmpty == true &&
                    tabSelected2 == false) ...[
                  Padding(
                    padding: const EdgeInsets.only(top: 150.0),
                    child: Center(
                        child:
                            Text('no_jobs_found', style: Styles.bold(size: 16))
                                .tr()),
                  ),
                ]
              ],
            ),
          ),
        ));
  }

  Widget _tabBar() {
    return Container(
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: ColorConstants.WHITE,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              //padding: const EdgeInsets.only(left: 20, right: 30, top: 20),
              padding: const EdgeInsets.only(left: 0, right: 0, top: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        tabSelected1 = true;
                        tabSelected2 = false;
                        tabSelected3 = false;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(left: 10, bottom: 5),
                      child: Text('recommended_jobs',
                              style: tabSelected1
                                  ? Styles.bold(
                                      size: 14,
                                      color: tabSelected1
                                          ? APK_DETAILS['package_name'] ==
                                                  'com.singulariswow'
                                              ? ColorConstants.WOW_PRIMARY_COLOR
                                              : APK_DETAILS['package_name'] ==
                                                      'com.singularis.jumeira'
                                                  ? ColorConstants.VIEW_ALL
                                                  : ColorConstants.PRIMARY_BLUE
                                          : ColorConstants.BODY_TEXT)
                                  : Styles.semibold(
                                      size: 14,
                                      color: tabSelected1
                                          ? APK_DETAILS['package_name'] ==
                                                  'com.singulariswow'
                                              ? ColorConstants.WOW_PRIMARY_COLOR
                                              : APK_DETAILS['package_name'] ==
                                                      'com.singularis.jumeira'
                                                  ? ColorConstants.VIEW_ALL
                                                  : ColorConstants.PRIMARY_BLUE
                                          : ColorConstants.BODY_TEXT))
                          .tr(),
                    ),
                  ),
                  Spacer(),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        tabSelected1 = false;
                        tabSelected2 = true;
                        tabSelected3 = false;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(left: 10, bottom: 5),
                      child: Text('my_job',
                              style: tabSelected2
                                  ? Styles.bold(
                                      size: 14,
                                      color: tabSelected2
                                          ? APK_DETAILS['package_name'] ==
                                                  'com.singulariswow'
                                              ? ColorConstants.WOW_PRIMARY_COLOR
                                              : APK_DETAILS['package_name'] ==
                                                      'com.singularis.jumeira'
                                                  ? ColorConstants.VIEW_ALL
                                                  : ColorConstants.PRIMARY_BLUE
                                          : ColorConstants.BODY_TEXT)
                                  : Styles.semibold(
                                      size: 14,
                                      color: tabSelected2
                                          ? APK_DETAILS['package_name'] ==
                                                  'com.singulariswow'
                                              ? ColorConstants.WOW_PRIMARY_COLOR
                                              : APK_DETAILS['package_name'] ==
                                                      'com.singularis.jumeira'
                                                  ? ColorConstants.VIEW_ALL
                                                  : ColorConstants.PRIMARY_BLUE
                                          : ColorConstants.BODY_TEXT))
                          .tr(),
                    ),
                  ),
                  Spacer(),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        tabSelected1 = false;
                        tabSelected2 = false;
                        tabSelected3 = true;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(right: 10, bottom: 5),
                      child: Text('invited',
                              style: tabSelected3
                                  ? Styles.bold(
                                      size: 14,
                                      color: tabSelected3
                                          ? APK_DETAILS['package_name'] ==
                                                  'com.singulariswow'
                                              ? ColorConstants.WOW_PRIMARY_COLOR
                                              : APK_DETAILS['package_name'] ==
                                                      'com.singularis.jumeira'
                                                  ? ColorConstants.VIEW_ALL
                                                  : ColorConstants.PRIMARY_BLUE
                                          : Colors.grey)
                                  : Styles.semibold(
                                      size: 14,
                                      color: tabSelected3
                                          ? APK_DETAILS['package_name'] ==
                                                  'com.singulariswow'
                                              ? ColorConstants.WOW_PRIMARY_COLOR
                                              : APK_DETAILS['package_name'] ==
                                                      'com.singularis.jumeira'
                                                  ? ColorConstants.VIEW_ALL
                                                  : ColorConstants.PRIMARY_BLUE
                                          : Colors.grey))
                          .tr(),
                    ),
                  ),
                ],
              ),
            ),
            Stack(
              children: [
                Container(
                  color: ColorConstants.DIVIDER_COLOR_1,
                  width: MediaQuery.of(context).size.width,
                  height: 1.5,
                ),
                Positioned(
                  right: Utility().isRTL(context) ? width(context) * 0.5 : 220,
                  left: Utility().isRTL(context) ? 0 : width(context) * 0,
                  child: Container(
                    decoration: BoxDecoration(
                        gradient: tabSelected1
                            ? LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight()
                                  ])
                            : LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants.GREY_4,
                                    ColorConstants.GREY_4,
                                  ])),
                    height: tabSelected1 ? 2 : 0,
                  ),
                ),
                Positioned(
                  left: Utility().isRTL(context) ? width(context) * 0.5 : 180,
                  right: Utility().isRTL(context) ? 0 : width(context) * 0.30,
                  child: Container(
                    decoration: BoxDecoration(
                        gradient: tabSelected2
                            ? LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight()
                                  ])
                            : LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants.GREY_4,
                                    ColorConstants.GREY_4,
                                  ])),
                    height: tabSelected2 ? 3 : 0,
                  ),
                ),
                Positioned(
                  right: Utility().isRTL(context) ? width(context) * 0.7 : 0,
                  left: Utility().isRTL(context) ? 0 : width(context) * 0.72,
                  child: Container(
                    decoration: BoxDecoration(
                        gradient: tabSelected3
                            ? LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants().gradientLeft(),
                                    ColorConstants().gradientRight()
                                  ])
                            : LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                    ColorConstants.GREY_4,
                                    ColorConstants.GREY_4,
                                  ])),
                    height: tabSelected3 ? 4 : 0,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 10,
            ),
            Visibility(
              visible: tabSelected1,
              child: Container(
                width: MediaQuery.of(context).size.width,
                color: ColorConstants.WHITE,
                child: Column(children: [
                  Container(
                    height: 40,
                    color: ColorConstants.GREY,
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: SizeConstants.JOB_LEFT_SCREEN_MGN,
                          right: SizeConstants.JOB_RIGHT_SCREEN_MGN),
                      child: _searchFilter(),
                    ),
                  ),
                  if (widget.myJobEnable == true) ...[
                    myJobResponse?.data != null
                        ? _myJobSectionCard(0)
                        : SizedBox(),
                  ],

                  ///Complete Profile
                  SizedBox(
                    height: 18,
                  ),
                  widget.myJobEnable == true
                      ? _highLightsCard(
                          ColorConstants.HIGH_LIGHTS_CARD_COLOR1,
                          tr('Complete_your_profile_get_noticed_one'),
                          tr('Complete_your_profile_get_noticed_two'),
                          'complete_profile')
                      : SizedBox(),

                  // SizedBox(
                  //   height: 18,
                  // ),

                  // myJobLoading == false ? _jobBasedYourListCard() : BlankPage(),

                  // recommendedJobOpportunities?.data != null
                  //     ? _recommendedOpportunitiesListCard()
                  //     : BlankPage(),

                  // SizedBox(
                  //   height: 10,
                  // ),
                  allJobListResponse?.data != null ? _step2Card() : BlankPage(),

                  SizedBox(
                    height: 18,
                  ),
                  _highLightsCard(ColorConstants.ORANGE, tr('build_portfolio'),
                      tr('build_portfolio_text'), 'build_portfolio'),

                  SizedBox(
                    height: 18,
                  ),

                  allJobListResponse?.data != null ? _step3Card() : BlankPage(),
                ]),
              ),
            ),
            Visibility(
              visible: tabSelected2,
              child: Container(
                width: MediaQuery.of(context).size.width,
                color: ColorConstants.WHITE,
                child: Column(children: [
                  // Container(
                  //   height: 40,
                  //   color: ColorConstants.GREY,
                  //   child: Padding(
                  //     padding: const EdgeInsets.only(
                  //         left: SizeConstants.JOB_LEFT_SCREEN_MGN,
                  //         right: SizeConstants.JOB_RIGHT_SCREEN_MGN),
                  //     child: _searchFilter(),
                  //   ),
                  // ),
                  if (widget.myJobEnable == false) ...[
                    myJobResponse?.data != null
                        ? _myJobSectionCard(1)
                        : SizedBox(),
                  ],
                ]),
              ),
            ),
            Visibility(
                visible: tabSelected3,
                child: Container(
                    width: MediaQuery.of(context).size.width,
                    color: ColorConstants.WHITE,
                    child: matchingJobsResp?.data != null &&
                            matchingJobsResp?.data?.matchingJobs?.length != 0
                        ? invitedJobsCard()
                        : Container(
                            height: height(context),
                            child: Visibility(
                              visible: tabSelected3 &&
                                  matchingJobsResp
                                          ?.data?.matchingJobs?.length ==
                                      0,
                              child: Padding(
                                padding: const EdgeInsets.only(bottom: 150.0),
                                child: Center(
                                    child: Text('no_jobs_found',
                                            style: Styles.bold(size: 16))
                                        .tr()),
                              ),
                            ),
                          ))),
          ],
        ));
  }

  Widget _customAppBar() {
    return RoundedAppBar(
        appBarHeight: height(context) * 0.1,
        child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 12),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => NewPortfolioPage()))
                              .then((value) {
                            if (value != null)
                              menuProvider?.updateCurrentIndex(value);
                          });
                        },
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(200),
                          child: CachedNetworkImage(
                              imageUrl:
                                  '${Preference.getString(Preference.PROFILE_IMAGE)}',
                              fit: BoxFit.cover,
                              height: 50,
                              width: 50,
                              placeholder: (context, url) => SvgPicture.asset(
                                    'assets/images/default_user.svg',
                                    width: 50,
                                    height: 50,
                                  ),
                              errorWidget: (context, url, error) =>
                                  SvgPicture.asset(
                                    'assets/images/default_user.svg',
                                    width: 50,
                                    height: 50,
                                  )),
                        ),
                      ),
                      SizedBox(width: 10),
                      Spacer(),
                      Padding(
                        padding: Utility().isRTL(context)
                            ? EdgeInsets.only(left: 6, top: 8)
                            : EdgeInsets.only(right: 6, bottom: 8),
                        child: SizedBox(
                          // flex: 2,
                          child: Align(
                            alignment: Utility().isRTL(context)
                                ? Alignment.topLeft
                                : Alignment.topRight,
                            child: InkWell(
                              onTap: () {
                                _scaffoldKey.currentState?.openEndDrawer();
                              },
                              child: SvgPicture.asset(
                                  'assets/images/hamburger_menu.svg'),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ])));
  }

  Widget _searchFilter() {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            flex: 9,
            child: Container(
              child: Text('relevant_jobs',
                      style: Styles.bold(
                          size: 16, color: ColorConstants.HEADING_TITLE))
                  .tr(),
            ),
          ),
          // if (APK_DETAILS['package_name'] != 'com.singulariswow.mec') //for filter hide
          Expanded(
            flex: 1,
            child: InkWell(
              onTap: () async {
                selectedIndex = 0;
                //print('domain count=== ${domainList?.data?.list?.length}');
                getFilterList(domainList!.data!.list![0].id.toString());
                domainFilterList?.data?.list.clear();

                await showModalBottomSheet(
                    context: context,
                    backgroundColor: Colors.transparent,
                    isScrollControlled: true,
                    builder: (context) {
                      return StatefulBuilder(
                          builder: (BuildContext context, setState) {
                        void handleDomainFilterListResponse(
                            DomainFilterListState state) {
                          var popularCompetitionState = state;
                          setState(() {
                            switch (popularCompetitionState.apiState) {
                              case ApiStatus.LOADING:
                                Log.v("Loading....................");
                                domainListLoading = true;
                                break;
                              case ApiStatus.SUCCESS:
                                Log.v(
                                    "Filter list State......######..............");
                                domainFilterList = state.response;
                                domainListLoading = false;
                                setState(() {});

                                break;
                              case ApiStatus.ERROR:
                                Log.v(
                                    "Filter list CompetitionListIDState ............${popularCompetitionState.error}");
                                domainListLoading = false;
                                break;
                              case ApiStatus.INITIAL:
                                break;
                            }
                          });
                        }

                        return BlocListener<HomeBloc, HomeState>(
                            listener: (context, state) {
                              if (state is DomainFilterListState) {
                                handleDomainFilterListResponse(state);
                              }
                            },
                            child: FractionallySizedBox(
                              heightFactor: 0.7,
                              child: Container(
                                height: double.infinity,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                    color: ColorConstants.WHITE,
                                    borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(12),
                                        topRight: Radius.circular(8))),
                                child: SingleChildScrollView(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Center(
                                        child: Container(
                                          decoration: BoxDecoration(
                                              color: ColorConstants.GREY_4,
                                              borderRadius:
                                                  BorderRadius.circular(8)),
                                          width: 48,
                                          height: 5,
                                          margin: EdgeInsets.only(top: 8),
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 4),
                                        child: Row(
                                          children: [
                                            Text(
                                              'filter_by',
                                              style: Styles.semibold(size: 16),
                                            ).tr(),
                                            Spacer(),
                                            IconButton(
                                                onPressed: () {
                                                  this.setState(() {
                                                    seletedIds = '0';
                                                    selectedIdList.clear();
                                                  });
                                                  Navigator.pop(context);
                                                },
                                                icon: Icon(Icons.close))
                                          ],
                                        ),
                                      ),
                                      Divider(
                                        color: ColorConstants.GREY_4,
                                      ),
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 4),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 10,
                                                        vertical: 4),
                                                child: Text(
                                                  'domain',
                                                  style: Styles.bold(size: 14),
                                                ).tr()),
                                            Container(
                                              child: Wrap(
                                                direction: Axis.horizontal,
                                                children: List.generate(
                                                    domainList!
                                                        .data!.list!.length,
                                                    (i) => InkWell(
                                                          onTap: () {
                                                            setState(() {
                                                              selectedIndex = i;
                                                              seletedIds = '';
                                                              selectedIdList =
                                                                  [];
                                                              domainId =
                                                                  domainList!
                                                                      .data!
                                                                      .list![i]
                                                                      .id
                                                                      .toString();
                                                            });
                                                            getFilterList(
                                                                domainList!
                                                                    .data!
                                                                    .list![i]
                                                                    .id
                                                                    .toString());
                                                          },
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .only(
                                                                    left: 10,
                                                                    right: 5),
                                                            child: Chip(
                                                              side: i ==
                                                                      selectedIndex
                                                                  ? BorderSide(
                                                                      color: ColorConstants()
                                                                          .gradientRight())
                                                                  : null,
                                                              backgroundColor: i ==
                                                                      selectedIndex
                                                                  ? ColorConstants()
                                                                      .gradientRight()
                                                                      .withValues(
                                                                          alpha:
                                                                              0.08)
                                                                  : Color(
                                                                      0xffF2F2F2),
                                                              label: Container(
                                                                child: Text(
                                                                  '${domainList!.data!.list![i].name}',
                                                                  style: Styles
                                                                      .semibold(
                                                                          size:
                                                                              12,
                                                                          color:
                                                                              ColorConstants.BLACK),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        )),
                                              ),
                                            ),
                                            Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 10,
                                                        vertical: 4),
                                                child: Text(
                                                  'job_roles',
                                                  style: Styles.bold(size: 14),
                                                ).tr()),
                                            if (domainFilterList != null)
                                              Container(
                                                child: Wrap(
                                                  direction: Axis.horizontal,
                                                  children: List.generate(
                                                      domainFilterList!
                                                          .data!.list.length,
                                                      (i) => InkWell(
                                                            onTap: () {
                                                              if (selectedIdList
                                                                  .contains(
                                                                      domainFilterList!
                                                                          .data!
                                                                          .list[
                                                                              i]
                                                                          .id)) {
                                                                selectedIdList.remove(
                                                                    domainFilterList!
                                                                        .data!
                                                                        .list[i]
                                                                        .id);
                                                              } else {
                                                                selectedIdList.add(
                                                                    domainFilterList!
                                                                        .data!
                                                                        .list[i]
                                                                        .id!);
                                                              }
                                                              setState(() {});
                                                            },
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      left: 10,
                                                                      right: 5),
                                                              child: Chip(
                                                                side: selectedIdList.contains(
                                                                        domainFilterList!
                                                                            .data!
                                                                            .list[
                                                                                i]
                                                                            .id)
                                                                    ? BorderSide(
                                                                        color: ColorConstants()
                                                                            .gradientRight())
                                                                    : null,
                                                                backgroundColor: selectedIdList.contains(
                                                                        domainFilterList!
                                                                            .data!
                                                                            .list[
                                                                                i]
                                                                            .id)
                                                                    ? ColorConstants()
                                                                        .gradientRight()
                                                                        .withValues(
                                                                            alpha:
                                                                                0.08)
                                                                    : Color(
                                                                        0xffF2F2F2),
                                                                label:
                                                                    Container(
                                                                  child: Text(
                                                                      '${domainFilterList!.data!.list[i].title}',
                                                                      style: Styles
                                                                          .regular(
                                                                        size:
                                                                            12,
                                                                        color: ColorConstants
                                                                            .BLACK,
                                                                      )),
                                                                ),
                                                              ),
                                                            ),
                                                          )),
                                                ),
                                              ),
                                            InkWell(
                                              onTap: () {
                                                seletedIds = selectedIdList
                                                    .toString()
                                                    .replaceAll("[", "")
                                                    .replaceAll("]", "");
                                                Navigator.push(
                                                        context,
                                                        NextPageRoute(
                                                            JobSearchViewPage(
                                                              appBarTitle: tr(
                                                                  'Search_Jobs'),
                                                              isSearchMode:
                                                                  false,
                                                              jobRolesId:
                                                                  seletedIds,
                                                              domainId:
                                                                  domainId,
                                                            ),
                                                            isMaintainState:
                                                                true))
                                                    .then((value) => null);

                                                FirebaseAnalytics.instance
                                                    .logEvent(
                                                        name: 'careers_filer',
                                                        parameters: {
                                                      "domains_id": seletedIds,
                                                    });
                                              },
                                              child: Container(
                                                height: 40,
                                                margin: EdgeInsets.only(
                                                    left: 50,
                                                    top: 50,
                                                    right: 50,
                                                    bottom: 20),
                                                width: MediaQuery.of(context)
                                                    .size
                                                    .width,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(50),
                                                  gradient:
                                                      LinearGradient(colors: [
                                                    ColorConstants()
                                                        .gradientLeft(),
                                                    ColorConstants()
                                                        .gradientRight(),
                                                  ]),
                                                ),
                                                child: Align(
                                                  alignment: Alignment.center,
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            10.0),
                                                    child: Text(
                                                      tr('Search_Jobs'),
                                                      style: Styles.regular(
                                                        size: 13,
                                                        color: ColorConstants
                                                            .WHITE,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ));
                      });
                    }).then((value) {
                  getMyJobList(false);
                  getDomainList();
                });
              },
              child: Container(
                padding: EdgeInsets.only(left: 10.0),
                child: Icon(
                  Icons.filter_list,
                  color: ColorConstants.HEADING_TITLE,
                  size: 28,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _highLightsCard(
      Color colorBg, String strTitle, String strDes, String clickType) {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 6),
      margin: const EdgeInsets.only(
          left: SizeConstants.JOB_LEFT_SCREEN_MGN,
          right: SizeConstants.JOB_RIGHT_SCREEN_MGN),
      width: double.infinity,
      child: InkWell(
        onTap: () {
          if (clickType == 'build_portfolio') {
            FirebaseAnalytics.instance
                .logEvent(name: 'careers_portfolio', parameters: {
              "build_portfolio": 'build portfolio',
            });
            Navigator.push(context, NextPageRoute(NewPortfolioPage()))
                .then((value) {
              if (value != null) menuProvider?.updateCurrentIndex(value);
            });
          } else if (clickType == 'complete_profile') {
            FirebaseAnalytics.instance
                .logEvent(name: 'careers_profile', parameters: {
              "complete_profile": 'complete profile',
            });
            Navigator.push(context, NextPageRoute(NewPortfolioPage()))
                .then((value) {
              if (value != null) menuProvider?.updateCurrentIndex(value);
            });
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              clickType != 'complete_profile'
                  ? Expanded(
                      child: Image.asset(
                        'assets/images/build_read.png',
                        height: 40,
                        width: 40,
                      ),
                    )
                  : SizedBox(),
              Expanded(
                flex: 9,
                child: Container(
                  margin: EdgeInsets.only(left: 10.0, right: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('$strTitle',
                          style: Styles.bold(
                              lineHeight: 1.4,
                              size: 16,
                              color: ColorConstants.WHITE)),
                      Padding(
                        padding: const EdgeInsets.only(top: 10.0),
                        child: Text('$strDes',
                            style: Styles.regularWhite(lineHeight: 1.4)),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Container(
                  padding: EdgeInsets.only(
                    left: 10.0,
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: colorBg,
        boxShadow: [],
      ),
    );
  }

  Widget _myJobSectionCard(int scrollDirection) {
    return myJobResponse?.data!.length != 0
        ? Column(
            children: [
              Container(
                color: Colors.white,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(3.0), //my_job
                      child: Text('',
                              style: Styles.bold(
                                  size: 14,
                                  color: ColorConstants.HEADING_TITLE))
                          .tr(),
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.push(
                            context,
                            NextPageRoute(MyJobAllViewListPage(
                              myJobResponse: myJobResponse,
                            ))).then((value) {
                          getMyJobList(false);
                          getDomainList();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(right: 10.0),
                        child: Text(
                          'view_all',
                          style: Styles.regular(
                              size: 12, color: ColorConstants.HEADING_TITLE),
                        ).tr(),
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                height: 1,
                color: ColorConstants.DIVIDER_COLOR_1,
              ),
              Container(
                  padding: EdgeInsets.all(0),
                  height: scrollDirection == 1
                      ? null
                      : MediaQuery.of(context).size.height * 0.16,
                  width: width(context),
                  decoration: BoxDecoration(color: ColorConstants.WHITE),
                  child: ListView.builder(
                    physics: scrollDirection == 1
                        ? NeverScrollableScrollPhysics()
                        : null,
                    shrinkWrap: true,
                    scrollDirection:
                        scrollDirection == 1 ? Axis.vertical : Axis.horizontal,
                    itemCount: myJobResponse?.data!.length,
                    itemBuilder: (BuildContext context, int index) {
                      return InkWell(
                        onTap: () {
                          Navigator.push(
                              context,
                              NextPageRoute(JobDetailsPage(
                                title: myJobResponse?.data![index]!.name,
                                description:
                                    myJobResponse?.data![index]!.description,
                                location: myJobResponse?.data![index]!.location,
                                skillNames:
                                    myJobResponse?.data![index]!.skillNames,
                                companyName:
                                    myJobResponse?.data![index]!.organizedBy ??
                                        '',
                                domain: myJobResponse?.data![index]!.domainName,
                                companyThumbnail:
                                    myJobResponse?.data![index]!.image,
                                experience:
                                    myJobResponse?.data![index]!.experience,
                                id: myJobResponse?.data![index]!.id,
                                jobStatus:
                                    myJobResponse?.data![index]!.jobStatus,
                                jobStatusNumeric: int.parse(
                                    '${myJobResponse?.data![index]?.jobStatusNumeric ?? 0}'),
                                vacancy: myJobResponse?.data![index]?.vacancy,
                                minExperience:
                                    myJobResponse?.data![index]?.minExperience,
                                maxExperience:
                                    myJobResponse?.data![index]?.maxExperience,
                                landingPageUrl:
                                    myJobResponse?.data![index]?.landingPageUrl,
                              ))).then((value) {
                            getMyJobList(false);
                            getDomainList();
                          });
                        },
                        child: Container(
                          //height: MediaQuery.of(context).size.height * 0.17,
                          //width: min(width(context), 480) * 0.9,
                          margin: const EdgeInsets.only(right: 0, top: 0),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(0),
                            //border: Border.all(color: ColorConstants.DIVIDER_COLOR_1, width: 1),
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(
                                    width: min(width(context), 480) * 0.25,
                                    height: MediaQuery.of(context).size.height *
                                        0.12,
                                    // height: min(width(context), 480) * 0.25,
                                    padding: EdgeInsets.only(
                                        right: 15.0,
                                        left: 15,
                                        top: 10,
                                        bottom: 10),
                                    child: myJobResponse?.data![index]!.image !=
                                            null
                                        ? Image.network(
                                            '${myJobResponse?.data![index]!.image}',

                                            width:
                                                min(width(context), 480) * 0.25,
                                            height: MediaQuery.of(context)
                                                    .size
                                                    .height *
                                                0.12,

                                            // height: min(width(context), 480) * 0.25,
                                            // fit: BoxFit.cover,
                                          )
                                        : Image.asset('assets/images/pb_2.png'),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 12),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          width: min(width(context), 480) * 0.6,
                                          child: Text(
                                            '${myJobResponse?.data![index]!.name}',
                                            style: Styles.bold(size: 16),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 2,
                                          ),
                                        ),
                                        SizedBox(
                                          height: 4,
                                        ),
                                        SizedBox(
                                            width:
                                                min(width(context), 480) * 0.6,
                                            child: Text(
                                              '${myJobResponse?.data![index]!.organizedBy ?? ''}',
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              style: Styles.regular(
                                                  color: ColorConstants.GREY_3,
                                                  size: 13),
                                            )),
                                        SizedBox(
                                          height: height(context) * 0.015,
                                        ),
                                        myJobResponse
                                                    ?.data![index]!.jobStatus !=
                                                null
                                            ? Container(
                                                height: MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    0.025,
                                                decoration: BoxDecoration(
                                                  color: ColorConstants
                                                      .DIVIDER_COLOR_2,
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  // boxShadow: [
                                                  //   BoxShadow(
                                                  //     color: Colors.grey
                                                  //         .withValues(alpha: 0.5),
                                                  //     spreadRadius: 5,
                                                  //     blurRadius: 7,
                                                  //     offset: Offset(0,
                                                  //         3), // changes position of shadow
                                                  //   ),
                                                  // ],
                                                ),
                                                child: Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 8.0),
                                                  child: Center(
                                                    child: Text(
                                                      '${myJobResponse?.data![index]!.jobStatus ?? ''}',
                                                      style: Styles.regular(
                                                          color: myJobResponse
                                                                      ?.data![
                                                                          index]!
                                                                      .jobStatusNumeric ==
                                                                  0
                                                              ? ColorConstants
                                                                  .VIEW_ALL
                                                              : Colors.green,
                                                          size: 12),
                                                    ),
                                                  ),
                                                ),
                                              )
                                            : SizedBox(),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Divider(
                                height: 1,
                                color: ColorConstants.DIVIDER_COLOR_1,
                                thickness: 1,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ))
            ],
          )
        : SizedBox();
  }

  Widget invitedJobsCard() {
    return Consumer<MatchingJobsProvider>(
        builder: (context, matchingJobsProvider, child) => matchingJobsResp
                        ?.data?.matchingJobs ==
                    null &&
                matchingJobsResp?.data?.matchingJobs?.length == 0
            ? Center(child: Text('no_data', style: Styles.bold(size: 16)).tr())
            : ListView.builder(
                itemCount: matchingJobsResp?.data?.matchingJobs?.length ?? 0,
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return Container(
                    width: double.infinity,
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Container(
                                padding: EdgeInsets.only(
                                  right: 10.0,
                                ),
                                child: matchingJobsResp?.data
                                            ?.matchingJobs?[index].image !=
                                        null
                                    ? Image.network(
                                        '${matchingJobsResp?.data?.matchingJobs?[index].image ?? ''}')
                                    : Image.asset('assets/images/pb_2.png')),
                          ),
                          Expanded(
                            flex: 9,
                            child: InkWell(
                              onTap: () {
                                FirebaseAnalytics.instance.logEvent(
                                    name: 'careers_job_click',
                                    parameters: {
                                      "job_name": matchingJobsResp?.data
                                              ?.matchingJobs?[index].name ??
                                          '',
                                    });
                                Navigator.push(
                                    context,
                                    NextPageRoute(JobDetailsPage(
                                      title: matchingJobsResp
                                          ?.data?.matchingJobs?[index].name,
                                      description: matchingJobsResp?.data
                                          ?.matchingJobs?[index].description,
                                      location: matchingJobsResp?.data
                                          ?.matchingJobs?[index].workAddress,
                                      skillNames: '',
                                      companyName: matchingJobsResp?.data
                                          ?.matchingJobs?[index].organizedBy,
                                      domain: '',
                                      companyThumbnail: matchingJobsResp
                                          ?.data?.matchingJobs?[index].image,
                                      experience: matchingJobsResp?.data
                                          ?.matchingJobs?[index].experience,
                                      id: matchingJobsResp
                                          ?.data?.matchingJobs?[index].id,
                                      jobStatus: applied == index
                                          ? tr('application_under_process')
                                          : matchingJobsResp?.data
                                              ?.matchingJobs?[index].jobStatus,
                                      jobStatusNumeric: int.parse(
                                          '${matchingJobsResp?.data?.matchingJobs?[index].jobId}'),
                                      vacancy: matchingJobsResp?.data
                                          ?.matchingJobs?[index].numOfVacancy,
                                      maxExperience: matchingJobsResp?.data
                                          ?.matchingJobs?[index].maxExperience
                                          ?.toDouble(),
                                      minExperience: matchingJobsResp?.data
                                          ?.matchingJobs?[index].minExperience
                                          ?.toDouble(),
                                      landingPageUrl: matchingJobsResp?.data
                                          ?.matchingJobs?[index].landingPageUrl,
                                    )));
                              },
                              child: Container(
                                padding: EdgeInsets.only(left: 5.0, right: 5.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                        '${matchingJobsResp?.data?.matchingJobs?[index].name ?? ''}',
                                        style: Styles.bold(
                                            size: 16,
                                            color:
                                                ColorConstants.HEADING_TITLE)),
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          top: 6.0, right: 5.0),
                                      child: Text(
                                          '${matchingJobsResp?.data?.matchingJobs?[index].organizedBy ?? ''}',
                                          style: Styles.regular(
                                              size: 13,
                                              color: ColorConstants
                                                  .SUB_HEADING_TITLE)),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 5.0),
                                      child: Row(
                                        children: [
                                          matchingJobsResp?.data
                                                          ?.matchingJobs !=
                                                      null &&
                                                  matchingJobsResp
                                                          ?.data
                                                          ?.matchingJobs
                                                          ?.length !=
                                                      0
                                              ? SizedBox(
                                                  child: Row(
                                                    children: [
                                                      Icon(Icons.work_outline,
                                                          size: 16,
                                                          color: ColorConstants
                                                              .BODY_TEXT),
                                                      Padding(
                                                        padding: EdgeInsets.only(
                                                            left: Utility()
                                                                    .isRTL(
                                                                        context)
                                                                ? 0
                                                                : 5.0,
                                                            right: Utility()
                                                                    .isRTL(
                                                                        context)
                                                                ? 5.0
                                                                : 0.0),
                                                        child: Text(
                                                            '${tr('exp')}: ',
                                                            style: Styles.regular(
                                                                size: 13,
                                                                color: ColorConstants
                                                                    .BODY_TEXT)),
                                                      ),
                                                      /*Text(
                                                   '${widget.matchingJobsResp![index]!.experience != null ? widget.matchingJobsResp![index]!.experience : "0"} ${tr('yrs')}',
                                                   style: Styles.regular(
                                                       size: 13,
                                                       color: ColorConstants
                                                           .BODY_TEXT)),*/
                                                      Text(
                                                          '${matchingJobsResp?.data?.matchingJobs![index].minExperience != null ? matchingJobsResp?.data?.matchingJobs![index].minExperience : "0"}' +
                                                              '-${matchingJobsResp?.data?.matchingJobs![index].maxExperience != null ? matchingJobsResp?.data?.matchingJobs![index].maxExperience : "0"} ${tr('yrs')} ',
                                                          style: Styles.regular(
                                                              size: 12,
                                                              color:
                                                                  ColorConstants
                                                                      .GREY_3)),
                                                    ],
                                                  ),
                                                )
                                              : SizedBox(height: 20),
                                          matchingJobsResp
                                                          ?.data
                                                          ?.matchingJobs?[index]
                                                          .workAddress !=
                                                      null &&
                                                  matchingJobsResp
                                                          ?.data
                                                          ?.matchingJobs?[index]
                                                          .workAddress !=
                                                      ""
                                              ? Row(
                                                  children: [
                                                    Padding(
                                                      padding: EdgeInsets.only(
                                                          left: Utility().isRTL(
                                                                  context)
                                                              ? 0
                                                              : 20.0,
                                                          right: Utility()
                                                                  .isRTL(
                                                                      context)
                                                              ? 20.0
                                                              : 0.0),
                                                      child: Icon(
                                                        Icons
                                                            .location_on_outlined,
                                                        size: 16,
                                                        color: ColorConstants
                                                            .BODY_TEXT,
                                                      ),
                                                    ),
                                                    Text(
                                                        '${matchingJobsResp?.data?.matchingJobs![index].workAddress ?? ''}',
                                                        style: Styles.regular(
                                                            size: 13,
                                                            color: ColorConstants
                                                                .BODY_TEXT)),
                                                  ],
                                                )
                                              : SizedBox(),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: matchingJobsProvider
                                            .list[index]!.jobStatus ==
                                        null ||
                                    matchingJobsProvider
                                            .list[index]!.jobStatus ==
                                        "" ||
                                    matchingJobsProvider.list[index]!.jobStatus
                                            ?.toLowerCase() ==
                                        'pending'
                                ? InkWell(
                                    onTap: () {
                                      // applied = index;
                                      matchingJobsProvider
                                          .updateAppliedStatus(index);
                                      jobApply(
                                          int.parse(
                                              '${matchingJobsResp?.data?.matchingJobs?[index].programId}'),
                                          1);
                                      _onLoadingForJob();
                                    },
                                    child: Container(
                                      padding: EdgeInsets.only(left: 0.0),
                                      child: GradientText(
                                        matchingJobsProvider.list[index]
                                                        ?.jobStatus ==
                                                    null ||
                                                matchingJobsProvider.list[index]
                                                        ?.jobStatus ==
                                                    "" ||
                                                matchingJobsProvider
                                                        .list[index]?.jobStatus
                                                        ?.toLowerCase() ==
                                                    'pending'
                                            ? tr('apply_button')
                                            : tr('applied'),
                                        style: Styles.bold(size: 14),
                                        colors: [
                                          matchingJobsProvider.list[index]
                                                          ?.jobStatus ==
                                                      null ||
                                                  matchingJobsProvider
                                                          .list[index]
                                                          ?.jobStatus ==
                                                      "" ||
                                                  matchingJobsProvider
                                                          .list[index]
                                                          ?.jobStatus
                                                          ?.toLowerCase() ==
                                                      'pending'
                                              ? ColorConstants().gradientLeft()
                                              : ColorConstants.GREEN,
                                          matchingJobsProvider.list[index]
                                                          ?.jobStatus ==
                                                      null ||
                                                  matchingJobsProvider
                                                          .list[index]
                                                          ?.jobStatus ==
                                                      "" ||
                                                  matchingJobsProvider
                                                          .list[index]
                                                          ?.jobStatus
                                                          ?.toLowerCase() ==
                                                      'pending'
                                              ? ColorConstants().gradientRight()
                                              : ColorConstants.GREEN,
                                        ],
                                      ),
                                    ),
                                  )
                                : Padding(
                                    padding:
                                        const EdgeInsets.only(bottom: 20.0),
                                    child: Text(
                                      'applied',
                                      style: Styles.bold(
                                          color: Colors.green, size: 12),
                                    ).tr(),
                                  ),
                          ),
                        ],
                      ),
                    ),
                  );
                }));
  }

  Widget _jobBasedYourListCard() {
    return Container(
      color: ColorConstants.WHITE,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(13.0),
            child: Text('jobs_based_portfolio',
                    style: Styles.bold(
                        size: 14, color: ColorConstants.HEADING_TITLE))
                .tr(),
          ),
          Divider(
            height: 1,
            color: ColorConstants.DIVIDER_COLOR_1,
          ),
          allJobListResponse?.data != null ? renderJobList(4) : SizedBox(),
        ],
      ),
    );
  }

  Widget _step2Card() {
    return Container(
      color: ColorConstants.WHITE,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //< 4
          /*if (int.parse('${allJobListResponse?.data?.length}') > 4) ...[
            renderJobSecondPositionList((allJobListResponse?.data!.length)! - 4),
          ],*/

          //TODO: change for show all data on list 7-jun-24
          renderJobSecondPositionList((allJobListResponse?.data!.length)!),
        ],
      ),
    );
  }

  Widget _step3Card() {
    return Container(
      color: ColorConstants.WHITE,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (int.parse('${allJobListResponse?.data?.length}') > 4) ...[
            renderJobThirdPositionList((allJobListResponse?.data!.length)! - 4),
          ],
        ],
      ),
    );
  }

  Widget _recommendedOpportunitiesListCard() {
    return recommendedJobOpportunities?.data!.length != 0
        ? Container(
            margin: EdgeInsets.only(top: 10),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 10.0),
                      child: Icon(CupertinoIcons.star_fill,
                          color: ColorConstants.YELLOW, size: 18),
                    ),
                    Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 10,
                        ),
                        child: Text(
                          'recommended_opportunities',
                          style:
                              Styles.bold(color: Color(0xff0E1638), size: 14),
                        ).tr()),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Container(
                    height: 300,
                    child: recommendedJobOpportunities?.data?.length != 0
                        ? ListView.builder(
                            itemCount:
                                recommendedJobOpportunities?.data?.length,
                            scrollDirection: Axis.horizontal,
                            itemBuilder: (BuildContext context, int index) {
                              return InkWell(
                                  onTap: () {
                                    Navigator.push(
                                        context,
                                        NextPageRoute(JobDetailsPage(
                                          title: recommendedJobOpportunities
                                              ?.data![index]!.name,
                                          description:
                                              recommendedJobOpportunities
                                                  ?.data![index]!.description,
                                          location: recommendedJobOpportunities
                                              ?.data![index]!.location,
                                          skillNames:
                                              recommendedJobOpportunities
                                                  ?.data![index]!.skillNames,
                                          companyName:
                                              recommendedJobOpportunities
                                                  ?.data![index]!.organizedBy,
                                          domain: recommendedJobOpportunities
                                              ?.data![index]!.domainName,
                                          companyThumbnail:
                                              recommendedJobOpportunities
                                                  ?.data![index]!.image,
                                          experience:
                                              recommendedJobOpportunities
                                                  ?.data![index]!.experience,
                                          //jobListDetails: jobList,
                                          id: recommendedJobOpportunities
                                              ?.data![index]!.id,
                                          jobStatus: recommendedJobOpportunities
                                              ?.data![index]!.jobStatus,
                                          jobStatusNumeric:
                                              recommendedJobOpportunities
                                                  ?.data![index]!
                                                  .jobStatusNumeric,
                                          vacancy: recommendedJobOpportunities
                                              ?.data![index]!.vacancy,
                                          minExperience:
                                              recommendedJobOpportunities
                                                  ?.data![index]!.minExperience,
                                          maxExperience:
                                              recommendedJobOpportunities
                                                  ?.data![index]!.maxExperience,
                                          landingPageUrl:
                                              recommendedJobOpportunities
                                                  ?.data![index]!
                                                  .landingPageUrl,
                                        )));
                                  },
                                  child: Container(
                                    width: min(width(context), 480) * 0.7,
                                    decoration: BoxDecoration(
                                        color: ColorConstants.WHITE,
                                        borderRadius: BorderRadius.circular(10),
                                        border: Border.all(
                                            color: ColorConstants.List_Color)),
                                    margin: EdgeInsets.all(8),
                                    child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 8.0,
                                                right: 8.0,
                                                top: 8.0,
                                                bottom: 8.0),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                CachedNetworkImage(
                                                  imageUrl:
                                                      '${recommendedJobOpportunities?.data![index]!.image}',
                                                  width: 70,
                                                  height: 60,
                                                  errorWidget:
                                                      (context, url, error) =>
                                                          SvgPicture.asset(
                                                    'assets/images/exp_emp.svg',
                                                  ),
                                                  fit: BoxFit.fill,
                                                ),
                                                SizedBox(
                                                  height: 46,
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            top: 7.0),
                                                    child: Text(
                                                      '${recommendedJobOpportunities?.data![index]!.name ?? ''}',
                                                      maxLines: 2,
                                                      style: Styles.bold(
                                                          color:
                                                              Color(0xff0E1638),
                                                          size: 16),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                  height: 5,
                                                ),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  children: [
                                                    Flexible(
                                                      child: Text(
                                                        '${recommendedJobOpportunities?.data![index]!.organizedBy ?? ''}',
                                                        maxLines: 1,
                                                        softWrap: true,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        style: Styles.regular(
                                                            color: ColorConstants
                                                                .SUB_HEADING_TITLE,
                                                            size: 13),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(
                                                  height: 10,
                                                ),
                                                recommendedJobOpportunities
                                                                ?.data![index]!
                                                                .location !=
                                                            null ||
                                                        recommendedJobOpportunities
                                                                ?.data![index]!
                                                                .location
                                                                ?.isNotEmpty ==
                                                            true
                                                    ? Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(top: 7.0),
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      left:
                                                                          1.0),
                                                              child: Icon(
                                                                Icons
                                                                    .location_on_outlined,
                                                                size: 16,
                                                                color: ColorConstants
                                                                    .BODY_TEXT,
                                                              ),
                                                            ),
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      left:
                                                                          8.0),
                                                              child: Text(
                                                                '${recommendedJobOpportunities?.data![index]!.location}',
                                                                style: Styles.regular(
                                                                    color: ColorConstants
                                                                        .BODY_TEXT,
                                                                    size: 13),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      )
                                                    : SizedBox(
                                                        height: 25,
                                                      ),
                                                SizedBox(
                                                  height: 7,
                                                ),
                                                recommendedJobOpportunities
                                                                ?.data![index]!
                                                                .maxExperience !=
                                                            null ||
                                                        recommendedJobOpportunities
                                                                ?.data![index]!
                                                                .minExperience !=
                                                            null
                                                    ? SizedBox(
                                                        height: 20,
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Icon(
                                                                Icons
                                                                    .work_outline,
                                                                size: 16,
                                                                color: ColorConstants
                                                                    .BODY_TEXT),
                                                            Padding(
                                                              padding:
                                                                  EdgeInsets
                                                                      .only(
                                                                left: Utility()
                                                                        .isRTL(
                                                                            context)
                                                                    ? 0
                                                                    : 5.0,
                                                                right: Utility()
                                                                        .isRTL(
                                                                            context)
                                                                    ? 5.0
                                                                    : 0.0,
                                                              ),
                                                              child: Text(
                                                                  '${tr('exp')}: ',
                                                                  style: Styles.regular(
                                                                      size: 12,
                                                                      color: ColorConstants
                                                                          .BODY_TEXT)),
                                                            ),
                                                            /*Text(
                                                          '${recommendedJobOpportunities?.data![index]!.experience?? '0'} ${tr('yrs')}',
                                                          style: Styles.regular(
                                                              size: 12,
                                                              color: ColorConstants
                                                                  .BODY_TEXT)),*/
                                                            Text(
                                                                '${recommendedJobOpportunities?.data![index]?.minExperience != null ? recommendedJobOpportunities?.data![index]?.minExperience : "0"}' +
                                                                    '-${recommendedJobOpportunities?.data![index]?.maxExperience != null ? recommendedJobOpportunities?.data![index]?.maxExperience : "0"} ${tr('yrs')} ',
                                                                style: Styles.regular(
                                                                    size: 12,
                                                                    color: ColorConstants
                                                                        .GREY_3)),
                                                          ],
                                                        ),
                                                      )
                                                    : SizedBox(height: 20),
                                                SizedBox(
                                                  height: 20,
                                                ),
                                                recommendedJobOpportunities
                                                                ?.data![index]!
                                                                .jobStatus ==
                                                            null ||
                                                        recommendedJobOpportunities
                                                                ?.data![index]!
                                                                .jobStatus ==
                                                            ""
                                                    ? InkWell(
                                                        onTap: () {
                                                          jobApply(
                                                              int.parse(
                                                                  '${recommendedJobOpportunities?.data![index]!.id}'),
                                                              1);
                                                          _onLoadingForJob();

                                                          setState(() {
                                                            recommendedJobOpportunities
                                                                ?.data!
                                                                .removeAt(
                                                                    index);
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 45,
                                                          width: MediaQuery.of(
                                                                  context)
                                                              .size
                                                              .width,
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        50),
                                                            gradient:
                                                                LinearGradient(
                                                                    colors: [
                                                                  ColorConstants
                                                                      .DASHBOARD_APPLY_COLOR,
                                                                  ColorConstants
                                                                      .DASHBOARD_APPLY_COLOR,
                                                                ]),
                                                          ),
                                                          child: Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .center,
                                                            children: [
                                                              Text(
                                                                'apply_button',
                                                                style: TextStyle(
                                                                    color: Colors
                                                                        .white,
                                                                    fontSize:
                                                                        16,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold),
                                                              ).tr(),
                                                            ],
                                                          ),
                                                        ),
                                                      )
                                                    : Center(
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .only(
                                                                  bottom: 20.0),
                                                          child: Text(
                                                            'applied',
                                                            style: Styles.bold(
                                                                color: Colors
                                                                    .green,
                                                                size: 14),
                                                          ).tr(),
                                                        ),
                                                      ),
                                              ],
                                            ),
                                          ),
                                        ]),
                                  ));
                            })
                        : SizedBox(),
                  ),
                )
              ],
            ),
          )
        : SizedBox();
  }

  Widget renderJobList(int position) {
    return Consumer<CompetitionResponseProvider>(
        builder: (context, competitionProvider, child) => ListView.builder(
            itemCount: (allJobListResponse?.data?.length)! < position
                ? allJobListResponse?.data?.length
                : position,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (BuildContext context, int index) {
              return Column(
                children: [
                  Container(
                    width: double.infinity,
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Container(
                                padding: EdgeInsets.only(
                                  right: 10.0,
                                ),
                                child: allJobListResponse
                                            ?.data![index]!.image !=
                                        null
                                    ? Image.network(
                                        '${allJobListResponse?.data![index]!.image}')
                                    : Image.asset('assets/images/pb_2.png')),
                          ),
                          Expanded(
                            flex: 9,
                            child: InkWell(
                              onTap: () {
                                FirebaseAnalytics.instance.logEvent(
                                    name: 'careers_job_click',
                                    parameters: {
                                      "job_name": allJobListResponse
                                              ?.data![index]!.name ??
                                          '',
                                    });
                                Navigator.push(
                                    context,
                                    NextPageRoute(JobDetailsPage(
                                      title: allJobListResponse
                                          ?.data![index]!.name,
                                      description: allJobListResponse
                                          ?.data![index]!.description,
                                      location: allJobListResponse
                                          ?.data![index]!.location,
                                      skillNames: allJobListResponse
                                          ?.data![index]!.skillNames,
                                      companyName: allJobListResponse
                                          ?.data![index]!.organizedBy,
                                      domain: allJobListResponse
                                          ?.data![index]!.domainName,
                                      companyThumbnail: allJobListResponse
                                          ?.data![index]!.image,
                                      experience: allJobListResponse
                                          ?.data![index]!.experience,
                                      id: allJobListResponse?.data![index]!.id,
                                      jobStatus: applied == index
                                          ? tr('application_under_process')
                                          : allJobListResponse
                                              ?.data![index]!.jobStatus,
                                      jobStatusNumeric: allJobListResponse
                                          ?.data![index]!.jobStatusNumeric,
                                      vacancy: allJobListResponse
                                          ?.data![index]!.vacancy,
                                      maxExperience: allJobListResponse
                                          ?.data![index]!.maxExperience,
                                      minExperience: allJobListResponse
                                          ?.data![index]!.minExperience,
                                      landingPageUrl: allJobListResponse
                                          ?.data![index]!.landingPageUrl,
                                    )));
                              },
                              child: Container(
                                padding: EdgeInsets.only(left: 5.0, right: 5.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                        '${allJobListResponse?.data![index]!.name ?? ''}',
                                        style: Styles.bold(
                                            size: 16,
                                            color:
                                                ColorConstants.HEADING_TITLE)),
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          top: 6.0, right: 5.0),
                                      child: Text(
                                          '${allJobListResponse?.data![index]!.organizedBy ?? ''}',
                                          style: Styles.regular(
                                              size: 13,
                                              color: ColorConstants
                                                  .SUB_HEADING_TITLE)),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 5.0),
                                      child: Row(
                                        children: [
                                          recommendedJobOpportunities?.data !=
                                                      null &&
                                                  recommendedJobOpportunities
                                                          ?.data?.length !=
                                                      0
                                              ? SizedBox(
                                                  child: Row(
                                                    children: [
                                                      Icon(Icons.work_outline,
                                                          size: 16,
                                                          color: ColorConstants
                                                              .BODY_TEXT),
                                                      Padding(
                                                        padding: EdgeInsets.only(
                                                            left: Utility()
                                                                    .isRTL(
                                                                        context)
                                                                ? 0
                                                                : 5.0,
                                                            right: Utility()
                                                                    .isRTL(
                                                                        context)
                                                                ? 5.0
                                                                : 0.0),
                                                        child: Text(
                                                            '${tr('exp')}: ',
                                                            style: Styles.regular(
                                                                size: 13,
                                                                color: ColorConstants
                                                                    .BODY_TEXT)),
                                                      ),
                                                      /*Text(
                                                   '${allJobListResponse?.data![index]!.experience != null ? allJobListResponse?.data![index]!.experience : "0"} ${tr('yrs')}',
                                                   style: Styles.regular(
                                                       size: 13,
                                                       color: ColorConstants
                                                           .BODY_TEXT)),*/
                                                      Text(
                                                          '${allJobListResponse?.data?[index]?.minExperience != null ? allJobListResponse?.data![index]?.minExperience : "0"}' +
                                                              '-${allJobListResponse?.data?[index]?.maxExperience != null ? allJobListResponse?.data![index]?.maxExperience : "0"} ${tr('yrs')} ',
                                                          style: Styles.regular(
                                                              size: 12,
                                                              color:
                                                                  ColorConstants
                                                                      .GREY_3)),
                                                    ],
                                                  ),
                                                )
                                              : SizedBox(height: 20),
                                          allJobListResponse?.data![index]!
                                                      .location !=
                                                  null
                                              ? Row(
                                                  children: [
                                                    Padding(
                                                      padding: EdgeInsets.only(
                                                          left: Utility().isRTL(
                                                                  context)
                                                              ? 0
                                                              : 20.0,
                                                          right: Utility()
                                                                  .isRTL(
                                                                      context)
                                                              ? 20.0
                                                              : 0.0),
                                                      child: Icon(
                                                        Icons
                                                            .location_on_outlined,
                                                        size: 16,
                                                        color: ColorConstants
                                                            .BODY_TEXT,
                                                      ),
                                                    ),
                                                    Text(
                                                        '${allJobListResponse?.data![index]!.location}',
                                                        style: Styles.regular(
                                                            size: 13,
                                                            color: ColorConstants
                                                                .BODY_TEXT)),
                                                  ],
                                                )
                                              : SizedBox(),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child:
                                allJobListResponse?.data![index]!.jobStatus ==
                                            null ||
                                        allJobListResponse
                                                ?.data![index]!.jobStatus ==
                                            ""
                                    ? InkWell(
                                        onTap: () {
                                          FirebaseAnalytics.instance.logEvent(
                                              name: 'careers_job_apply',
                                              parameters: {
                                                "job_name": allJobListResponse
                                                        ?.data![index]!.name ??
                                                    '',
                                              });
                                          competitionProvider
                                              .updateAppliedStatus(index);
                                          jobApply(
                                              int.parse(
                                                  '${allJobListResponse?.data![index]!.id}'),
                                              1);
                                          _onLoadingForJob();
                                        },
                                        child: Container(
                                          padding: EdgeInsets.only(left: 0.0),
                                          child: GradientText(
                                            tr('apply_button'),
                                            style: Styles.bold(size: 14),
                                            colors: [
                                              ColorConstants().gradientLeft(),
                                              ColorConstants().gradientRight(),
                                            ],
                                          ),
                                        ),
                                      )
                                    : Center(
                                        child: Text(
                                          'applied',
                                          style: Styles.bold(
                                              color: Colors.green, size: 12),
                                        ).tr(),
                                      ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: ColorConstants.DIVIDER_COLOR_1,
                    thickness: 1,
                  ),
                ],
              );
            }));
  }

  Widget renderJobSecondPositionList(int position) {
    return Consumer<CompetitionResponseProvider>(
        builder: (context, competitionProvider, child) => ListView.builder(
            itemCount: position < 4 ? position : 4,
            //itemCount: position,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (BuildContext context, int index) {
              //int newIndex = index + 4;
              int newIndex = index;
              return Column(
                children: [
                  Container(
                    width: double.infinity,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8.0, vertical: 6),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Container(
                                padding: EdgeInsets.only(
                                  right: 10.0,
                                ),
                                child: allJobListResponse
                                            ?.data![newIndex]!.image !=
                                        null
                                    ? Image.network(
                                        '${allJobListResponse?.data![newIndex]!.image}',
                                        height: 80,
                                        width: 80,
                                      )
                                    : Image.asset('assets/images/pb_2.png')),
                          ),
                          Expanded(
                            flex: 9,
                            child: InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    NextPageRoute(JobDetailsPage(
                                      title: allJobListResponse
                                          ?.data![newIndex]!.name,
                                      description: allJobListResponse
                                          ?.data![newIndex]!.description,
                                      location: allJobListResponse
                                          ?.data![newIndex]!.location,
                                      skillNames: allJobListResponse
                                          ?.data![newIndex]!.skillNames,
                                      companyName: allJobListResponse
                                          ?.data![newIndex]!.organizedBy,
                                      domain: allJobListResponse
                                          ?.data![newIndex]!.domainName,
                                      companyThumbnail: allJobListResponse
                                          ?.data![newIndex]!.image,
                                      experience: allJobListResponse
                                          ?.data![newIndex]!.experience,
                                      id: allJobListResponse
                                          ?.data![newIndex]!.id,
                                      jobStatus: competitionProvider
                                                  .list[index]?.jobStatus !=
                                              null
                                          ? tr('application_under_process')
                                          : allJobListResponse
                                              ?.data![newIndex]!.jobStatus,
                                      jobStatusNumeric: allJobListResponse
                                          ?.data![newIndex]!.jobStatusNumeric,
                                      vacancy: allJobListResponse
                                          ?.data![newIndex]!.vacancy,
                                      minExperience: allJobListResponse
                                          ?.data![newIndex]!.minExperience,
                                      maxExperience: allJobListResponse
                                          ?.data![newIndex]!.maxExperience,
                                      landingPageUrl: allJobListResponse
                                          ?.data![index]!.landingPageUrl,
                                    )));
                              },
                              child: Container(
                                padding: EdgeInsets.only(left: 5.0, right: 5.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                        '${allJobListResponse?.data![newIndex]!.name}',
                                        style: Styles.bold(
                                            size: 16,
                                            color:
                                                ColorConstants.HEADING_TITLE)),
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          top: 6.0, right: 5.0),
                                      child: Text(
                                          '${allJobListResponse?.data![newIndex]!.organizedBy ?? ''}',
                                          style: Styles.regular(
                                              size: 13,
                                              color: ColorConstants
                                                  .SUB_HEADING_TITLE)),
                                    ),
                                    allJobListResponse?.data![newIndex]!
                                                        .maxExperience !=
                                                    null &&
                                                allJobListResponse
                                                        ?.data![newIndex]!
                                                        .maxExperience ==
                                                    0.0 ||
                                            allJobListResponse?.data![newIndex]!
                                                        .minExperience !=
                                                    null &&
                                                allJobListResponse
                                                        ?.data![newIndex]!
                                                        .minExperience !=
                                                    0.0
                                        ? Padding(
                                            padding:
                                                const EdgeInsets.only(top: 5.0),
                                            child: Row(
                                              children: [
                                                // Image.asset(
                                                //     'assets/images/jobicon.png'),
                                                Icon(Icons.work_outline,
                                                    size: 16,
                                                    color: ColorConstants
                                                        .BODY_TEXT),
                                                Padding(
                                                  padding: EdgeInsets.only(
                                                      left: Utility()
                                                              .isRTL(context)
                                                          ? 0
                                                          : 5.0,
                                                      right: Utility()
                                                              .isRTL(context)
                                                          ? 5.0
                                                          : 0.0),
                                                  child: Text('${tr('exp')}: ',
                                                      style: Styles.regular(
                                                          size: 13,
                                                          color: ColorConstants
                                                              .BODY_TEXT)),
                                                ),
                                                /*Text(
                                              '${allJobListResponse?.data![newIndex]!.experience != null ? allJobListResponse?.data![newIndex]!.experience : "0"} ${tr('yrs')}',
                                              style: Styles.regular(
                                                  size: 13,
                                                  color: ColorConstants
                                                      .BODY_TEXT)),*/
                                                Text(
                                                    '${allJobListResponse?.data?[newIndex]?.minExperience ?? ''}' +
                                                        '-${allJobListResponse?.data?[newIndex]?.maxExperience ?? ''} ${tr('yrs')} ',
                                                    style: Styles.regular(
                                                        size: 12,
                                                        color: ColorConstants
                                                            .GREY_3)),

                                                if (allJobListResponse
                                                            ?.data?[newIndex]!
                                                            .location !=
                                                        "" &&
                                                    allJobListResponse
                                                            ?.data?[newIndex]!
                                                            .location !=
                                                        null) ...[
                                                  Row(
                                                    children: [
                                                      Padding(
                                                        padding: EdgeInsets.only(
                                                            left: Utility()
                                                                    .isRTL(
                                                                        context)
                                                                ? 0
                                                                : 20.0,
                                                            right: Utility()
                                                                    .isRTL(
                                                                        context)
                                                                ? 20.0
                                                                : 0.0),
                                                        child: Icon(
                                                          Icons
                                                              .location_on_outlined,
                                                          size: 16,
                                                          color: ColorConstants
                                                              .BODY_TEXT,
                                                        ),
                                                      ),
                                                      Text(
                                                          '${allJobListResponse?.data?[newIndex]?.location ?? ''}',
                                                          style: Styles.regular(
                                                              size: 13,
                                                              color: ColorConstants
                                                                  .BODY_TEXT)),
                                                    ],
                                                  ),
                                                ],
                                              ],
                                            ),
                                          )
                                        : SizedBox(height: 20),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: competitionProvider
                                            .list[newIndex]!.jobStatus ==
                                        null ||
                                    competitionProvider
                                            .list[newIndex]!.jobStatus ==
                                        ""
                                ? InkWell(
                                    onTap: () {
                                      // applied = index;
                                      competitionProvider
                                          .updateAppliedStatus(newIndex);
                                      jobApply(
                                          int.parse(
                                              '${allJobListResponse?.data![newIndex]!.id}'),
                                          1);
                                      _onLoadingForJob();
                                    },
                                    child: Container(
                                      padding: EdgeInsets.only(left: 0.0),
                                      child: GradientText(
                                        competitionProvider.list[newIndex]
                                                    ?.jobStatus ==
                                                null
                                            ? tr('apply_button')
                                            : tr('applied'),
                                        style: Styles.bold(size: 14),
                                        colors: [
                                          competitionProvider.list[newIndex]
                                                      ?.jobStatus ==
                                                  null
                                              ? ColorConstants().gradientLeft()
                                              : ColorConstants.GREEN,
                                          competitionProvider.list[newIndex]
                                                      ?.jobStatus ==
                                                  null
                                              ? ColorConstants().gradientRight()
                                              : ColorConstants.GREEN,
                                        ],
                                      ),
                                    ),
                                  )
                                : Padding(
                                    padding:
                                        const EdgeInsets.only(bottom: 20.0),
                                    child: Text(
                                      'applied',
                                      style: Styles.bold(
                                          color: Colors.green, size: 12),
                                    ).tr(),
                                  ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: ColorConstants.DIVIDER_COLOR_1,
                    thickness: 1,
                  ),
                ],
              );
            }));
  }

  Widget renderJobThirdPositionList(int position) {
    return Consumer<CompetitionResponseProvider>(
        builder: (context, competitionProvider, child) => ListView.builder(
            itemCount: position,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (BuildContext context, int index) {
              int newIndex = index + 4;

              return Column(
                children: [
                  Container(
                    width: double.infinity,
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Container(
                                padding: EdgeInsets.only(
                                  right: 10.0,
                                ),
                                child: allJobListResponse
                                            ?.data![newIndex]!.image !=
                                        null
                                    ? Image.network(
                                        '${allJobListResponse?.data![newIndex]!.image}',
                                        height: 80,
                                        width: 80,
                                      )
                                    : Image.asset('assets/images/pb_2.png')),
                          ),
                          Expanded(
                            flex: 9,
                            child: InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    NextPageRoute(JobDetailsPage(
                                      title: allJobListResponse
                                          ?.data![newIndex]!.name,
                                      description: allJobListResponse
                                          ?.data![newIndex]!.description,
                                      location: allJobListResponse
                                          ?.data![newIndex]!.location,
                                      skillNames: allJobListResponse
                                          ?.data![newIndex]!.skillNames,
                                      companyName: allJobListResponse
                                              ?.data![newIndex]!.organizedBy ??
                                          '',
                                      domain: allJobListResponse
                                          ?.data![newIndex]!.domainName,
                                      companyThumbnail: allJobListResponse
                                          ?.data![newIndex]!.image,
                                      experience: allJobListResponse
                                          ?.data![newIndex]!.experience,
                                      jobStatusNumeric: allJobListResponse
                                          ?.data![newIndex]!.jobStatusNumeric,
                                      //jobListDetails: jobList,
                                      id: allJobListResponse
                                          ?.data![newIndex]!.id,
                                      jobStatus: applied == index
                                          ? tr('application_under_process')
                                          : allJobListResponse
                                              ?.data![newIndex]!.jobStatus,
                                      vacancy: allJobListResponse
                                          ?.data![newIndex]!.vacancy,
                                      minExperience: allJobListResponse
                                          ?.data![newIndex]!.minExperience,
                                      maxExperience: allJobListResponse
                                          ?.data![newIndex]!.maxExperience,
                                      landingPageUrl: allJobListResponse
                                          ?.data![index]!.landingPageUrl,
                                    )));
                              },
                              child: Container(
                                padding: EdgeInsets.only(left: 5.0, right: 5.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                        '${allJobListResponse?.data![newIndex]!.name}',
                                        style: Styles.bold(
                                            size: 14,
                                            color: ColorConstants.BLACK)),
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          top: 6.0, right: 5.0),
                                      child: Text(
                                          '${allJobListResponse?.data![newIndex]!.organizedBy ?? ''}',
                                          style: Styles.regular(
                                              size: 12,
                                              color: Color(0xff3E4245))),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 5.0),
                                      child: Row(
                                        children: [
                                          allJobListResponse?.data![newIndex]!
                                                              .minExperience !=
                                                          null &&
                                                      allJobListResponse
                                                              ?.data![newIndex]!
                                                              .minExperience !=
                                                          0.0 ||
                                                  allJobListResponse
                                                              ?.data![newIndex]!
                                                              .maxExperience !=
                                                          null &&
                                                      allJobListResponse
                                                              ?.data![newIndex]!
                                                              .maxExperience !=
                                                          0.0
                                              ? SizedBox(
                                                  child: Row(
                                                    children: [
                                                      Icon(Icons.work_outline,
                                                          size: 16,
                                                          color: ColorConstants
                                                              .BODY_TEXT),
                                                      Padding(
                                                        padding: EdgeInsets.only(
                                                            left: Utility()
                                                                    .isRTL(
                                                                        context)
                                                                ? 0
                                                                : 5.0,
                                                            right: Utility()
                                                                    .isRTL(
                                                                        context)
                                                                ? 5.0
                                                                : 0.0),
                                                        child: Text(
                                                            '${tr('exp')}: ',
                                                            style: Styles.regular(
                                                                size: 12,
                                                                color: ColorConstants
                                                                    .BODY_TEXT)),
                                                      ),
                                                      Text(
                                                          '${allJobListResponse?.data?[newIndex]?.minExperience != 0.0 ? allJobListResponse?.data![newIndex]?.minExperience : "0"}' +
                                                              '-${allJobListResponse?.data![newIndex]?.maxExperience != 0.0 ? allJobListResponse?.data![newIndex]?.maxExperience : "0"} ${tr('yrs')} ',
                                                          style: Styles.regular(
                                                              size: 12,
                                                              color:
                                                                  ColorConstants
                                                                      .GREY_3)),
                                                    ],
                                                  ),
                                                )
                                              : SizedBox(
                                                  height: 20,
                                                ),
                                          allJobListResponse?.data![newIndex]!
                                                          .location !=
                                                      null &&
                                                  allJobListResponse
                                                          ?.data![newIndex]!
                                                          .location !=
                                                      ""
                                              ? Row(
                                                  children: [
                                                    Padding(
                                                      padding: EdgeInsets.only(
                                                          left: Utility().isRTL(
                                                                  context)
                                                              ? 0
                                                              : 20.0,
                                                          right: Utility()
                                                                  .isRTL(
                                                                      context)
                                                              ? 20.0
                                                              : 0.0),
                                                      child: Icon(
                                                        Icons
                                                            .location_on_outlined,
                                                        size: 16,
                                                        color: ColorConstants
                                                            .BODY_TEXT,
                                                      ),
                                                    ),
                                                    Text(
                                                        '${allJobListResponse?.data![newIndex]!.location}',
                                                        style: Styles.regular(
                                                            size: 12,
                                                            color: ColorConstants
                                                                .BODY_TEXT)),
                                                  ],
                                                )
                                              : SizedBox(),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child:
                                competitionProvider.list[newIndex]!.jobStatus ==
                                            null ||
                                        competitionProvider
                                                .list[newIndex]!.jobStatus ==
                                            ""
                                    ? InkWell(
                                        onTap: () {
                                          competitionProvider
                                              .updateAppliedStatus(newIndex);
                                          jobApply(
                                              int.parse(
                                                  '${allJobListResponse?.data![newIndex]!.id}'),
                                              1);
                                          _onLoadingForJob();
                                        },
                                        child: Container(
                                          padding: EdgeInsets.only(left: 0.0),
                                          child: GradientText(
                                            tr('apply_button'),
                                            style: Styles.bold(size: 14),
                                            colors: [
                                              ColorConstants().gradientLeft(),
                                              ColorConstants().gradientRight(),
                                            ],
                                          ),
                                        ),
                                      )
                                    : Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 20.0),
                                        child: Text(
                                          'applied',
                                          style: Styles.bold(
                                              color: Colors.green, size: 12),
                                        ).tr(),
                                      ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: ColorConstants.GREY_3,
                    thickness: 1,
                  ),
                ],
              );
            }));
  }

  void _onLoadingForJob() {
    BuildContext? dialogContext;
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        dialogContext = context;
        return Dialog(
          child: Container(
            padding: EdgeInsets.only(left: 20, right: 10),
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: new Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                new CircularProgressIndicator(
                  color: Colors.blue,
                ),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10.0),
                    child: new Text("${tr('job_apply')}..."),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
    new Future.delayed(new Duration(seconds: 2), () {
      Get.rawSnackbar(
        messageText: Text(
          'application_submitted',
          style: Styles.regular(size: 14, color: ColorConstants.WHITE),
        ).tr(),
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: ColorConstants.BLACK,
        borderRadius: 4,
        duration: Duration(seconds: 3),
        boxShadows: [
          BoxShadow(
              color: Color(0xff898989).withValues(alpha: 0.1),
              offset: Offset(0, 4.0),
              blurRadius: 11)
        ],
      );
      Navigator.pop(dialogContext!); //pop dialog
    });
  }
}

class BlankPage extends StatelessWidget {
  const BlankPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(
                      right: 10.0,
                    ),
                    child: Image.asset('assets/images/blank.png'),
                  ),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 190,
                              decoration: BoxDecoration(
                                color: Colors.white,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 160,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: Color(0xffe6e4e6),
                                highlightColor: Color(0xffeaf0f3),
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: Color(0xffe6e4e6),
                                  highlightColor: Color(0xffeaf0f3),
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Divider(
          height: 1,
          color: ColorConstants.GREY_3,
        ),
        Container(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(
                      right: 10.0,
                    ),
                    child: Image.asset('assets/images/blank.png'),
                  ),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 190,
                              decoration: BoxDecoration(
                                color: Colors.white,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 160,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: Color(0xffe6e4e6),
                                highlightColor: Color(0xffeaf0f3),
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: Color(0xffe6e4e6),
                                  highlightColor: Color(0xffeaf0f3),
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
