import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/auth_response/competition_my_activity.dart';
import 'package:masterg/data/models/response/home_response/competition_content_list_resp.dart';
import 'package:masterg/data/models/response/home_response/competition_response.dart';
import 'package:masterg/data/models/response/home_response/domain_filter_list.dart';
import 'package:masterg/data/models/response/home_response/domain_list_response.dart';
import 'package:masterg/data/models/response/home_response/portfolio_competition_response.dart';
import 'package:masterg/data/models/response/home_response/top_score.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/singularis/app_drawer_page.dart';
import 'package:masterg/pages/singularis/competition/competition_detail.dart';
import 'package:masterg/pages/singularis/competition/competition_filter.dart';
import 'package:masterg/pages/singularis/competition/competition_my_activity.dart';
import 'package:masterg/pages/singularis/competition/competition_navigation/competition_my_activity.dart';
import 'package:masterg/pages/singularis/orgnization_leaderboard.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/str_to_time.dart';
import 'package:masterg/utils/utility.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

import '../../../utils/config.dart';
import '../../user_profile_page/portfolio_create_form/portfolio_page.dart';

class Competetion extends StatefulWidget {
  final bool? fromDasboard;
  const Competetion({Key? key, this.fromDasboard = false}) : super(key: key);

  @override
  _CompetetionState createState() => _CompetetionState();
}

List<int> selectedIdList = <int>[];
String seletedIds = '';
String selectedDifficulty = '';

enum EventStatus {
  upcoming,
  past,
  live,
}

class _CompetetionState extends State<Competetion>
    with SingleTickerProviderStateMixin {
  // List<MProgram>? competitionList;
  CompetitionResponse? competitionResponse, popularCompetitionResponse;
  CompetitionContentListResponse? eventCertificate;

  DomainListResponse? domainList;
  DomainFilterListResponse? domainFilterList;
  PortfolioCompetitionResponse? completedCompetition;
  CompetitionMyActivityResponse? myActivity;
  TopScoringResponse? userRank;
  bool? competitionLoading;
  bool? popularCompetitionLoading;
  MenuListProvider? menuProvider;
  bool tabSelected1 = true;
  bool tabSelected2 = false;
  bool webLoading = true;
  bool tabClickableEvent = false;
  bool tabClickableCmp = false;

  List<String> difficulty = ['Easy', 'Medium', 'Hard'];
  int selectedIndex = 0;
  var _scaffoldKey = new GlobalKey<ScaffoldState>();
  Map<String, bool> isSelected = new Map<String, bool>();
  // bool isSelectUpcomimg = false;
  // bool isSelectPast = false;
  // bool isSelectedLive = false;
  // bool isAllChecked = false;

  List<EventStatus> selectedFilter = [];

  bool _callMyActivity = false;

  late final AnimationController _controller;
  late final Animation<double> _animation;

  @override
  void initState() {
    topScoringUser();
    getCompetitionList(false, '', competitionType: 'event');
    getDomainList();

    //for live text animation
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    );

    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  List<String> listOfMonths = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ];

  void topScoringUser() {
    BlocProvider.of<HomeBloc>(context).add(TopScoringUserEvent(
        userId: Preference.getInt(Preference.USER_ID), skipCurrentUser: false));
  }

  void getCompetitionList(bool isFilter, String? ids,
      {String? competitionType, bool? callMyActivity}) {
    BlocProvider.of<HomeBloc>(context).add(CompetitionListEvent(
        isPopular: false,
        isFilter: isFilter,
        ids: ids,
        competitionType: competitionType,
        callMyActivity: callMyActivity));
  }

  void getDomainList() {
    BlocProvider.of<HomeBloc>(context).add(DomainListEvent());
  }

  void getFilterList(String ids) {
    BlocProvider.of<HomeBloc>(context).add(DomainFilterListEvent(ids: ids));
  }

  void getEventCertificate({int? competitionId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(CompetitionContentListEvent(competitionId: competitionId));
  }

  @override
  Widget build(BuildContext context) {
    double barThickness = MediaQuery.of(context).size.height * 0.012;
    double mobileWidth = MediaQuery.of(context).size.width - 50;
    double mobileHeight = MediaQuery.of(context).size.height;

    bool isrtl = Utility().isRTL(context);

    return Scaffold(
        key: _scaffoldKey,
        endDrawer: new AppDrawer(),
        body: BlocManager(
          initState: (BuildContext context) {},
          child: Consumer<MenuListProvider>(
              builder: (context, mp, child) => BlocListener<HomeBloc,
                      HomeState>(
                  listener: (context, state) {
                    if (state is CompetitionListState) {
                      _handlecompetitionListResponse(state);
                    }
                    if (state is DomainListState) {
                      handleDomainListResponse(state);
                    }

                    if (state is TopScoringUserState) {
                      handletopScoring(state);
                    }

                    if (state is AppJobListCompeState) {
                      _handlecompetitionDetails(state);
                    }
                    setState(() {
                      menuProvider = mp;
                    });
                  },
                  child: Container(
                    color: ColorConstants.WHITE,
                    child: SingleChildScrollView(
                      child: Stack(
                        children: [
                          Column(children: [
                            widget.fromDasboard == false
                                ? Container(
                                    width: width(context),
                                    height: height(context) * 0.1,
                                    decoration: BoxDecoration(
                                      color: ColorConstants.WHITE,
                                      gradient: LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            ColorConstants().gradientLeft(),
                                            ColorConstants().gradientRight()
                                          ]),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 8),
                                      child: Row(
                                        children: [
                                          InkWell(
                                            onTap: () {
                                              if (Preference.getString(
                                                          Preference.ROLE) ==
                                                      'Learner' ||
                                                  Preference.getString(
                                                          Preference.ROLE) ==
                                                      'Lead' ||
                                                  Preference.getString(
                                                          Preference.ROLE) ==
                                                      'Alumni') {
                                                Navigator.push(
                                                        context,
                                                        NextPageRoute(
                                                            NewPortfolioPage()))
                                                    .then((value) {
                                                  if (value != null)
                                                    mp.updateCurrentIndex(
                                                        value);
                                                });
                                              } else {
                                                Navigator.push(
                                                    context,
                                                    NextPageRoute(
                                                        NewPortfolioPage(
                                                      expJobResume: false,
                                                    ))).then((value) {
                                                  if (value != null)
                                                    mp.updateCurrentIndex(
                                                        value);
                                                });
                                              }
                                            },
                                            child: SizedBox(
                                              width: 50,
                                              height: 50,
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(200),
                                                child: CachedNetworkImage(
                                                    imageUrl:
                                                        '${Preference.getString(Preference.PROFILE_IMAGE)}',
                                                    width: 50,
                                                    height: 50,
                                                    fit: BoxFit.cover,
                                                    placeholder:
                                                        (context, url) =>
                                                            SvgPicture.asset(
                                                              'assets/images/default_user.svg',
                                                              width: 50,
                                                            ),
                                                    errorWidget:
                                                        (context, url, error) =>
                                                            SvgPicture.asset(
                                                              'assets/images/default_user.svg',
                                                              width: 50,
                                                            )),
                                              ),
                                            ),
                                          ),
                                          SizedBox(
                                            width: width(context) * 0.02,
                                          ),
                                          SizedBox(
                                              width: width(context) * 0.7,
                                              child: Text(
                                                Utility().decrypted128(
                                                    '${Preference.getString(Preference.FIRST_NAME)}'),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                style: Styles.bold(
                                                    size: 18,
                                                    color:
                                                        ColorConstants.WHITE),
                                              )),
                                          Spacer(),
                                          Padding(
                                            padding: Utility().isRTL(context)
                                                ? EdgeInsets.only(
                                                    top: 18.0, left: 6)
                                                : EdgeInsets.only(
                                                    top: 18.0, right: 6),
                                            child: SizedBox(
                                              // flex: 2,
                                              child: Align(
                                                alignment:
                                                    Utility().isRTL(context)
                                                        ? Alignment.topLeft
                                                        : Alignment.topRight,
                                                child: InkWell(
                                                  onTap: () {
                                                    _scaffoldKey.currentState
                                                        ?.openEndDrawer();
                                                  },
                                                  child: SvgPicture.asset(
                                                      'assets/images/hamburger_menu.svg'),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                : Container(
                                    width: width(context),
                                    padding: EdgeInsets.only(top: 45),
                                    decoration: BoxDecoration(
                                      color: ColorConstants.WHITE,
                                      gradient: LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            ColorConstants().gradientLeft(),
                                            ColorConstants().gradientRight()
                                          ]),
                                    ),
                                    child: Row(children: [
                                      IconButton(
                                          onPressed: () {
                                            Navigator.pop(context);
                                          },
                                          icon: Icon(
                                            Icons.arrow_back_ios,
                                            color: ColorConstants.WHITE,
                                          ))
                                    ]),
                                  ),
                            Transform.translate(
                              offset: Offset(0, -0.4),
                              child: Container(
                                width: double.infinity,
                                height: mobileHeight * 0.20,
                                padding: EdgeInsets.only(top: 10),
                                decoration: BoxDecoration(
                                  color: ColorConstants.WHITE,
                                  gradient: LinearGradient(
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                      colors: <Color>[
                                        ColorConstants().gradientLeft(),
                                        ColorConstants().gradientRight()
                                      ]),
                                ),
                                child: Stack(
                                  children: [
                                    Positioned(
                                        left: isrtl ? null : mobileWidth * 0.09,
                                        right:
                                            !isrtl ? null : mobileWidth * 0.09,
                                        top: 8,
                                        child: renderProgressBar(
                                            percentage(
                                                userRank?.data?.first?.score ??
                                                    0),
                                            barThickness,
                                            mobileWidth)),
                                    Positioned(
                                        left: isrtl ? null : mobileWidth * 0.02,
                                        right:
                                            !isrtl ? null : mobileWidth * 0.02,
                                        top: 30,
                                        child: Text(
                                          '${userRank?.data?.first?.score.toStringAsFixed(2) ?? 0} ${tr('points')}',
                                          style: Styles.regular(
                                              color: ColorConstants.WHITE,
                                              size: 12.5),
                                        )),
                                    Positioned(
                                      left: isrtl ? null : mobileWidth * 0.06,
                                      right: !isrtl ? null : mobileWidth * 0.06,
                                      child: Container(
                                        width: 25,
                                        height: 25,
                                        decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                                color: ColorConstants.WHITE,
                                                width: 2.5)),
                                        child: Image.asset(
                                            'assets/images/check.png'),
                                      ),
                                    ),
                                    Positioned(
                                        // left: mobileWidth * 0.59,
                                        left: isrtl ? null : mobileWidth * 0.59,
                                        right:
                                            !isrtl ? null : mobileWidth * 0.59,
                                        top: 8,
                                        child: renderBar(
                                            barThickness, mobileWidth)),
                                    Positioned(
                                        left: isrtl ? null : mobileWidth * 0.72,
                                        right:
                                            !isrtl ? null : mobileWidth * 0.72,
                                        top: 8,
                                        child: renderBar(
                                            barThickness, mobileWidth)),
                                    Positioned(
                                        left: isrtl ? null : mobileWidth * 0.85,
                                        right:
                                            !isrtl ? null : mobileWidth * 0.85,
                                        top: 8,
                                        child: renderBar(
                                            barThickness, mobileWidth)),
                                    Positioned(
                                        left: isrtl ? null : mobileWidth * 0.97,
                                        right:
                                            !isrtl ? null : mobileWidth * 0.97,
                                        top: 8,
                                        child: renderBar(
                                            barThickness, mobileWidth,
                                            fullWidth: true)),
                                    Positioned(
                                        left: isrtl ? null : mobileWidth * 0.53,
                                        right:
                                            !isrtl ? null : mobileWidth * 0.53,
                                        top: 4,
                                        child: renderEllipse(
                                            '${nextValue(userRank?.data?.first?.score ?? 0, 1)}')),
                                    Positioned(
                                        left: isrtl ? null : mobileWidth * 0.66,
                                        right:
                                            !isrtl ? null : mobileWidth * 0.66,
                                        top: 3.8,
                                        child: renderEllipse(
                                            '${nextValue(userRank?.data?.first?.score ?? 0, 2)}')),
                                    Positioned(
                                        left: isrtl ? null : mobileWidth * 0.79,
                                        right:
                                            !isrtl ? null : mobileWidth * 0.79,
                                        top: 4,
                                        child: renderEllipse(
                                            '${nextValue(userRank?.data?.first?.score ?? 0, 3)}')),
                                    Positioned(
                                        left: isrtl ? null : mobileWidth * 0.92,
                                        right:
                                            !isrtl ? null : mobileWidth * 0.92,
                                        top: 4,
                                        child: renderEllipse(
                                            '${nextValue(userRank?.data?.first?.score ?? 0, 4)}')),
                                    Positioned(
                                        left: isrtl ? null : mobileWidth * 0.07,
                                        right:
                                            !isrtl ? null : mobileWidth * 0.07,
                                        bottom: 55,
                                        child: renderTopButton(
                                          'assets/images/leaderboard.png',
                                          '${tr('your_rank')}: ',
                                          '${userRank?.data?.first?.rank ?? '--'}',
                                          userRank?.data?.first?.rank,
                                          userRank?.data?.first?.score,
                                        )),
                                    Positioned(
                                        right:
                                            isrtl ? null : mobileWidth * 0.07,
                                        left:
                                            !isrtl ? null : mobileWidth * 0.07,
                                        bottom: 55,
                                        child: renderTopButton(
                                          'assets/images/coin.png',
                                          '${tr('points')}: ',
                                          '${userRank?.data?.first?.score ?? '--'}',
                                          userRank?.data?.first?.rank,
                                          userRank?.data?.first?.score,
                                        )),
                                    Positioned(
                                      bottom: -1,
                                      left: 0,
                                      right: 0,
                                      child: Container(
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                            color: ColorConstants.WHITE,
                                            borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(16),
                                                topRight: Radius.circular(16))),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ]),
                          _tabBar(),
                        ],
                      ),
                    ),
                  ))),
        ));
  }

  Widget _tabBar() {
    List<Competition?>? filterEventList = competitionResponse?.event;

    filterEventList = competitionResponse?.event?.where((element) {
      if (selectedFilter.isEmpty) {
        return true;
      }
      EventStatus? elementEventStatus;
      switch (element?.eventStatus.toString().toLowerCase()) {
        case 'live':
          elementEventStatus = EventStatus.live;
          break;
        case 'past':
          elementEventStatus = EventStatus.past;
          break;
        case 'upcoming':
          elementEventStatus = EventStatus.upcoming;
          break;
      }
      return selectedFilter.contains(elementEventStatus);
    }).toList();

    return Container(
      width: MediaQuery.of(context).size.width,
      margin: EdgeInsets.only(top: 230),
      decoration: BoxDecoration(
          color: ColorConstants.WHITE,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(15), topRight: Radius.circular(15))),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 30, right: 30, top: 15),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                //TODO: Event Tab
                _callMyActivity == false
                    ? GestureDetector(
                        onTap: () {
                          if (competitionLoading == false) {
                            setState(() {
                              tabSelected1 = true;
                              tabSelected2 = false;
                              _callMyActivity = true;
                            });
                            if (competitionResponse?.event == null) {
                              _callMyActivity = false;
                              getCompetitionList(false, '',
                                  competitionType: 'event');
                            } else {
                              getCompetitionList(false, '',
                                  competitionType: 'event',
                                  callMyActivity: true);
                            }
                          } else {
                            print('singhLoadingEvent else');
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(left: 30, bottom: 5),
                          child: Text('events',
                                  style: tabSelected1
                                      ? Styles.bold(
                                          size: 16,
                                          color: tabSelected1
                                              ? APK_DETAILS['package_name'] ==
                                                      'com.singulariswow'
                                                  ? ColorConstants
                                                      .WOW_PRIMARY_COLOR
                                                  : APK_DETAILS[
                                                              'package_name'] ==
                                                          'com.singularis.jumeira'
                                                      ? ColorConstants.VIEW_ALL
                                                      : ColorConstants
                                                          .PRIMARY_BLUE
                                              : ColorConstants.BODY_TEXT)
                                      : Styles.semibold(
                                          size: 16,
                                          color: tabSelected1
                                              ? APK_DETAILS['package_name'] ==
                                                      'com.singulariswow'
                                                  ? ColorConstants
                                                      .WOW_PRIMARY_COLOR
                                                  : APK_DETAILS[
                                                              'package_name'] ==
                                                          'com.singularis.jumeira'
                                                      ? ColorConstants.VIEW_ALL
                                                      : ColorConstants
                                                          .PRIMARY_BLUE
                                              : ColorConstants.BODY_TEXT))
                              .tr(),
                        ),
                      )
                    : Padding(
                        padding: const EdgeInsets.only(left: 30, bottom: 5),
                        child: Text('events',
                                style: tabSelected1
                                    ? Styles.bold(
                                        size: 16,
                                        color: tabSelected1
                                            ? APK_DETAILS['package_name'] ==
                                                    'com.singulariswow'
                                                ? ColorConstants
                                                    .WOW_PRIMARY_COLOR
                                                : APK_DETAILS['package_name'] ==
                                                        'com.singularis.jumeira'
                                                    ? ColorConstants.VIEW_ALL
                                                    : ColorConstants
                                                        .PRIMARY_BLUE
                                            : ColorConstants.BODY_TEXT)
                                    : Styles.semibold(
                                        size: 16,
                                        color: tabSelected1
                                            ? APK_DETAILS['package_name'] ==
                                                    'com.singulariswow'
                                                ? ColorConstants
                                                    .WOW_PRIMARY_COLOR
                                                : APK_DETAILS['package_name'] ==
                                                        'com.singularis.jumeira'
                                                    ? ColorConstants.VIEW_ALL
                                                    : ColorConstants
                                                        .PRIMARY_BLUE
                                            : ColorConstants.BODY_TEXT))
                            .tr(),
                      ),
                Spacer(),

                //TODO: Competitions Tab
                _callMyActivity == false
                    ? GestureDetector(
                        onTap: () {
                          if (competitionLoading == false) {
                            setState(() {
                              tabSelected1 = false;
                              tabSelected2 = true;
                              _callMyActivity = true;
                            });
                            if (competitionResponse?.event == null) {
                              _callMyActivity = false;
                              getCompetitionList(false, '',
                                  competitionType: '');
                            } else {
                              getCompetitionList(false, '',
                                  competitionType: '', callMyActivity: true);
                            }
                          } else {
                            print('singhLoadingCmp else');
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(right: 30, bottom: 5),
                          child: Text('competitions',
                                  style: tabSelected2
                                      ? Styles.bold(
                                          size: 16,
                                          color: tabSelected2
                                              ? APK_DETAILS['package_name'] ==
                                                      'com.singulariswow'
                                                  ? ColorConstants
                                                      .WOW_PRIMARY_COLOR
                                                  : APK_DETAILS[
                                                              'package_name'] ==
                                                          'com.singularis.jumeira'
                                                      ? ColorConstants.VIEW_ALL
                                                      : ColorConstants
                                                          .PRIMARY_BLUE
                                              : Colors.grey)
                                      : Styles.semibold(
                                          size: 16,
                                          color: tabSelected2
                                              ? APK_DETAILS['package_name'] ==
                                                      'com.singulariswow'
                                                  ? ColorConstants
                                                      .WOW_PRIMARY_COLOR
                                                  : APK_DETAILS[
                                                              'package_name'] ==
                                                          'com.singularis.jumeira'
                                                      ? ColorConstants.VIEW_ALL
                                                      : ColorConstants
                                                          .PRIMARY_BLUE
                                              : Colors.grey))
                              .tr(),
                        ),
                      )
                    : Padding(
                        padding: const EdgeInsets.only(right: 30, bottom: 5),
                        child: Text('competitions',
                                style: tabSelected2
                                    ? Styles.bold(
                                        size: 16,
                                        color: tabSelected2
                                            ? APK_DETAILS['package_name'] ==
                                                    'com.singulariswow'
                                                ? ColorConstants
                                                    .WOW_PRIMARY_COLOR
                                                : APK_DETAILS['package_name'] ==
                                                        'com.singularis.jumeira'
                                                    ? ColorConstants.VIEW_ALL
                                                    : ColorConstants
                                                        .PRIMARY_BLUE
                                            : Colors.grey)
                                    : Styles.semibold(
                                        size: 16,
                                        color: tabSelected2
                                            ? APK_DETAILS['package_name'] ==
                                                    'com.singulariswow'
                                                ? ColorConstants
                                                    .WOW_PRIMARY_COLOR
                                                : APK_DETAILS['package_name'] ==
                                                        'com.singularis.jumeira'
                                                    ? ColorConstants.VIEW_ALL
                                                    : ColorConstants
                                                        .PRIMARY_BLUE
                                            : Colors.grey))
                            .tr(),
                      ),
              ],
            ),
          ),
          Stack(
            children: [
              Container(
                color: ColorConstants.DIVIDER_COLOR_1,
                width: MediaQuery.of(context).size.width,
                height: 1.5,
              ),
              Positioned(
                left: Utility().isRTL(context) ? width(context) * 0.5 : 0,
                right: Utility().isRTL(context) ? 0 : width(context) * 0.5,
                child: Container(
                  decoration: BoxDecoration(
                      gradient: tabSelected1
                          ? LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: <Color>[
                                  ColorConstants().gradientLeft(),
                                  ColorConstants().gradientRight()
                                ])
                          : LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: <Color>[
                                  ColorConstants.GREY_4,
                                  ColorConstants.GREY_4,
                                ])),
                  width: 200,
                  height: tabSelected1 ? 3 : 0,
                ),
              ),
              Positioned(
                right: Utility().isRTL(context) ? width(context) * 0.5 : 0,
                left: Utility().isRTL(context) ? 0 : width(context) * 0.5,
                child: Container(
                  decoration: BoxDecoration(
                      gradient: tabSelected2
                          ? LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: <Color>[
                                  ColorConstants().gradientLeft(),
                                  ColorConstants().gradientRight()
                                ])
                          : LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: <Color>[
                                  ColorConstants.GREY_4,
                                  ColorConstants.GREY_4,
                                ])),
                  width: 200,
                  height: tabSelected2 ? 3 : 0,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10,
          ),

          //TODO:Competition Section
          completedCompetition != null ||
                  myActivity != null &&
                      myActivity?.data.length != 0 &&
                      completedCompetition?.data.length != 0
              ? Visibility(
                  visible: tabSelected1,
                  child: Container(
                      color: ColorConstants.WHITE,
                      width: MediaQuery.of(context).size.width,
                      child: Column(
                        children: [
                          if (myActivity?.data != null &&
                              myActivity?.data.length != 0)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8),
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text('my_events',
                                          style: Styles.bold(
                                            size: 14,
                                            color: ColorConstants.GREY_2,
                                          )).tr(),
                                      InkWell(
                                        onTap: () {
                                          Navigator.push(
                                              context,
                                              PageTransition(
                                                  duration: Duration(
                                                      milliseconds: 300),
                                                  reverseDuration: Duration(
                                                      milliseconds: 300),
                                                  type: PageTransitionType
                                                      .bottomToTop,
                                                  child: CompetitionMyActivity(
                                                    completedCompetition:
                                                        completedCompetition,
                                                    myActivity: myActivity,
                                                    enableStatus: true,
                                                  ))).then((value) {
                                            topScoringUser();
                                            //getCompetitionList(false, '', competitionType: 'event');
                                            //getDomainList();
                                          });
                                        },
                                        child: Text('view_all',
                                            style: Styles.regular(
                                              size: 12,
                                              color: ColorConstants.BODY_TEXT,
                                            )).tr(),
                                      )
                                    ],
                                  ),
                                  if ((myActivity?.data.length ?? 0) +
                                          (completedCompetition?.data.length ??
                                              0) >
                                      0)
                                    _callMyActivity == false
                                        ? SizedBox(
                                            height: height(context) * 0.17,
                                            child: ListView.builder(
                                                itemCount:
                                                    ((myActivity?.data.length ??
                                                                0)) <
                                                            4
                                                        ? (myActivity
                                                                ?.data.length ??
                                                            0)
                                                        : 4,
                                                scrollDirection:
                                                    Axis.horizontal,
                                                itemBuilder: (context, index) {
                                                  return Container(
                                                    margin: EdgeInsets.only(
                                                        left: 8),
                                                    child:
                                                        CompetitionMyAcitivityCard(
                                                      id: myActivity
                                                          ?.data[index].id,
                                                      desc: myActivity
                                                          ?.data[index].desc,
                                                      score: int.parse(
                                                          '${myActivity?.data[index].score}'),
                                                      gscore: int.parse(
                                                          '${myActivity?.data[index].gscore ?? 0}'),
                                                      date: myActivity
                                                          ?.data[index]
                                                          .starDate,
                                                      conductedBy: myActivity
                                                          ?.data[index]
                                                          .organizedBy,
                                                      image: myActivity
                                                          ?.data[index].pImage,
                                                      title: myActivity
                                                          ?.data[index].name,
                                                      totalAct: myActivity
                                                          ?.data[index]
                                                          .totalContents,
                                                      doneAct: myActivity
                                                          ?.data[index]
                                                          .totalActivitiesCompleted,
                                                      difficulty: myActivity
                                                          ?.data[index]
                                                          .competitionLevel,
                                                      activityStatus: myActivity
                                                              ?.data[index]
                                                              .activityStatus ??
                                                          '',
                                                      activityStatusNumeric:
                                                          myActivity
                                                              ?.data[index]
                                                              .activityStatusNumeric,
                                                      progressStatus: myActivity
                                                          ?.data[index]
                                                          .progressStatus,
                                                      enableProgressStatus:
                                                          true,
                                                      onClose: () {
                                                        topScoringUser();
                                                        getCompetitionList(
                                                            false, '',
                                                            competitionType:
                                                                'event');
                                                        getDomainList();
                                                      },
                                                    ),
                                                  );
                                                }),
                                          )
                                        : EventBlankPage(),
                                ],
                              ),
                            ),

                          if ((completedCompetition != null ||
                                  myActivity != null) &&
                              (myActivity?.data.length ?? 0) +
                                      (completedCompetition?.data.length ?? 0) >
                                  0)
                            Container(
                              height: height(context) * 0.02,
                              width: double.infinity,
                              color: ColorConstants.GREY,
                            ),

                          //if (competitionResponse != null && competitionResponse?.event?.length != 0)
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                /*Text('participate_portfolio',
                                style: Styles.bold(
                                  size: 14,
                                  color: ColorConstants.GREY_2,
                                )).tr(),*/

                                //Event singh
                                if (competitionResponse != null &&
                                    competitionResponse?.event?.length != 0)
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 8, right: 8),
                                    child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 14),
                                            child: Text('participate_portfolio',
                                                style: Styles.bold(
                                                  size: 14,
                                                  color: ColorConstants
                                                      .HEADING_TITLE,
                                                )).tr(),
                                          ),
                                          // if (APK_DETAILS['package_name'] !=
                                          //     'com.singulariswow.mec') //for filter hide
                                          InkWell(
                                              onTap: () {
                                                List<String?> selectedEventId =
                                                    [];
                                                //currently on 3 state are there in filter when all filter are selected length will be 3
                                                if (selectedFilter.length ==
                                                    3) {
                                                  competitionResponse?.event
                                                      ?.forEach((value) {
                                                    selectedEventId.add(
                                                        value?.eventStatus);
                                                  });
                                                } else {
                                                  competitionResponse?.event
                                                      ?.forEach((value) {
                                                    selectedEventId.add(
                                                        value?.eventStatus);
                                                  });
                                                }

                                                _showModalBottomSheet(context);
                                              },
                                              child: Icon(
                                                Icons.filter_list,
                                                color: ColorConstants
                                                    .HEADING_TITLE,
                                              ))
                                        ]),
                                  ),
                                competitionLoading == false
                                    ? filterEventList != null &&
                                            filterEventList.length != 0
                                        ? ListView.builder(
                                            shrinkWrap: true,
                                            physics: BouncingScrollPhysics(),
                                            itemCount: widget.fromDasboard ==
                                                    true
                                                ? min(
                                                    3,
                                                    int.parse(
                                                        '${filterEventList.length}'))
                                                : filterEventList.length,
                                            itemBuilder: (BuildContext context,
                                                int index) {
                                              if (selectedFilter.length != 0) {
                                                return InkWell(
                                                    onTap: () {
                                                      Navigator.push(
                                                          context,
                                                          MaterialPageRoute(
                                                              builder: (context) => CompetitionDetail(
                                                                  competitionId:
                                                                      competitionResponse
                                                                          ?.event?[
                                                                              index]
                                                                          ?.id,
                                                                  isEvent:
                                                                      true))).then(
                                                          (value) {
                                                        topScoringUser();
                                                        getCompetitionList(
                                                            false, '',
                                                            competitionType:
                                                                'event');
                                                        getDomainList();
                                                      });
                                                    },
                                                    child: renderCompetitionCard(
                                                        '${filterEventList?[index]?.image ?? ''}',
                                                        '${filterEventList?[index]?.name ?? ''}',
                                                        '${filterEventList?[index]?.organizedBy ?? ''}',
                                                        '${filterEventList?[index]?.competitionLevel}',
                                                        '${filterEventList?[index]?.gScore ?? 0}',
                                                        '${filterEventList?[index]?.startDate}',
                                                        '${filterEventList?[index]?.endDate}',
                                                        '${filterEventList?[index]?.progressStatus}',
                                                        true,
                                                        '${filterEventList?[index]?.location}'));
                                              } else {
                                                if (filterEventList?[index]
                                                        ?.progressStatus
                                                        ?.toLowerCase() !=
                                                    'past') {
                                                  return InkWell(
                                                      onTap: () {
                                                        Navigator.push(
                                                            context,
                                                            MaterialPageRoute(
                                                                builder: (context) => CompetitionDetail(
                                                                    competitionId: competitionResponse
                                                                        ?.event?[
                                                                            index]
                                                                        ?.id,
                                                                    isEvent:
                                                                        true))).then(
                                                            (value) {
                                                          topScoringUser();
                                                          getCompetitionList(
                                                              false, '',
                                                              competitionType:
                                                                  'event');
                                                          getDomainList();
                                                        });
                                                      },
                                                      child: renderCompetitionCard(
                                                          '${filterEventList?[index]?.image ?? ''}',
                                                          '${filterEventList?[index]?.name ?? ''}',
                                                          '${filterEventList?[index]?.organizedBy ?? ''}',
                                                          '${filterEventList?[index]?.competitionLevel}',
                                                          '${filterEventList?[index]?.gScore ?? 0}',
                                                          '${filterEventList?[index]?.startDate}',
                                                          '${filterEventList?[index]?.endDate}',
                                                          '${filterEventList?[index]?.progressStatus}',
                                                          true,
                                                          '${filterEventList?[index]?.location}'));
                                                }
                                                return SizedBox(
                                                  child: index ==
                                                          selectedFilter.length
                                                      ? Center(
                                                          child: Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(60.0),
                                                          child: Text(
                                                                  'no_event_found')
                                                              .tr(),
                                                        ))
                                                      : SizedBox(),
                                                );
                                              }
                                            })
                                        : Container(
                                            height: height(context) * 0.1,
                                            color: ColorConstants.WHITE,
                                            width: double.infinity,
                                            child: Center(
                                                child: Text(
                                              'no_event_found',
                                              style: Styles.regular(size: 14),
                                            ).tr()))
                                    : Shimmer.fromColors(
                                        baseColor: Colors.grey[300]!,
                                        highlightColor: Colors.grey[100]!,
                                        enabled: true,
                                        child: ListView.builder(
                                          shrinkWrap: true,
                                          itemBuilder: (_, __) => Container(
                                            padding: EdgeInsets.symmetric(
                                                vertical: 8),
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            child: Container(
                                              decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(8)),
                                              width: double.infinity,
                                              height: 80,
                                            ),
                                          ),
                                          itemCount: 2,
                                        ),
                                      ),
                              ],
                            ),
                          ),
                        ],
                      )),
                )
              : BlankPage(),

          //TODO:Competition Section
          Visibility(
            visible: tabSelected2,
            child: Container(
                color: ColorConstants.WHITE,
                // height: MediaQuery.of(context).size.height * 0.735,
                width: MediaQuery.of(context).size.width,
                child: Column(
                  children: [
                    if ((completedCompetition != null || myActivity != null) &&
                        (myActivity?.data.length ?? 0) +
                                (completedCompetition?.data.length ?? 0) >
                            0)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('my_competitions',
                                    style: Styles.bold(
                                      size: 14,
                                      color: ColorConstants.HEADING_TITLE,
                                    )).tr(),
                                InkWell(
                                  onTap: () {
                                    Navigator.push(
                                        context,
                                        PageTransition(
                                            duration:
                                                Duration(milliseconds: 300),
                                            reverseDuration:
                                                Duration(milliseconds: 300),
                                            type:
                                                PageTransitionType.bottomToTop,
                                            child: CompetitionMyActivity(
                                              completedCompetition:
                                                  completedCompetition,
                                              myActivity: myActivity,
                                            ))).then((value) {
                                      topScoringUser();
                                      //getCompetitionList(false, '');
                                      //getDomainList();
                                    });
                                  },
                                  child: Text('view_all',
                                      style: Styles.regular(
                                        size: 12,
                                        color: ColorConstants.BODY_TEXT,
                                      )).tr(),
                                )
                              ],
                            ),
                            if ((myActivity?.data.length ?? 0) +
                                    (completedCompetition?.data.length ?? 0) >
                                0)
                              _callMyActivity == false
                                  ? SizedBox(
                                      height: height(context) * 0.17,
                                      child: ListView.builder(
                                          itemCount:
                                              ((myActivity?.data.length ?? 0) +
                                                          (completedCompetition
                                                                  ?.data
                                                                  .length ??
                                                              0)) <
                                                      4
                                                  ? (myActivity?.data.length ??
                                                          0) +
                                                      (completedCompetition
                                                              ?.data.length ??
                                                          0)
                                                  : 4,
                                          scrollDirection: Axis.horizontal,
                                          itemBuilder: (context, index) {
                                            if (index <
                                                (myActivity?.data.length ?? 0))
                                              return Container(
                                                margin:
                                                    EdgeInsets.only(left: 8),
                                                child:
                                                    CompetitionMyAcitivityCard(
                                                  id: myActivity
                                                      ?.data[index].id,
                                                  desc: myActivity
                                                      ?.data[index].desc,
                                                  score: int.parse(
                                                      '${myActivity?.data[index].score}'),
                                                  gscore: int.parse(
                                                      '${myActivity?.data[index].gscore ?? 0}'),
                                                  date: myActivity
                                                      ?.data[index].starDate,
                                                  conductedBy: myActivity
                                                      ?.data[index].organizedBy,
                                                  image: myActivity
                                                      ?.data[index].pImage,
                                                  title: myActivity
                                                      ?.data[index].name,
                                                  totalAct: myActivity
                                                      ?.data[index]
                                                      .totalContents,
                                                  doneAct: myActivity
                                                      ?.data[index]
                                                      .totalActivitiesCompleted,
                                                  difficulty: myActivity
                                                      ?.data[index]
                                                      .competitionLevel,
                                                  activityStatus: myActivity
                                                          ?.data[index]
                                                          .activityStatus ??
                                                      '',
                                                  activityStatusNumeric:
                                                      myActivity?.data[index]
                                                          .activityStatusNumeric,
                                                  progressStatus: myActivity
                                                      ?.data[index]
                                                      .progressStatus,
                                                  onClose: () {
                                                    topScoringUser();
                                                    getCompetitionList(
                                                        false, '');
                                                    getDomainList();
                                                  },
                                                ),
                                              );
                                            else {
                                              index = index -
                                                  (myActivity?.data.length ??
                                                      0);
                                              return Container(
                                                margin:
                                                    EdgeInsets.only(left: 8),
                                                child:
                                                    CompetitionMyAcitivityCard(
                                                  image: completedCompetition
                                                      ?.data[index].pImage,
                                                  title: completedCompetition
                                                      ?.data[index].pName,
                                                  totalAct: completedCompetition
                                                      ?.data[index]
                                                      .totalActivities,
                                                  doneAct: completedCompetition
                                                      ?.data[index]
                                                      .completedActivity,
                                                  id: completedCompetition
                                                      ?.data[index].pId,
                                                  score: int.parse(
                                                      '${completedCompetition?.data[index].score}'),
                                                  gscore: int.parse(
                                                      '${completedCompetition?.data[index].gScore}'),
                                                  desc: completedCompetition
                                                      ?.data[index].desc,
                                                  date: completedCompetition
                                                      ?.data[index].startDate,
                                                  difficulty:
                                                      completedCompetition
                                                          ?.data[index]
                                                          .competitionLevel,
                                                  conductedBy:
                                                      completedCompetition
                                                          ?.data[index]
                                                          .organizedBy,
                                                  activityStatus: null,
                                                  rank: completedCompetition
                                                      ?.data[index].rank,
                                                  onClose: () {
                                                    topScoringUser();
                                                    getCompetitionList(
                                                        false, '');
                                                    getDomainList();
                                                  },
                                                ),
                                              );
                                            }
                                          }),
                                    )
                                  : EventBlankPage()
                          ],
                        ),
                      ),
                    Container(
                      height: height(context) * 0.02,
                      width: double.infinity,
                      color: ColorConstants.GREY,
                    ),
                    if (widget.fromDasboard == false)
                      Padding(
                        padding: const EdgeInsets.only(left: 8, right: 8),
                        child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 14),
                                child: Text('participate_portfolio',
                                    style: Styles.bold(
                                      size: 14,
                                      color: ColorConstants.HEADING_TITLE,
                                    )).tr(),
                              ),
                              InkWell(
                                  onTap: () async {
                                    selectedIndex = 0;
                                    getFilterList(domainList!.data!.list![0].id
                                        .toString());

                                    await Navigator.push(
                                        context,
                                        PageTransition(
                                            duration:
                                                Duration(milliseconds: 350),
                                            reverseDuration:
                                                Duration(milliseconds: 350),
                                            type:
                                                PageTransitionType.bottomToTop,
                                            child: CompetitionFilter(
                                              domainList: domainList,
                                            ))).then((value) {
                                      topScoringUser();
                                      getCompetitionList(false, '');
                                      getDomainList();
                                    });
                                  },
                                  child: Icon(
                                    Icons.filter_list,
                                    color: ColorConstants.HEADING_TITLE,
                                  ))
                            ]),
                      ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: competitionLoading == false
                          ? competitionResponse != null &&
                                  competitionResponse?.data?.length != 0
                              ? ListView.builder(
                                  shrinkWrap: true,
                                  physics: BouncingScrollPhysics(),
                                  itemCount: widget.fromDasboard == true
                                      ? min(
                                          3,
                                          int.parse(
                                              '${competitionResponse?.data?.length}'))
                                      : min(
                                          4,
                                          competitionResponse?.data?.length ??
                                              0),
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    return InkWell(
                                        onTap: () {
                                          Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (context) =>
                                                      CompetitionDetail(
                                                          competitionId:
                                                              competitionResponse
                                                                  ?.data?[index]
                                                                  ?.id))).then(
                                              (value) {
                                            topScoringUser();
                                            getCompetitionList(false, '',
                                                competitionType: '');
                                            getDomainList();
                                          });
                                        },
                                        child: renderCompetitionCard(
                                            '${competitionResponse?.data![index]?.image ?? ''}',
                                            '${competitionResponse?.data![index]?.name ?? ''}',
                                            '${competitionResponse?.data![index]?.organizedBy ?? ''}',
                                            '${competitionResponse?.data![index]?.competitionLevel}',
                                            '${competitionResponse?.data![index]?.gScore ?? 0}',
                                            '${competitionResponse?.data?[index]?.startDate}',
                                            '${competitionResponse?.data?[index]?.endDate}',
                                            '${competitionResponse?.data?[index]?.progressStatus}',
                                            false,
                                            '${competitionResponse?.data?[index]?.location}'));
                                  })
                              : Container(
                                  height: height(context) * 0.1,
                                  color: ColorConstants.WHITE,
                                  width: double.infinity,
                                  child: Center(
                                      child: Text(
                                    'no_competition_found',
                                    style: Styles.regular(size: 14),
                                  ).tr()))
                          : Shimmer.fromColors(
                              baseColor: Colors.grey[300]!,
                              highlightColor: Colors.grey[100]!,
                              enabled: true,
                              child: ListView.builder(
                                shrinkWrap: true,
                                itemBuilder: (_, __) => Container(
                                  padding: EdgeInsets.symmetric(vertical: 8),
                                  width: MediaQuery.of(context).size.width,
                                  child: Container(
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(8)),
                                    width: double.infinity,
                                    height: 80,
                                  ),
                                ),
                                itemCount: 2,
                              ),
                            ),
                    ),
                    SizedBox(height: 10),
                    if (competitionLoading == false &&
                        popularCompetitionResponse?.data?.length != 0)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/images/star.svg',
                              height: height(context) * 0.020,
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Text('Popular_activity',
                                style: Styles.regular(
                                  size: 14,
                                  color: ColorConstants.HEADING_TITLE,
                                )).tr()
                          ],
                        ),
                      ),
                    if (competitionLoading == false)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Container(
                          width: double.infinity,
                          height: popularCompetitionResponse?.data?.length != 0
                              ? height(context) * 0.45
                              : 0,
                          padding: EdgeInsets.symmetric(vertical: 20),
                          child: popularCompetitionResponse != null &&
                                  popularCompetitionResponse?.data?.length != 0
                              ? ListView.builder(
                                  itemCount:
                                      popularCompetitionResponse?.data?.length,
                                  shrinkWrap: true,
                                  scrollDirection: Axis.horizontal,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    return InkWell(
                                        onTap: () {
                                          Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (BuildContext
                                                          context) =>
                                                      CompetitionDetail(
                                                          competitionId:
                                                              popularCompetitionResponse
                                                                  ?.data?[index]
                                                                  ?.id))).then(
                                              (value) {
                                            topScoringUser();
                                            getCompetitionList(false, '');
                                            getDomainList();
                                          });
                                        },
                                        child: renderActivityCard(
                                            '${popularCompetitionResponse?.data![index]?.image}',
                                            '${popularCompetitionResponse?.data![index]?.name}',
                                            '',
                                            '${popularCompetitionResponse?.data![index]?.competitionLevel}',
                                            '${popularCompetitionResponse?.data![index]?.gScore}',
                                            '${popularCompetitionResponse?.data?[index]?.startDate}',
                                            popularCompetitionResponse
                                                ?.data?[index]?.endDate));
                                  })
                              : Center(
                                  child: Text('no_competition_found').tr()),
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: competitionLoading == false &&
                              widget.fromDasboard == false
                          ? competitionResponse?.data?.length != 0 &&
                                  competitionResponse?.data != null
                              ? ListView.builder(
                                  shrinkWrap: true,
                                  physics: BouncingScrollPhysics(),
                                  itemCount: competitionResponse!.data!.length >
                                          4
                                      ? competitionResponse!.data!.length - 4
                                      : 0,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    index = index + 4;
                                    /*String startDate =
                                '${competitionResponse?.data?[index]?.startDate?.split(' ').first}';

                            DateTime dateTime =
                            DateTime.parse(startDate);
                            String startFormated =
                            DateFormat('dd-MMM-yyyy')
                                .format(dateTime);

                            String endDate =
                                '${competitionResponse?.data?[index]?.endDate?.split(' ').first}';

                            dateTime = DateTime.parse(endDate);
                            String endDateFormated =
                            DateFormat('dd-MMM-yyyy')
                                .format(dateTime);*/
                                    return InkWell(
                                        onTap: () {
                                          Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (context) =>
                                                      CompetitionDetail(
                                                          competitionId:
                                                              competitionResponse
                                                                  ?.data?[index]
                                                                  ?.id))).then(
                                              (value) {
                                            topScoringUser();
                                            getCompetitionList(false, '');
                                            getDomainList();
                                          });
                                        },
                                        child: renderCompetitionCard(
                                            '${competitionResponse?.data![index]?.image ?? ''}',
                                            '${competitionResponse?.data![index]?.name ?? ''}',
                                            '${competitionResponse?.data![index]?.organizedBy ?? ''}',
                                            '${competitionResponse?.data![index]?.competitionLevel}',
                                            '${competitionResponse?.data![index]?.gScore ?? 0}',
                                            '${competitionResponse?.data?[index]?.startDate}',
                                            '${competitionResponse?.data?[index]?.endDate}',
                                            '${competitionResponse?.data?[index]?.endDate}',
                                            false,
                                            '${competitionResponse?.data?[index]?.location}'));
                                  })
                              : SizedBox() /*Container(
                                        height: height(context) * 0.1,
                                        color: ColorConstants.WHITE,
                                        width: double.infinity,
                                        child: Center(
                                            child: Text(
                                          'no_competition_found',
                                          style: Styles.regular(size: 14),
                                        ).tr()))*/
                          : Shimmer.fromColors(
                              baseColor: Colors.grey[300]!,
                              highlightColor: Colors.grey[100]!,
                              enabled: true,
                              child: ListView.builder(
                                shrinkWrap: true,
                                itemBuilder: (_, __) => Container(
                                  padding: EdgeInsets.symmetric(vertical: 8),
                                  width: MediaQuery.of(context).size.width,
                                  child: Container(
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(8)),
                                    width: double.infinity,
                                    height: 80,
                                  ),
                                ),
                                itemCount: 2,
                              ),
                            ),
                    ),
                  ],
                )),
          ),
        ],
      ),
    );
  }

  renderActivityCard(String competitionImg, String name, String companyName,
      String difficulty, String gScore, String startDate, endDate) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.7,
      margin: EdgeInsets.only(bottom: 20, left: 0, right: 20),
      decoration: BoxDecoration(
        color: ColorConstants.WHITE,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: const Offset(5, 2),
          ),
        ],
      ),
      child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.7,
              height: height(context) * 0.23,
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16)),
                child: CachedNetworkImage(
                  imageUrl: competitionImg,
                  width: MediaQuery.of(context).size.width * 0.7,
                  height: height(context) * 0.23,
                  errorWidget: (context, url, error) => Image.asset(
                    'assets/images/comp_emp.png',
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.only(left: 8, right: 8),
              child: Text(
                name,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style:
                    Styles.bold(size: 16, color: ColorConstants.HEADING_TITLE),
              ),
            ),
            SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.only(left: 8, right: 8),
              child: Row(children: [
                Text(difficulty.capital(),
                    style: Styles.regular(
                        color: ColorConstants.GREEN_1, size: 12)),
                SizedBox(
                  width: 4,
                ),
                Text('•',
                    style:
                        Styles.regular(color: ColorConstants.GREY_2, size: 12)),
                SizedBox(
                  width: 4,
                ),
                SizedBox(
                    height: 15, child: Image.asset('assets/images/coin.png')),
                SizedBox(
                  width: 4,
                ),
                Text('$gScore ${tr('points')}',
                    style: Styles.regular(
                        color: ColorConstants.ORANGE_4, size: 12)),
              ]),
            ),
            SizedBox(
              height: 6,
            ),
            Padding(
                padding: const EdgeInsets.only(left: 8, right: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.calendar_month,
                      color: ColorConstants.BODY_TEXT,
                      size: 18,
                    ),
                    SizedBox(
                      width: 2,
                    ),
                    StrToTime(
                      time: startDate,
                      dateFormat: ' dd-MMM-yyyy ',
                      appendString: Utility().isRTL(context) ? '' : tr('to'),
                      textStyle: Styles.regular(
                          size: 12, color: ColorConstants.BODY_TEXT),
                    ),
                    StrToTime(
                      time: endDate,
                      dateFormat: ' dd-MMM-yyyy ',
                      appendString: Utility().isRTL(context) ? tr('to') : '',
                      textStyle: Styles.regular(
                          size: 12, color: ColorConstants.BODY_TEXT),
                    ),
                  ],
                )),
            Padding(
                padding: const EdgeInsets.only(left: 8, right: 8, top: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.access_time_filled,
                      color: ColorConstants.BODY_TEXT,
                      size: 18,
                    ),
                    SizedBox(
                      width: 2,
                    ),
                    StrToTime(
                      time: startDate,
                      dateFormat: ' hh:mm a ',
                      appendString: Utility().isRTL(context) ? '' : tr('to'),
                      textStyle: Styles.regular(
                          size: 12, color: ColorConstants.BODY_TEXT),
                    ),
                    StrToTime(
                      time: endDate,
                      dateFormat: ' hh:mm a ',
                      appendString: Utility().isRTL(context) ? tr('to') : '',
                      textStyle: Styles.regular(
                          size: 12, color: ColorConstants.BODY_TEXT),
                    ),
                  ],
                ))
          ]),
    );
  }

  renderCompetitionCard(
      String competitionImg,
      String name,
      String companyName,
      String difficulty,
      String gScore,
      String startdate,
      String endDate,
      String progressStatus,
      bool enableProgressStatus,
      String location) {
    return Stack(
      children: [
        Container(
          //height: 116,
          width: double.infinity,
          padding: EdgeInsets.all(8),
          margin: EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: ColorConstants.WHITE,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 10,
                offset: const Offset(5, 2),
              ),
            ],
          ),
          child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            SizedBox(
              width: width(context) * 0.21,
              height: height(context) * 0.1,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CachedNetworkImage(
                  imageUrl: competitionImg,
                  errorWidget: (context, url, error) => SizedBox(
                    child: SvgPicture.asset(
                      'assets/images/event_default.svg',
                      height: 20,
                      width: 20,
                    ),
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(width: 10),
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(
                    width: width(context) * 0.5,
                    child: Text(
                      name,
                      style: Styles.bold(
                        color: ColorConstants.HEADING_TITLE,
                        size: 16,
                      ),
                      maxLines: 1,
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // SizedBox(
                  //   height: 2,
                  // ),
                  if (companyName != '')
                    SizedBox(
                      width: width(context) * 0.4,
                      child: Text(
                        companyName,
                        style:
                            Styles.semibold(size: 13, color: Color(0xff929BA3)),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                  enableProgressStatus == true
                      ? SizedBox()
                      : Padding(
                          padding: const EdgeInsets.only(top: 2.0),
                          child: Row(
                            children: [
                              Text('${difficulty.capital()}',
                                  style: Styles.regular(
                                      color: ColorConstants.GREEN_1, size: 12)),
                              SizedBox(
                                width: 4,
                              ),
                              Text('•',
                                  style: Styles.regular(
                                      color: ColorConstants.GREY_2, size: 12)),
                              SizedBox(
                                width: 4,
                              ),
                              SizedBox(
                                  height: 15,
                                  child: Image.asset('assets/images/coin.png')),
                              SizedBox(
                                width: 4,
                              ),
                              Text('$gScore ${tr('points')}',
                                  style: Styles.regular(
                                      color: ColorConstants.ORANGE_4,
                                      size: 12)),

                              /*Padding(
                          padding: const EdgeInsets.only(left: 10.0),
                          child: Text('$progressStatus',
                              style: Styles.regular(
                                  color: ColorConstants.RED, size: 15)),
                        ),*/
                            ],
                          ),
                        ),

                  //TODO: Show Live
                  enableProgressStatus == true
                      ? progressStatus != 'null' &&
                              progressStatus.toLowerCase() == 'live'
                          ? Row(
                              children: [
                                SvgPicture.asset(
                                  'assets/images/live_icon.svg',
                                  fit: BoxFit.fitHeight,
                                ),
                                SizedBox(width: 5),
                                progressStatus.toLowerCase() == 'live'
                                    ? FadeTransition(
                                        opacity: _animation,
                                        child: Text(
                                          'live',
                                          style: Styles.bold(
                                              color: ColorConstants.RED,
                                              size: 16),
                                        ).tr(),
                                      )
                                    : Text('$progressStatus',
                                        style: Styles.bold(
                                            color: ColorConstants.RED,
                                            size: 16))
                              ],
                            )
                          : SizedBox()
                      : SizedBox(),
                  // SizedBox(height: 2),

                  //TODO: Show Location
                  location != 'null' && enableProgressStatus
                      ? Padding(
                          padding: const EdgeInsets.only(top: 5.0, bottom: 5.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.location_on,
                                color: ColorConstants.BODY_TEXT,
                                size: 16,
                              ),
                              SizedBox(
                                width: 3,
                              ),
                              Text('$location',
                                  style: Styles.regular(
                                      size: 12,
                                      color: ColorConstants.BODY_TEXT)),
                            ],
                          ),
                        )
                      : SizedBox(),

                  //TODO:Show Date
                  SizedBox(height: 2),
                  enableProgressStatus == true &&
                          progressStatus.toLowerCase() == 'live'
                      ? SizedBox()
                      : Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.calendar_month,
                              color: ColorConstants.BODY_TEXT,
                              size: 16,
                            ),

                            SizedBox(
                              width: 3,
                            ),
                            StrToTime(
                              time: startdate,
                              dateFormat: ' dd-MMM-yy ',
                              //appendString: Utility().isRTL(context) ? '' : tr('to'),
                              appendString: ',',
                              textStyle: Styles.regular(
                                  size: 12,
                                  lineHeight: 1,
                                  color: ColorConstants.BODY_TEXT),
                            ),

                            //Star time Change by 14 Aug
                            StrToTime(
                              time: startdate,
                              dateFormat: ' hh:mm a ',
                              //appendString: Utility().isRTL(context) ? '' : tr('to'),
                              appendString: '',
                              textStyle: Styles.regular(
                                  size: 12,
                                  lineHeight: 1,
                                  color: ColorConstants.BODY_TEXT),
                            ),
                          ],
                        ),

                  //TODO: Show Time
                  enableProgressStatus == true &&
                          progressStatus.toLowerCase() == 'live'
                      ? SizedBox()
                      : Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              /*Icon(
                                //Icons.access_time,
                                Icons.calendar_month,
                                color: ColorConstants.BODY_TEXT,
                                size: 16,
                              ),*/
                              Text(
                                'To',
                                style: Styles.bold(
                                    size: 13,
                                    lineHeight: 1,
                                    color: ColorConstants.BODY_TEXT),
                              ),
                              SizedBox(
                                width: 3,
                              ),
                              /*StrToTime(
                                time: startdate,
                                dateFormat: ' hh:mm a ',
                                appendString:
                                    Utility().isRTL(context) ? '' : tr('to'),
                                textStyle: Styles.regular(
                                    size: 12,
                                    lineHeight: 1,
                                    color: ColorConstants.BODY_TEXT),
                              ),*/

                              //Star date Change by 14 Aug
                              StrToTime(
                                time: endDate,
                                dateFormat: ' dd-MMM-yy ',
                                //appendString: Utility().isRTL(context) ? tr('to') : '',
                                appendString: ',',
                                textStyle: Styles.regular(
                                    size: 12,
                                    lineHeight: 1,
                                    color: Color.fromARGB(255, 4, 6, 16)),
                              ),

                              StrToTime(
                                time: endDate,
                                dateFormat: ' hh:mm a ',
                                appendString:
                                    Utility().isRTL(context) ? tr('to') : '',
                                textStyle: Styles.regular(
                                    size: 12,
                                    lineHeight: 1,
                                    color: Color.fromARGB(255, 4, 6, 16)),
                              ),
                            ],
                          ),
                        ),
                ],
              ),
            ),
          ]),
        ),
        Positioned(
            right: Utility().isRTL(context) ? null : 12,
            left: Utility().isRTL(context) ? 2 : null,
            top: 10,
            bottom: 10,
            child: Icon((Icons.arrow_forward_ios), size: 20)),
      ],
    );
  }

  renderTopButton(
      String img, String title, String value, int? rank, int? score) {
    return InkWell(
      onTap: () {
        Navigator.of(context)
            .push(MaterialPageRoute(
                builder: (context) => OrganizationLeaderboard(
                      rank: rank,
                      score: score,
                    )))
            .then((value) => topScoringUser());
      },
      child: Container(
        height: height(context) * 0.05,
        width: MediaQuery.of(context).size.width * 0.42,
        padding: EdgeInsets.symmetric(vertical: 4, horizontal: 6),
        decoration: BoxDecoration(
            color: ColorConstants.WHITE.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8)),
        child: Row(children: [
          Container(
            width: 30,
            height: 30,
            padding: EdgeInsets.all(2),
            decoration: BoxDecoration(
                shape: BoxShape.circle, color: ColorConstants.WHITE),
            child: Image.asset(img),
          ),
          SizedBox(
            width: 10,
          ),
          Text(title,
              style: Styles.regular(size: 15, color: Color(0xff0E1638))),
          Text(value == '0' ? '- -' : value, style: Styles.semibold(size: 14)),
        ]),
      ),
    );
  }

  renderBar(barThickness, mobileWidth, {fullWidth = false}) {
    return Container(
        width: fullWidth ? mobileWidth : mobileWidth * 0.07,
        height: barThickness,
        color: ColorConstants.WHITE.withValues(alpha: 0.3));
  }

  renderEllipse(String text) {
    return Column(
      children: [
        Transform.scale(
          scale: 1.35,
          child: SvgPicture.asset(
            'assets/images/ellipse.svg',
            colorFilter: ColorFilter.mode(
                ColorConstants().primaryForgroundColor().withValues(alpha: 0.4),
                BlendMode.srcIn),
          ),
        ),
        SizedBox(
          height: 10,
        ),
        Text('$text',
            style: Styles.regular(color: ColorConstants.WHITE, size: 12.5))
      ],
    );
  }

  renderProgressBar(percent, barThickness, mobileWidth) {
    return Container(
      height: barThickness,
      width: mobileWidth * 0.46,
      decoration: BoxDecoration(
        color: ColorConstants.WHITE.withValues(alpha: 0.3),
      ),
      child: Stack(
        children: [
          Container(
            height: barThickness,
            width: mobileWidth * 0.46 * (percent / 100),
            decoration: BoxDecoration(
              color: ColorConstants.YELLOW.withValues(alpha: 0.5),
              borderRadius: BorderRadius.only(
                  topRight: Radius.circular(30),
                  bottomRight: Radius.circular(30)),
            ),
            child: Opacity(
              opacity: 0.3,
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                    topRight: Radius.circular(30),
                    bottomRight: Radius.circular(30)),
                child: SvgPicture.asset(
                  'assets/images/whiteStripe.svg',
                  fit: BoxFit.fitHeight,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showModalBottomSheet(BuildContext context) {
    showModalBottomSheet(
        context: context,
        enableDrag: false,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (BuildContext context, setState) {
            return FractionallySizedBox(
              heightFactor: 0.55,
              child: Container(
                height: 200,
                //height: double.infinity,
                width: double.infinity,
                decoration: BoxDecoration(
                    color: ColorConstants.WHITE,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(8))),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: Container(
                          decoration: BoxDecoration(
                              color: ColorConstants.GREY_4,
                              borderRadius: BorderRadius.circular(8)),
                          width: 48,
                          height: 5,
                          margin: EdgeInsets.only(top: 8),
                        ),
                      ),
                      Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        child: Row(
                          children: [
                            Text(
                              'filter_by',
                              style: Styles.semibold(size: 16),
                            ).tr(),
                            Spacer(),
                            IconButton(
                                onPressed: () {
                                  this.setState(() {
                                    seletedIds = '0';
                                    selectedIdList.clear();
                                  });
                                  Navigator.pop(context);
                                },
                                icon: Icon(Icons.close))
                          ],
                        ),
                      ),
                      Divider(
                        color: ColorConstants.GREY_4,
                      ),
                      Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(children: [
                              Checkbox(
                                  activeColor:
                                      ColorConstants.ACTIVE_TAB_UNDERLINE,
                                  value: selectedFilter
                                      .contains(EventStatus.upcoming),
                                  onChanged: ((value) {
                                    setState(() {
                                      if (selectedFilter
                                          .contains(EventStatus.upcoming)) {
                                        selectedFilter
                                            .remove(EventStatus.upcoming);
                                      } else {
                                        selectedFilter
                                            .add(EventStatus.upcoming);
                                      }
                                    });

                                    /*getCompetitionList(
                                      true,
                                      '',
                                      competitionType: 'event',
                                    );*/

                                    // Future.delayed(Duration(milliseconds: 200),
                                    //     () {
                                    //   Navigator.pop(context);
                                    // });
                                  })),
                              Text('upcoming_events', style: Styles.regular())
                                  .tr(),
                            ]),
                            Row(children: [
                              Checkbox(
                                  activeColor:
                                      ColorConstants.ACTIVE_TAB_UNDERLINE,
                                  value:
                                      selectedFilter.contains(EventStatus.past),
                                  onChanged: ((value) {
                                    setState(() {
                                      if (selectedFilter
                                          .contains(EventStatus.past)) {
                                        selectedFilter.remove(EventStatus.past);
                                      } else {
                                        selectedFilter.add(EventStatus.past);
                                      }
                                    });

                                    /*getCompetitionList(
                                      true,
                                      '',
                                      competitionType: 'event',
                                    );*/

                                    // Future.delayed(Duration(milliseconds: 200),
                                    //     () {
                                    //   Navigator.pop(context);
                                    // });
                                  })),
                              Text('past_events', style: Styles.regular()).tr(),
                            ]),
                            Row(children: [
                              Checkbox(
                                  activeColor:
                                      ColorConstants.ACTIVE_TAB_UNDERLINE,
                                  value:
                                      selectedFilter.contains(EventStatus.live),
                                  onChanged: ((value) {
                                    setState(() {
                                      if (selectedFilter
                                          .contains(EventStatus.live)) {
                                        selectedFilter.remove(EventStatus.live);
                                      } else {
                                        selectedFilter.add(EventStatus.live);
                                      }
                                    });

                                    /*getCompetitionList(
                                      true,
                                      '',
                                      competitionType: 'event',
                                    );*/

                                    // Future.delayed(Duration(milliseconds: 200),
                                    //     () {
                                    //   Navigator.pop(context);
                                    // });
                                  })),
                              Text('live_events', style: Styles.regular()).tr(),
                            ]),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          });
        }).then((value) {});
  }

  _eventsFilter() {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            flex: 9,
            child: Container(
              child: Text('relevant_jobs',
                      style: Styles.bold(
                          size: 16, color: ColorConstants.HEADING_TITLE))
                  .tr(),
            ),
          ),
          // if (APK_DETAILS['package_name'] != 'com.singulariswow.mec') //for filter hide
          Expanded(
            flex: 1,
            child: InkWell(
              onTap: () async {
                selectedIndex = 0;
                getFilterList(domainList!.data!.list![0].id.toString());
                domainFilterList?.data?.list.clear();

                await showModalBottomSheet(
                    context: context,
                    backgroundColor: Colors.transparent,
                    isScrollControlled: true,
                    builder: (context) {
                      return StatefulBuilder(
                          builder: (BuildContext context, setState) {
                        return FractionallySizedBox(
                          heightFactor: 0.7,
                          child: Container(
                            height: 300,
                            //height: double.infinity,
                            width: double.infinity,
                            decoration: BoxDecoration(
                                color: ColorConstants.RED,
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(12),
                                    topRight: Radius.circular(8))),
                            child: SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Center(
                                    child: Container(
                                      decoration: BoxDecoration(
                                          color: ColorConstants.GREY_4,
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                      width: 48,
                                      height: 5,
                                      margin: EdgeInsets.only(top: 8),
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    child: Row(
                                      children: [
                                        Text(
                                          'filter_by',
                                          style: Styles.semibold(size: 16),
                                        ).tr(),
                                        Spacer(),
                                        IconButton(
                                            onPressed: () {
                                              this.setState(() {
                                                seletedIds = '0';
                                                selectedIdList.clear();
                                              });
                                              Navigator.pop(context);
                                            },
                                            icon: Icon(Icons.close))
                                      ],
                                    ),
                                  ),
                                  Divider(
                                    color: ColorConstants.GREY_4,
                                  ),
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 10, vertical: 4),
                                            child: Text(
                                              'domain',
                                              style: Styles.bold(size: 14),
                                            ).tr()),
                                        Container(
                                          child: Wrap(
                                            direction: Axis.horizontal,
                                            children: List.generate(
                                                domainList!.data!.list!.length,
                                                (i) => InkWell(
                                                      onTap: () {
                                                        setState(() {
                                                          selectedIndex = i;
                                                          seletedIds = '';
                                                          selectedIdList = [];
                                                        });
                                                        getFilterList(
                                                            domainList!.data!
                                                                .list![i].id
                                                                .toString());
                                                      },
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                left: 10,
                                                                right: 5),
                                                        child: Chip(
                                                          side: i ==
                                                                  selectedIndex
                                                              ? BorderSide(
                                                                  color: ColorConstants()
                                                                      .gradientRight())
                                                              : null,
                                                          backgroundColor: i ==
                                                                  selectedIndex
                                                              ? ColorConstants()
                                                                  .gradientRight()
                                                                  .withValues(
                                                                      alpha:
                                                                          0.08)
                                                              : Color(
                                                                  0xffF2F2F2),
                                                          label: Container(
                                                            child: Text(
                                                              '${domainList!.data!.list![i].name}',
                                                              style: Styles.semibold(
                                                                  size: 12,
                                                                  color:
                                                                      ColorConstants
                                                                          .BLACK),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    )),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      });
                    }).then((value) {});
              },
              child: Container(
                padding: EdgeInsets.only(left: 10.0),
                child: Icon(
                  Icons.filter_list,
                  color: ColorConstants.HEADING_TITLE,
                  size: 28,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  renderFilter(int selectedIndex) {
    return Container(
      height: double.infinity,
      width: double.infinity,
      decoration: BoxDecoration(
          color: ColorConstants.WHITE,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12), topRight: Radius.circular(8))),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                decoration: BoxDecoration(
                    color: ColorConstants.GREY_4,
                    borderRadius: BorderRadius.circular(8)),
                width: 48,
                height: 5,
                margin: EdgeInsets.only(top: 8),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Row(
                children: [
                  Text(
                    'filter_by',
                    style: Styles.semibold(size: 16),
                  ).tr(),
                  Spacer(),
                  IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Icon(Icons.close))
                ],
              ),
            ),
            Divider(
              color: ColorConstants.GREY_4,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text('domain').tr(),
                  ),
                  Container(
                    child: Wrap(
                      direction: Axis.horizontal,
                      children: List.generate(
                          domainList!.data!.list!.length,
                          (i) => InkWell(
                                onTap: () {
                                  getFilterList(
                                      domainList!.data!.list![i].id.toString());
                                  selectedIndex = i;
                                  setState(() {});
                                },
                                child: Padding(
                                  padding:
                                      const EdgeInsets.only(left: 10, right: 5),
                                  child: Chip(
                                    backgroundColor: i == selectedIndex
                                        ? ColorConstants.GREEN
                                        : Color(0xffF2F2F2),
                                    label: Container(
                                      child: Text(
                                          '${domainList!.data!.list![i].name}'),
                                    ),
                                  ),
                                ),
                              )),
                    ),
                  ),
                  Text("job_roles").tr(),
                  if (domainFilterList != null)
                    Container(
                      child: Wrap(
                        direction: Axis.horizontal,
                        children: List.generate(
                            domainFilterList!.data!.list.length,
                            (i) => InkWell(
                                  onTap: () {},
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                        left: 10, right: 5),
                                    child: Chip(
                                      backgroundColor: Color(0xffF2F2F2),
                                      label: Container(
                                        child: Text(
                                            '${domainFilterList!.data!.list[i].title}'),
                                      ),
                                    ),
                                  ),
                                )),
                      ),
                    ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Iterable<Widget> get shimmerChips sync* {
    for (int i = 0; i < domainList!.data!.list!.length; i++) {
      yield InkWell(
        onTap: () {
          setState(() {
            getFilterList(domainList!.data!.list![i].id.toString());
            Navigator.pop(context);
          });
        },
        child: Padding(
          padding: const EdgeInsets.only(left: 10, right: 5),
          child: Chip(
            backgroundColor: Color(0xffF2F2F2),
            label: Container(
              child: Text('${domainList!.data!.list![i].name}'),
            ),
          ),
        ),
      );
    }
  }

  Iterable<Widget> get filterChips sync* {
    for (int i = 0; i < domainFilterList!.data!.list.length; i++) {
      yield InkWell(
        onTap: () {
          getFilterList(domainFilterList!.data!.list[i].id.toString());
        },
        child: Padding(
          padding: const EdgeInsets.only(left: 10, right: 5),
          child: Chip(
            backgroundColor: Color(0xffF2F2F2),
            label: Container(
              child: Text('${domainFilterList!.data!.list[i].title}'),
            ),
          ),
        ),
      );
    }
  }

  void _handlecompetitionListResponse(CompetitionListState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          if (_callMyActivity == true) {
            competitionLoading = false;
          } else {
            competitionLoading = true;
          }

          break;
        case ApiStatus.SUCCESS:
          Log.v("CompetitionState.................... ${state.isEventType}");
          if (_callMyActivity == true) {
            myActivity = state.myActivity;
            _callMyActivity = false;
          } else {
            competitionResponse = state.competitonResponse;
            popularCompetitionResponse = state.popularCompetitionResponse;
            completedCompetition = state.competedCompetition;
            myActivity = state.myActivity;
          }

          competitionLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error CompetitionListIDState1111111 ..........................${competitionState.error}");
          competitionLoading = false;
          _callMyActivity = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handlecompetitionDetails(AppJobListCompeState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          competitionLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("CompetitionState....................");
          eventCertificate = state.response;

          competitionLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error CompetitionListIDState ..........................${competitionState}");
          competitionLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleDomainListResponse(DomainListState state) {
    var popularCompetitionState = state;
    setState(() {
      switch (popularCompetitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          popularCompetitionLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("popularCompetitionState....................");
          domainList = state.response;
          popularCompetitionLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error Popular CompetitionListIDState ..........................${popularCompetitionState.error}");
          popularCompetitionLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handletopScoring(TopScoringUserState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Portfolio Competition Loading....................");
          popularCompetitionLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("PortfolioState Competition Success....................");
          if (portfolioState.response?.data != null &&
              portfolioState.response?.data?.length != 0)
            userRank = portfolioState.response;

          popularCompetitionLoading = false;
          setState(() {});
          break;

        case ApiStatus.ERROR:
          popularCompetitionLoading = false;
          Log.v("PortfolioState Error..........................");
          Log.v(
              "PortfolioState Error..........................${portfolioState.error}");

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  int nextValue(int c, int n) {
    int x = 50 - (c % 50);
    c = (x + c) + (50 * (n - 1));
    return c;
  }

  double percentage(int c) {
    int h = nextValue(c, 1);
    int l = h - 50;
    double result = ((c - l) * 100) / 50;
    return result;
  }
}

extension on String {
  String capital() {
    return this[0].toUpperCase() + this.substring(1);
  }
}

class EventBlankPage extends StatelessWidget {
  const EventBlankPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 90,
          width: double.infinity,
          padding: EdgeInsets.all(8),
          margin: EdgeInsets.only(bottom: 6.0, top: 20.0),
          decoration: BoxDecoration(
            color: ColorConstants.WHITE,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 10,
                offset: const Offset(5, 5),
              ),
            ],
          ),
          child: Row(children: [
            Shimmer.fromColors(
              baseColor: Color(0xffe6e4e6),
              highlightColor: Color(0xffeaf0f3),
              child: Container(
                  height: 80,
                  margin: EdgeInsets.only(left: 2),
                  width: 80,
                  decoration: BoxDecoration(
                    color: Colors.white,
                  )),
            ),
            SizedBox(width: 10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Shimmer.fromColors(
                  baseColor: Color(0xffe6e4e6),
                  highlightColor: Color(0xffeaf0f3),
                  child: Container(
                      height: 12,
                      margin: EdgeInsets.only(left: 2),
                      width: 150,
                      decoration: BoxDecoration(
                        color: Colors.white,
                      )),
                ),
                SizedBox(
                  height: 7,
                ),
                Row(
                  children: [
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 100,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 100,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                  ],
                ),
                SizedBox(
                  height: 7,
                ),
                Row(
                  children: [
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 60,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                    SizedBox(
                      width: 7,
                    ),
                    SizedBox(
                      width: 7,
                    ),
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 70,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                    SizedBox(
                      width: 3,
                    ),
                    SizedBox(
                      width: 3,
                    ),
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 70,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                  ],
                )
              ],
            ),
          ]),
        ),
      ],
    );
  }
}

class BlankPage extends StatelessWidget {
  const BlankPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 10.0, top: 10.0),
          child: Text('my_events',
              style: Styles.bold(
                size: 14,
                color: ColorConstants.GREY_2,
              )).tr(),
        ),
        Container(
          height: 90,
          width: double.infinity,
          padding: EdgeInsets.all(8),
          margin: EdgeInsets.only(bottom: 6.0, top: 20.0),
          decoration: BoxDecoration(
            color: ColorConstants.WHITE,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 10,
                offset: const Offset(5, 5),
              ),
            ],
          ),
          child: Row(children: [
            Shimmer.fromColors(
              baseColor: Color(0xffe6e4e6),
              highlightColor: Color(0xffeaf0f3),
              child: Container(
                  height: 80,
                  margin: EdgeInsets.only(left: 2),
                  width: 80,
                  decoration: BoxDecoration(
                    color: Colors.white,
                  )),
            ),
            SizedBox(width: 10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Shimmer.fromColors(
                  baseColor: Color(0xffe6e4e6),
                  highlightColor: Color(0xffeaf0f3),
                  child: Container(
                      height: 12,
                      margin: EdgeInsets.only(left: 2),
                      width: 150,
                      decoration: BoxDecoration(
                        color: Colors.white,
                      )),
                ),
                SizedBox(
                  height: 7,
                ),
                Row(
                  children: [
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 100,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 100,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                  ],
                ),
                SizedBox(
                  height: 7,
                ),
                Row(
                  children: [
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 60,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                    SizedBox(
                      width: 7,
                    ),
                    SizedBox(
                      width: 7,
                    ),
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 70,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                    SizedBox(
                      width: 3,
                    ),
                    SizedBox(
                      width: 3,
                    ),
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                          height: 12,
                          margin: EdgeInsets.only(left: 2),
                          width: 70,
                          decoration: BoxDecoration(
                            color: Colors.white,
                          )),
                    ),
                  ],
                )
              ],
            ),
          ]),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 10.0, top: 15.0),
          child: Text('participate_portfolio',
              style: Styles.bold(
                size: 14,
                color: ColorConstants.HEADING_TITLE,
              )).tr(),
        ),
        Container(
          color: ColorConstants.WHITE,
          margin: EdgeInsets.all(8),
          // height: 200,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: 3,
            itemBuilder: (context, index) {
              return Container(
                height: 90,
                width: double.infinity,
                padding: EdgeInsets.all(8),
                margin: EdgeInsets.only(bottom: 6.0, top: 20.0),
                decoration: BoxDecoration(
                  color: ColorConstants.WHITE,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      offset: const Offset(5, 5),
                    ),
                  ],
                ),
                child: Row(children: [
                  Shimmer.fromColors(
                    baseColor: Color(0xffe6e4e6),
                    highlightColor: Color(0xffeaf0f3),
                    child: Container(
                        height: 80,
                        margin: EdgeInsets.only(left: 2),
                        width: 80,
                        decoration: BoxDecoration(
                          color: Colors.white,
                        )),
                  ),
                  SizedBox(width: 10),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Shimmer.fromColors(
                        baseColor: Color(0xffe6e4e6),
                        highlightColor: Color(0xffeaf0f3),
                        child: Container(
                            height: 12,
                            margin: EdgeInsets.only(left: 2),
                            width: 150,
                            decoration: BoxDecoration(
                              color: Colors.white,
                            )),
                      ),
                      SizedBox(
                        height: 7,
                      ),
                      Row(
                        children: [
                          Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 12,
                                margin: EdgeInsets.only(left: 2),
                                width: 100,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                          Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 12,
                                margin: EdgeInsets.only(left: 2),
                                width: 100,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 7,
                      ),
                      Row(
                        children: [
                          Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 12,
                                margin: EdgeInsets.only(left: 2),
                                width: 60,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                          SizedBox(
                            width: 7,
                          ),
                          SizedBox(
                            width: 7,
                          ),
                          Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 12,
                                margin: EdgeInsets.only(left: 2),
                                width: 70,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                          SizedBox(
                            width: 3,
                          ),
                          SizedBox(
                            width: 3,
                          ),
                          Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 12,
                                margin: EdgeInsets.only(left: 2),
                                width: 70,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ],
                      )
                    ],
                  ),
                ]),
              );
            },
          ),
        ),
      ],
    );
  }
}
