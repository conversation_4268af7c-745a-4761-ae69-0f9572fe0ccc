import 'dart:io';
import 'dart:isolate';
import 'dart:ui';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/assessment_certificate_response.dart';
import 'package:masterg/data/models/response/home_response/assessment_details_response.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/video_resume/preview_sample_resume_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:open_filex/open_filex.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:path_provider/path_provider.dart';

import '../../custom_pages/custom_widgets/NextPageRouting.dart';

class EventAssessmentCertificate extends StatefulWidget {
  final int? contentId;
  final int? certificateId;
  final String? htmlUrl;

  const EventAssessmentCertificate(
      {super.key, this.contentId, this.certificateId, this.htmlUrl});

  @override
  State<EventAssessmentCertificate> createState() =>
      _EventAssessmentCertificateState();
}

class _EventAssessmentCertificateState
    extends State<EventAssessmentCertificate> {
  bool isLoading = false;

  AssessmentDetailsResponse? assessmentDetails;
  AssessmentCertificateResponse? certificateUrl;

  final ReceivePort _port = ReceivePort();
  @override
  void initState() {
    getAssementDetailsCertificate(contentId: widget.contentId);

    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) {
      // String id = data[0];
      // DownloadTaskStatus status = data[1];
      // int progress = data[2];
      setState(() {});
    });

    //FlutterDownloader.registerCallback(downloadCallback as DownloadCallback); //OLD
    FlutterDownloader.registerCallback(downloadCallback); //New

    super.initState();
  }

  //static void downloadCallback(String id, DownloadTaskStatus status, int progress) {
  static void downloadCallback(String id, int status, int progress) {
    final SendPort send =
        IsolateNameServer.lookupPortByName('downloader_send_port')!;
    send.send([id, status, progress]);
  }

  void getAssementDetailsCertificate({int? contentId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(AssessmentDetailsEvent(contentId: contentId));
  }

  void getAssessmentCertificate({int? certificateId, int? contentId}) {
    BlocProvider.of<HomeBloc>(context).add(AssessmentCertificateEvent(
        contentId: contentId, certificateId: certificateId));
  }

  bool webLoading = true;
  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (context) {},
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) {
              if (state is AssessmentDetailsState) {
                _handlecompetitionDetails(state);
              }

              if (state is AssessmentCertificateState) {
                _handleAssessmentCertificate(state);
              }
            },
            child: Scaffold(
                appBar: AppBar(
                    elevation: 1,
                    leading: BackButton(color: ColorConstants.BLACK),
                    iconTheme: const IconThemeData(color: ColorConstants.WHITE),
                    backgroundColor: ColorConstants.WHITE,
                    title: Text('assessment_certificate',
                            style: Styles.bold(color: ColorConstants.BLACK))
                        .tr()),
                backgroundColor: Colors.white,
                body: ScreenWithLoader(
                    isLoading: isLoading,
                    body: Column(
                      children: [
                        Column(
                          children: [
                            SizedBox(
                                height: 300,
                                child: getCertificateList(
                                    assessmentDetails: assessmentDetails))

                            // Text('${ Uri.parse(
                            //         '${eventCertificate?.data?.certificate?.htmlUrl}')}')
                          ],
                        )
                      ],
                    )))));
  }

  Widget getCertificateList({AssessmentDetailsResponse? assessmentDetails}) {
    String url =
        '${assessmentDetails?.data?.assessmentDetails?.certificatehtmlUrl}';
    return url.isNotEmpty
        ? Container(
            color: ColorConstants.WHITE,
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            width: width(context),
            height: 300,
            child: Container(
              decoration: BoxDecoration(
                color: ColorConstants.WHITE,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.5),
                    spreadRadius: 5,
                    blurRadius: 7,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  if (isLoading == false)
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 0,
                      top: 0,
                      child: InAppWebView(
                          onLoadStart: ((controller, url) => setState(() {
                                webLoading = true;
                              })),
                          onLoadStop: ((controller, url) =>
                              Future.delayed(const Duration(seconds: 1))
                                  .then((value) => setState(() {
                                        webLoading = false;
                                      }))),
                          initialOptions: InAppWebViewGroupOptions(
                              crossPlatform: InAppWebViewOptions(
                                mediaPlaybackRequiresUserGesture: true,
                                useShouldOverrideUrlLoading: true,
                              ),
                              ios: IOSInAppWebViewOptions(
                                  allowsInlineMediaPlayback: true,
                                  allowsLinkPreview: false)),
                          initialUrlRequest: URLRequest(url: WebUri('$url'))),
                    ),
                  if (webLoading)
                    SizedBox(
                      width: double.infinity,
                      height: double.infinity,
                      child: Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        enabled: true,
                        child: Container(
                          width: 200,
                          height: 12,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  Positioned(
                    bottom: 0,
                    child: Container(
                      width: width(context) * 0.92,
                      height: 40,
                      decoration: const ShapeDecoration(
                        color: Color(0xFF303E9F),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(8),
                            bottomRight: Radius.circular(8),
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          const SizedBox(
                            width: 20,
                          ),
                          SizedBox(
                              width: 24,
                              height: 24,
                              child: Icon(Icons.picture_as_pdf,
                                  color: ColorConstants.WHITE, size: 20)),
                          const SizedBox(
                            width: 10,
                          ),
                          Text(
                            '${''}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Spacer(),
                          InkWell(
                              onTap: () async {
                                /*Navigator.push(
                                    context,
                                    NextPageRoute(PreviewSampleResume(
                                      title: tr('assessment_certificate'),
                                      previewUrl: '${certificateUrl?.url}',
                                      msg: tr('certificate_not_found'),
                                    )));*/

                                if (assessmentDetails?.data?.assessmentDetails
                                        ?.certificateUrl !=
                                    '') {
                                  Navigator.push(
                                      context,
                                      NextPageRoute(PreviewSampleResume(
                                          title: tr('assessment_certificate'),
                                          previewUrl:
                                              '${assessmentDetails?.data?.assessmentDetails?.certificateUrl}',
                                          msg: tr('certificate_not_found'))));
                                } else {
                                  final String url1 =
                                      '${assessmentDetails?.data?.assessmentDetails?.certificateHtml}';
                                  if (await canLaunchUrl(Uri.parse(url1))) {
                                    await launchUrl(Uri.parse(url1));
                                    ;
                                  } else {
                                    throw 'Could not launch $url1';
                                  }
                                }
                              },
                              child: Icon(Icons.visibility,
                                  color: ColorConstants.WHITE)),
                          /*SizedBox(width: 10),
                          InkWell(
                            onTap: () {
                              print(
                                  'Certificate is not Available${assessmentDetails?.data?.assessmentDetails?.certificatehtmlUrl}');
                              if (assessmentDetails?.data?.assessmentDetails
                                      ?.certificatehtmlUrl ==
                                  null) {
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(SnackBar(
                                  content: Text('certificate_not_found').tr(),
                                ));
                              } else {
                                // getcertificateDownloadList(
                                //     certificateId: bootCertificateList!.certificate!
                                //         .bootcampCertTest![index].certificateId!);
                                getAssessmentCertificate(
                                    certificateId: assessmentDetails?.data
                                        ?.assessmentDetails?.certificateId,
                                    contentId: widget.contentId);

                                certificateDownload(
                                    '${assessmentDetails?.data?.assessmentDetails?.certificatehtmlUrl}');
                              }
                            },
                            child: Container(
                                width: 30,
                                height: 30,
                                decoration: ShapeDecoration(
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(2)),
                                ),
                                child: Icon(Icons.download,
                                    color: ColorConstants.WHITE, size: 20)),
                          ),*/
                          const SizedBox(
                            width: 10,
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )
        : SizedBox(
            height: 300,
            child: Center(
                child: Text('certificate_not_found',
                        style: Styles.bold(color: ColorConstants.BLACK))
                    .tr()),
          );
  }

  void _handlecompetitionDetails(AssessmentDetailsState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("CompetitionState....................");
          assessmentDetails = state.response;

          isLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v(
              "Error CompetitionListIDState ..........................${competitionState}");
          isLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handleAssessmentCertificate(AssessmentCertificateState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "Success....................AssessmentCertificateState$certificateUrl");
          certificateUrl = state.response;

          isLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v("Error..........................AssessmentCertificateState");
          isLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void shareUrl(String url) {
    if (url.isNotEmpty) {
      Share.share(url, subject: 'Sharing URL');
    }
  }

  Future<void> certificateDownload(String url) async {
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    late AndroidDeviceInfo android;
    try {
      android = await plugin.androidInfo;
    } catch (e) {
      Log.v("exception file download $e");
    }
    // return;
    String localPath;

    final status = await Permission.storage.request();
    if (Platform.isIOS ||
        status.isGranted ||
        android.version.sdkInt >= 33 ||
        await Permission.storage.request().isGranted) {
      //  final externalDir = await getExternalStorageDirectory();
      final status = await Permission.storage.status;

      if (Platform.isAndroid) {
        localPath = "/sdcard/download/";
      } else {
        localPath = (await getApplicationDocumentsDirectory()).path;
      }
      final file = File("$localPath/${url.split('/').last}");
      if (!file.existsSync()) {
        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
          content: Text('Downloading Start'),
        ));

        final id = await FlutterDownloader.enqueue(
          url: url,
          savedDir: localPath,
          showNotification: true,
          saveInPublicStorage: true,
          openFileFromNotification: true,
        ).then((value) async {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Successfully Downloaded'),
          ));

          OpenFilex.open("$localPath/${url.split('/').last}");
        });
      } else {
        Utility.showSnackBar(scaffoldContext: context, message: 'file exists');
        OpenFilex.open("$localPath/${url.split('/').last}");
      }
    } else {
      launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      Log.v('Permission Denied');
    }
    return;
  }
}
