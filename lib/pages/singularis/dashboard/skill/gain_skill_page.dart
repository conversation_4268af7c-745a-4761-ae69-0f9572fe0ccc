import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:masterg/pages/ghome/widget/read_more.dart';
import 'package:masterg/pages/singularis/dashboard/skill/skill_child_card_page.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

import '../../../../blocs/home_bloc.dart';
import '../../../../local/pref/Preference.dart';
import '../../../../utils/Styles.dart';
import '../../../../utils/constant.dart';
import '../../../../utils/resource/colors.dart';
import '../../../../utils/str_to_time.dart';
import '../../../../utils/utility.dart';
import '../../../custom_pages/alert_widgets/alerts_widget.dart';
import '../../../custom_pages/custom_widgets/NextPageRouting.dart';
import '../../../ghome/my_assessments.dart';
import '../../../training_pages/new_screen/courses_details_page.dart';
import '../../competition/competition_detail.dart';
import '../../job/job_details_page.dart';
import 'model/GainSkillResponse.dart';
import 'model/skill_matching_service.dart';

class GainSkillPage extends StatefulWidget {
  final int? skillId;
  final String? skill;
  final String? level;
  final String? des;
  final int? weightagePerNo;

  GainSkillPage(
      {this.skillId,
      this.skill,
      this.level,
      this.des,
      this.weightagePerNo,
      super.key});

  @override
  State<GainSkillPage> createState() => _GainSkillPageState();
}

class _GainSkillPageState extends State<GainSkillPage>
    with SingleTickerProviderStateMixin {
  late Future<GainSkillMatchingResponse?> futureSkillMatchingData;

  ///Animation
  late final AnimationController _controllerAnim;
  late final Animation<double> _animation;
  bool isEnableEvent = false;

  List<String> listOfMonths = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ];

  @override
  void initState() {
    super.initState();
    futureSkillMatchingData = SkillMatchingService()
        .fetchSkillMatchingData(widget.skillId, widget.weightagePerNo);

    //for live text animation
    _controllerAnim = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);
    _animation = CurvedAnimation(
      parent: _controllerAnim,
      curve: Curves.easeIn,
    );
  }

  @override
  dispose() {
    // you need this
    _controllerAnim.dispose();
    super.dispose();
  }

  void jobApply(int jobId, int? isApplied) {
    BlocProvider.of<HomeBloc>(context).add(CompetitionContentListEvent(
        competitionId: jobId, isApplied: isApplied));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade200,
      appBar: AppBar(
        title: Text(
          'gain_skill',
          style: TextStyle(fontSize: 16, color: Colors.black),
        ).tr(),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(
          color: Colors.black, //change your color here
        ),
      ),
      body: FutureBuilder<GainSkillMatchingResponse?>(
        future: futureSkillMatchingData,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text(tr('error') + ': ${snapshot.error}'));
          } else if (!snapshot.hasData) {
            return Center(child: Text('no_data').tr());
          } else {
            final skillMatchingData = snapshot.data!;

            // Access the data and show it in the UI
            return ListView(
              children: [
                Container(
                  margin: EdgeInsets.all(5.0),
                  child: Column(
                    children: [
                      //TODO: Widget top1
                      Container(
                        // height: 30,
                        width: MediaQuery.of(context).size.width,
                        margin: EdgeInsets.all(8.0),
                        padding: EdgeInsets.all(8.0),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(color: Colors.white),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                SizedBox(
                                  width:
                                      MediaQuery.of(context).size.width - 220,
                                  child: Text(
                                    '${skillMatchingData.data.skillDetail.name ?? ''}',
                                    maxLines: 2,
                                    style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.black,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                                Spacer(),
                                Padding(
                                  padding: const EdgeInsets.only(left: 10.0),
                                  child: ElevatedButton(
                                    style: ButtonStyle(
                                      backgroundColor: WidgetStateProperty.all(
                                          ColorConstants.PRIMARY_COLOR),
                                      shape: WidgetStateProperty.all(
                                          RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(30))),
                                    ),
                                    child: SizedBox(
                                      width: 130,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text('assess_yourself').tr(),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 2.0),
                                            child: Icon(
                                              Icons.arrow_forward_ios_rounded,
                                              size: 18,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    onPressed: () {
                                      FirebaseAnalytics.instance
                                          .setCurrentScreen(
                                              screenName: 'Dashboard_Screen');
                                      FirebaseAnalytics.instance.logEvent(
                                          name:
                                              'user_id-${Preference.getInt(Preference.USER_ID)}',
                                          parameters: {
                                            "set_goal_event": 'Set_Your_Goal',
                                          });

                                      Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  MyAssessmentPage(
                                                    interestAreaID: '0',
                                                    skillID: widget.skillId,
                                                  )));
                                    },
                                  ),
                                ),
                              ],
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 10.0),
                              child: Text(
                                '${skillMatchingData.data.skillDetail.description ?? ''}',
                              ),
                            ),
                            SizedBox(
                              height: 15,
                            ),
                            SkillChildCard(
                              skillId: widget.skillId ?? 0,
                              skill: '${widget.skill}',
                              level: '${widget.level}',
                              rootCall: false,
                              weightagePerNo: widget.weightagePerNo ?? 0,
                            ),
                          ],
                        ),
                      ),

                      //TODO: Widget Program
                      skillMatchingData.data.gainskillMatchingPrograms.length !=
                              0
                          ? Column(
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(
                                      left: 10.0,
                                      top: Utility().isRTL(context) ? 10 : 20,
                                      right: 10.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Padding(
                                          padding: Utility().isRTL(context)
                                              ? EdgeInsets.only(right: 8)
                                              : EdgeInsets.only(left: 0.0),
                                          child: Text(
                                            'program',
                                            style: Styles.bold(
                                                color: Color(0xff0E1638),
                                                size: 14),
                                          ).tr()),
                                      Expanded(child: SizedBox()),
                                    ],
                                  ),
                                ),

                                //Programs list
                                Container(
                                    padding: EdgeInsets.only(top: 10),
                                    height:
                                        (MediaQuery.of(context).size.height *
                                            (Utility().isRTL(context)
                                                ? 0.48
                                                : 0.38)),
                                    width: MediaQuery.of(context).size.width,
                                    child: ListView.builder(
                                      itemCount: skillMatchingData.data
                                          .gainskillMatchingPrograms.length,
                                      shrinkWrap: true,
                                      scrollDirection: Axis.horizontal,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        return InkWell(
                                          onTap: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (context) => CoursesDetailsPage(
                                                      imgUrl: skillMatchingData
                                                          .data
                                                          .gainskillMatchingPrograms[
                                                              index]
                                                          .image,
                                                      indexc: index,
                                                      tagName: 'TagOther',
                                                      name: skillMatchingData
                                                          .data
                                                          .gainskillMatchingPrograms[
                                                              index]
                                                          .name,
                                                      description: skillMatchingData
                                                          .data
                                                          .gainskillMatchingPrograms[
                                                              index]
                                                          .description,
                                                      regularPrice: null,
                                                      salePrice: null,
                                                      trainer: '',
                                                      enrolmentCount: null,
                                                      type: skillMatchingData
                                                          .data
                                                          .gainskillMatchingPrograms[
                                                              index]
                                                          .subscriptionType,
                                                      id: skillMatchingData
                                                          .data
                                                          .gainskillMatchingPrograms[
                                                              index]
                                                          .id,
                                                      shortCode: '',
                                                      isSubscribed: false)),
                                            ).then((isSuccess) {
                                              if (isSuccess == true) {
                                                //_getFilteredPopularCourses();
                                              }
                                            });
                                          },
                                          child: _programWidget(
                                            name: skillMatchingData
                                                .data
                                                .gainskillMatchingPrograms[
                                                    index]
                                                .name,
                                            image: skillMatchingData
                                                .data
                                                .gainskillMatchingPrograms[
                                                    index]
                                                .image,
                                            orgName: skillMatchingData
                                                .data
                                                .gainskillMatchingPrograms[
                                                    index]
                                                .orgName,
                                            duration: skillMatchingData
                                                .data
                                                .gainskillMatchingPrograms[
                                                    index]
                                                .duration,
                                          ),
                                        );
                                      },
                                    )),
                                SizedBox(
                                  height: 20,
                                ),
                              ],
                            )
                          : SizedBox(),

                      //TODO: Widget Competitions
                      skillMatchingData.data.gainskillMatchingComp.length != 0
                          ? _competitionsWidget(
                              skillMatchingData.data.gainskillMatchingComp)
                          : SizedBox(),

                      //TODO: Widget Mentorship
                      skillMatchingData.data.gainskillMentorship.length != 0
                          ? Column(
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(
                                      left: 10.0,
                                      top: Utility().isRTL(context) ? 10 : 20,
                                      right: 10.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Padding(
                                          padding: Utility().isRTL(context)
                                              ? EdgeInsets.only(right: 8)
                                              : EdgeInsets.only(left: 0.0),
                                          child: Text(
                                            'mentorship',
                                            style: Styles.bold(
                                                color: Color(0xff0E1638),
                                                size: 14),
                                          ).tr()),
                                      Expanded(child: SizedBox()),
                                    ],
                                  ),
                                ),
                                Container(
                                    padding: EdgeInsets.only(top: 10),
                                    height:
                                        (MediaQuery.of(context).size.height *
                                            (Utility().isRTL(context)
                                                ? 0.48
                                                : 0.44)),
                                    width: MediaQuery.of(context).size.width,
                                    child: ListView.builder(
                                      itemCount: skillMatchingData
                                          .data.gainskillMentorship.length,
                                      shrinkWrap: true,
                                      scrollDirection: Axis.horizontal,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        return _memberShipWidget(
                                          name: skillMatchingData.data
                                              .gainskillMentorship[index].name,
                                          image: skillMatchingData
                                              .data
                                              .gainskillMentorship[index]
                                              .profileImage,
                                          experience: skillMatchingData
                                              .data
                                              .gainskillMentorship[index]
                                              .experienceYear,
                                          educationQualif: skillMatchingData
                                              .data
                                              .gainskillMentorship[index]
                                              .educationQualification,
                                          functionalArea: skillMatchingData
                                              .data
                                              .gainskillMentorship[index]
                                              .functionalArea,
                                          gainSkillMentorship: skillMatchingData
                                              .data.gainskillMentorship,
                                          clickIndex: index,
                                        );
                                      },
                                    )),
                                SizedBox(
                                  height: 20,
                                ),
                              ],
                            )
                          : SizedBox(),

                      //TODO: Widget Industry Training Jobs
                      skillMatchingData.data.gainskillMatchingJobs.length != 0
                          ? Column(
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(
                                      left: 10.0,
                                      top: Utility().isRTL(context) ? 10 : 20,
                                      right: 10.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Padding(
                                          padding: Utility().isRTL(context)
                                              ? EdgeInsets.only(right: 8)
                                              : EdgeInsets.only(left: 0.0),
                                          child: Text(
                                            'industry_training',
                                            style: Styles.bold(
                                                color: Color(0xff0E1638),
                                                size: 14),
                                          ).tr()),
                                      Expanded(child: SizedBox()),
                                    ],
                                  ),
                                ),
                                Container(
                                    padding: EdgeInsets.only(top: 10),
                                    //height: (MediaQuery.of(context).size.height * (Utility().isRTL(context) ? 0.48 : 0.44)),
                                    width: MediaQuery.of(context).size.width,
                                    child: ListView.builder(
                                      itemCount: skillMatchingData
                                          .data.gainskillMatchingJobs.length,
                                      //itemCount: 2,
                                      shrinkWrap: true,
                                      scrollDirection: Axis.vertical,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        return InkWell(
                                          onTap: () {
                                            Navigator.push(
                                                context,
                                                NextPageRoute(JobDetailsPage(
                                                  title: skillMatchingData
                                                      .data
                                                      .gainskillMatchingJobs[
                                                          index]
                                                      .name,
                                                  description: skillMatchingData
                                                      .data
                                                      .gainskillMatchingJobs[
                                                          index]
                                                      .description,
                                                  location: skillMatchingData
                                                      .data
                                                      .gainskillMatchingJobs[
                                                          index]
                                                      .workAddress,
                                                  skillNames: skillMatchingData
                                                      .data
                                                      .gainskillMatchingJobs[
                                                          index]
                                                      .skillNames,
                                                  companyName: skillMatchingData
                                                      .data
                                                      .gainskillMatchingJobs[
                                                          index]
                                                      .organizedBy,
                                                  domain: skillMatchingData
                                                      .data
                                                      .gainskillMatchingJobs[
                                                          index]
                                                      .domainName,
                                                  companyThumbnail:
                                                      skillMatchingData
                                                          .data
                                                          .gainskillMatchingJobs[
                                                              index]
                                                          .image,
                                                  experience: skillMatchingData
                                                      .data
                                                      .gainskillMatchingJobs[
                                                          index]
                                                      .experience,
                                                  jobStatusNumeric:
                                                      skillMatchingData
                                                          .data
                                                          .gainskillMatchingJobs[
                                                              index]
                                                          .jobStatusNumeric,
                                                  id: skillMatchingData
                                                      .data
                                                      .gainskillMatchingJobs[
                                                          index]
                                                      .id,
                                                  jobStatus: skillMatchingData
                                                      .data
                                                      .gainskillMatchingJobs[
                                                          index]
                                                      .jobStatus,
                                                  vacancy: skillMatchingData
                                                      .data
                                                      .gainskillMatchingJobs[
                                                          index]
                                                      .numOfVacancy,
                                                  minExperience:
                                                      skillMatchingData
                                                          .data
                                                          .gainskillMatchingJobs[
                                                              index]
                                                          .minExperience,
                                                  maxExperience:
                                                      skillMatchingData
                                                          .data
                                                          .gainskillMatchingJobs[
                                                              index]
                                                          .maxExperience,
                                                )));
                                          },
                                          child: Column(
                                            children: [
                                              Container(
                                                color: ColorConstants.WHITE,
                                                width: double.infinity,
                                                margin: EdgeInsets.only(
                                                    left: 10.0, right: 10.0),
                                                child: Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: 16,
                                                      vertical: 16),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    children: [
                                                      Expanded(
                                                        flex: 3,
                                                        child: Container(
                                                          padding:
                                                              EdgeInsets.only(
                                                            right: 10.0,
                                                          ),
                                                          child: skillMatchingData
                                                                      .data
                                                                      .gainskillMatchingJobs[
                                                                          index]
                                                                      .image !=
                                                                  null
                                                              ? Image.network(
                                                                  '${skillMatchingData.data.gainskillMatchingJobs[index].image}',
                                                                )
                                                              : Image.asset(
                                                                  'assets/images/pb_2.png'),
                                                        ),
                                                      ),
                                                      Expanded(
                                                        flex: 9,
                                                        child: Container(
                                                          padding:
                                                              EdgeInsets.only(
                                                                  left: 5.0,
                                                                  right: 5.0),
                                                          child: Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Text(
                                                                '${skillMatchingData.data.gainskillMatchingJobs[index].name ?? ''}',
                                                                style: Styles.bold(
                                                                    size: 16,
                                                                    color: ColorConstants
                                                                        .HEADING_TITLE),
                                                              ),
                                                              Padding(
                                                                padding:
                                                                    const EdgeInsets
                                                                        .only(
                                                                        top:
                                                                            6.0,
                                                                        right:
                                                                            5.0),
                                                                child: Text(
                                                                  '${skillMatchingData.data.gainskillMatchingJobs[index].organizedBy ?? ''}',
                                                                  style: Styles
                                                                      .regular(
                                                                    size: 13,
                                                                    color: ColorConstants
                                                                        .SUB_HEADING_TITLE,
                                                                  ),
                                                                ),
                                                              ),
                                                              Padding(
                                                                padding:
                                                                    const EdgeInsets
                                                                        .only(
                                                                        top:
                                                                            5.0),
                                                                child: Row(
                                                                  children: [
                                                                    skillMatchingData.data.gainskillMatchingJobs[index].minExperience !=
                                                                                null ||
                                                                            skillMatchingData.data.gainskillMatchingJobs[index].maxExperience !=
                                                                                null
                                                                        ? SizedBox(
                                                                            child:
                                                                                Row(
                                                                              children: [
                                                                                Icon(
                                                                                  Icons.work_outline,
                                                                                  size: 14,
                                                                                  color: ColorConstants.BODY_TEXT,
                                                                                ),
                                                                                Padding(
                                                                                  padding: EdgeInsets.only(
                                                                                    left: Utility().isRTL(context) ? 0 : 5.0,
                                                                                    right: Utility().isRTL(context) ? 5.0 : 0.0,
                                                                                  ),
                                                                                  child: Text(
                                                                                    '${tr('exp')}: ',
                                                                                    style: Styles.regular(
                                                                                      size: 13,
                                                                                      color: ColorConstants.BODY_TEXT,
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                                Text('${skillMatchingData.data.gainskillMatchingJobs[index].minExperience != null ? skillMatchingData.data.gainskillMatchingJobs[index].minExperience : "0"}' + '-${skillMatchingData.data.gainskillMatchingJobs[index].maxExperience != null ? skillMatchingData.data.gainskillMatchingJobs[index].maxExperience : "0"} ${tr('yrs')} ', style: Styles.regular(size: 12, color: ColorConstants.GREY_3)),
                                                                              ],
                                                                            ),
                                                                          )
                                                                        : SizedBox(),
                                                                    if (skillMatchingData.data.gainskillMatchingJobs[index].workAddress !=
                                                                            null &&
                                                                        skillMatchingData.data.gainskillMatchingJobs[index].workAddress !=
                                                                            "") ...[
                                                                      Row(
                                                                        children: [
                                                                          Padding(
                                                                            padding:
                                                                                EdgeInsets.only(
                                                                              left: Utility().isRTL(context) ? 0 : 5.0,
                                                                              right: Utility().isRTL(context) ? 5.0 : 0.0,
                                                                            ),
                                                                            child:
                                                                                Icon(
                                                                              Icons.location_on_outlined,
                                                                              size: 14,
                                                                              color: ColorConstants.BODY_TEXT,
                                                                            ),
                                                                          ),
                                                                          SizedBox(
                                                                            child:
                                                                                Text(
                                                                              '${skillMatchingData.data.gainskillMatchingJobs[index].workAddress}',
                                                                              softWrap: true,
                                                                              overflow: TextOverflow.ellipsis,
                                                                              style: Styles.regular(
                                                                                size: 13,
                                                                                color: ColorConstants.BODY_TEXT,
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    ],
                                                                  ],
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      Expanded(
                                                        flex: 2,
                                                        child: skillMatchingData
                                                                        .data
                                                                        .gainskillMatchingJobs[
                                                                            index]
                                                                        .jobStatus ==
                                                                    null ||
                                                                skillMatchingData
                                                                        .data
                                                                        .gainskillMatchingJobs[
                                                                            index]
                                                                        .jobStatus ==
                                                                    "" ||
                                                                skillMatchingData
                                                                        .data
                                                                        .gainskillMatchingJobs[
                                                                            index]
                                                                        .jobStatus
                                                                        ?.toLowerCase() ==
                                                                    "pending"
                                                            ? InkWell(
                                                                onTap: () {
                                                                  FirebaseAnalytics
                                                                      .instance
                                                                      .logEvent(
                                                                    name:
                                                                        'jobs_internships',
                                                                    parameters: {
                                                                      "job_apply_dashboard":
                                                                          'Apply',
                                                                      "job_name":
                                                                          skillMatchingData.data.gainskillMatchingJobs[index].name ??
                                                                              '',
                                                                    },
                                                                  );
                                                                  //jobApply(int.parse('${matchingJobsResp![index].programId}',), 1,);
                                                                  jobApply(
                                                                    int.parse(
                                                                      '${skillMatchingData.data.gainskillMatchingJobs[index].id}',
                                                                    ),
                                                                    1,
                                                                  );
                                                                  setState(() {
                                                                    skillMatchingData
                                                                        .data
                                                                        .gainskillMatchingJobs
                                                                        .removeAt(
                                                                            index);
                                                                  });
                                                                  _onLoadingForJob();
                                                                },
                                                                child:
                                                                    Container(
                                                                  // padding: EdgeInsets.only(
                                                                  //     left: 8),
                                                                  child:
                                                                      GradientText(
                                                                    tr('apply_button'),
                                                                    style: Styles
                                                                        .bold(
                                                                            size:
                                                                                14),
                                                                    colors: [
                                                                      ColorConstants()
                                                                          .gradientLeft(),
                                                                      ColorConstants()
                                                                          .gradientRight(),
                                                                    ],
                                                                  ),
                                                                ),
                                                              )
                                                            : Padding(
                                                                padding:
                                                                    const EdgeInsets
                                                                        .only(
                                                                        bottom:
                                                                            20.0),
                                                                child: Text(
                                                                  'applied',
                                                                  style: Styles.bold(
                                                                      color: Colors
                                                                          .green,
                                                                      size: 12),
                                                                ).tr(),
                                                              ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 10.0, right: 10.0),
                                                child: Divider(
                                                  height: 1,
                                                  thickness: 1,
                                                  color: ColorConstants.GREY_10,
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    )),
                                SizedBox(
                                  height: 20,
                                ),
                              ],
                            )
                          : SizedBox(),

                      //TODO: Widget Matching Internships
                      skillMatchingData
                                  .data.gainskillMatchingInternships.length !=
                              0
                          ? Column(
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(
                                      left: 10.0,
                                      top: Utility().isRTL(context) ? 10 : 20,
                                      right: 10.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Padding(
                                          padding: EdgeInsets.all(1),
                                          child: Text(
                                            'internships',
                                            style: Styles.bold(
                                                color: Color(0xff0E1638),
                                                size: 14),
                                          ).tr()),
                                      Expanded(child: SizedBox()),
                                    ],
                                  ),
                                ),

                                //Programs list
                                Container(
                                    padding: EdgeInsets.only(top: 10),
                                    //color: Colors.white,
                                    //height: (MediaQuery.of(context).size.height * (Utility().isRTL(context) ? 0.48 : 0.38)),
                                    width: MediaQuery.of(context).size.width,
                                    child: ListView.builder(
                                      itemCount: skillMatchingData.data
                                          .gainskillMatchingInternships.length,
                                      shrinkWrap: true,
                                      scrollDirection: Axis.vertical,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        String startDateString =
                                            "${skillMatchingData.data.gainskillMatchingInternships[index].startDate}";
                                        DateTime startDate =
                                            DateFormat("yyyy-MM-dd")
                                                .parse(startDateString);

                                        DateTime endDate = DateTime.now();
                                        if (skillMatchingData
                                                .data
                                                .gainskillMatchingInternships[
                                                    index]
                                                .endDate !=
                                            '') {
                                          String endDateString =
                                              "${skillMatchingData.data.gainskillMatchingInternships[index].endDate}";
                                          endDate = DateFormat("yyyy-MM-dd")
                                              .parse(endDateString);
                                        }

                                        return Transform.translate(
                                          offset: Offset(0, -10),
                                          child: Container(
                                            margin: EdgeInsets.all(10),
                                            padding: EdgeInsets.all(10),
                                            color: Colors.white,
                                            child: InkWell(
                                              onTap: () {
                                                Navigator.push(
                                                    context,
                                                    NextPageRoute(
                                                        JobDetailsPage(
                                                      title: skillMatchingData
                                                          .data
                                                          .gainskillMatchingInternships[
                                                              index]
                                                          .name,
                                                      description: skillMatchingData
                                                          .data
                                                          .gainskillMatchingInternships[
                                                              index]
                                                          .description,
                                                      location: '',
                                                      skillNames: skillMatchingData
                                                          .data
                                                          .gainskillMatchingInternships[
                                                              index]
                                                          .name,
                                                      companyName: skillMatchingData
                                                          .data
                                                          .gainskillMatchingInternships[
                                                              index]
                                                          .organizedBy,
                                                      domain: skillMatchingData
                                                          .data
                                                          .gainskillMatchingInternships[
                                                              index]
                                                          .orgName,
                                                      companyThumbnail:
                                                          skillMatchingData
                                                              .data
                                                              .gainskillMatchingInternships[
                                                                  index]
                                                              .image,
                                                      experience: '',
                                                      jobStatusNumeric: null,
                                                      id: skillMatchingData
                                                          .data
                                                          .gainskillMatchingInternships[
                                                              index]
                                                          .id,
                                                      jobStatus: skillMatchingData
                                                          .data
                                                          .gainskillMatchingInternships[
                                                              index]
                                                          .status,
                                                      vacancy: null,
                                                      minExperience: null,
                                                      maxExperience: null,
                                                    )));
                                              },
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(10),
                                                        child: SizedBox(
                                                          width:
                                                              width(context) *
                                                                  0.2,
                                                          height:
                                                              width(context) *
                                                                  0.2,
                                                          child:
                                                              CachedNetworkImage(
                                                            imageUrl:
                                                                '${skillMatchingData.data.gainskillMatchingInternships[index].image}',
                                                            fit: BoxFit.cover,
                                                            errorWidget:
                                                                (context, url,
                                                                        data) =>
                                                                    Image.asset(
                                                              "assets/images/certificate_dummy.png",
                                                              fit: BoxFit.cover,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 10,
                                                      ),
                                                      SizedBox(
                                                        width: width(context) *
                                                            0.6,
                                                        child: Column(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .start,
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Text(
                                                              '${skillMatchingData.data.gainskillMatchingInternships[index].name}',
                                                              style:
                                                                  Styles.bold(
                                                                      size: 14),
                                                            ),
                                                            SizedBox(
                                                              height: 10,
                                                            ),
                                                            Text(
                                                              '${skillMatchingData.data.gainskillMatchingInternships[index].workMode}',
                                                              style: Styles
                                                                  .regular(
                                                                      size: 12),
                                                            ),
                                                            SizedBox(
                                                              height: 10,
                                                            ),
                                                            Text(
                                                              '${calculateTimeDifferenceBetween(startDate, endDate)} • ${listOfMonths[startDate.month - 1].substring(0, 3)} ${startDate.year.toString().substring(2, 4)}  -  ' +
                                                                  ' ${listOfMonths[endDate.month - 1].substring(0, 3)} ${endDate.year.toString().substring(2, 4)}',
                                                              style: Styles
                                                                  .regular(
                                                                      size: 12),
                                                            ),
                                                          ],
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                  SizedBox(
                                                    height: 8,
                                                  ),
                                                  ReadMoreText(
                                                    text:
                                                        '${skillMatchingData.data.gainskillMatchingInternships[index].description}',
                                                    color:
                                                        ColorConstants.GREY_3,
                                                  ),
                                                  if (index + 1 !=
                                                      min(
                                                          2,
                                                          int.parse(
                                                              '${skillMatchingData.data.gainskillMatchingInternships.length}')))
                                                    Padding(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          vertical: 12),
                                                      child: Divider(),
                                                    )
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    )),
                                SizedBox(
                                  height: 20,
                                ),
                              ],
                            )
                          : SizedBox(),
                    ],
                  ),
                )
              ],
            );
          }
        },
      ),
    );
  }

  Widget _programWidget(
      {String? name, String? image, String? orgName, int? duration}) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.7,
      margin: EdgeInsets.only(
          right: Utility().isRTL(context) ? 0 : 15,
          left: Utility().isRTL(context) ? 15 : 10),
      decoration: BoxDecoration(
          color: ColorConstants.WHITE, borderRadius: BorderRadius.circular(10)),
      child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.width * 0.5,
              width: MediaQuery.of(context).size.width,
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10)),
                child: Image.network(
                  '${image}',
                  errorBuilder: (context, error, stackTrace) {
                    return SvgPicture.asset(
                      'assets/images/gscore_postnow_bg.svg',
                    );
                  },
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 8, right: 8),
              child: Text('${name}',
                  overflow: TextOverflow.ellipsis,
                  style: Styles.bold(
                      size: 16, color: ColorConstants.HEADING_TITLE)),
            ),
            SizedBox(
              height: 4,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 8, right: 8),
              child: Text(
                  '${tr('by')} ${Utility().decrypted128(orgName ?? '')}',
                  style: Styles.regular(
                      size: 12, color: ColorConstants.BODY_TEXT)),
            ),
            SizedBox(
              height: 5,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 8, right: 8),
              child: Row(
                children: [
                  Icon(CupertinoIcons.clock_fill,
                      size: 16, color: ColorConstants.BODY_TEXT),
                  SizedBox(
                    width: 4,
                  ),
                  duration != null
                      ? Text('${duration} ${tr('hrs')}',
                          overflow: TextOverflow.clip,
                          maxLines: 1,
                          style: Styles.regular(
                              size: 12, color: ColorConstants.BODY_TEXT))
                      : SizedBox(),
                ],
              ),
            )
          ]),
    );
  }

  //TODO:Competition Widgets
  Widget _competitionsWidget(
      List<GainSkillMatchingComp> gainSkillMatchingComp) {
    return Container(
      margin: EdgeInsets.only(top: 8),
      decoration: BoxDecoration(color: ColorConstants.WHITE),
      padding: const EdgeInsets.only(top: 16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      // Icon(Icons.emoji_events_outlined, size: 18),
                      //SizedBox(width: 10),
                      Text(
                        'competitions',
                        style: Styles.bold(
                            color: ColorConstants.HEADING_TITLE, size: 14),
                      ).tr(),
                      Spacer(),
                    ],
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 6),
          Divider(thickness: 1, color: ColorConstants.DIVIDER_COLOR_1),
          Container(
            child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Column(
                  children: [
                    ListView.builder(
                        itemCount: gainSkillMatchingComp.length ?? 0,
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        itemBuilder: (BuildContext context, int index) {
                          String startDate =
                              '${gainSkillMatchingComp[index].startDate?.split(' ').first}';

                          DateTime dateTime = DateTime.parse(startDate);

                          String endDate =
                              '${gainSkillMatchingComp[index].endDate?.split(' ').first}';
                          dateTime = DateTime.parse(endDate);

                          return InkWell(
                              onTap: () {
                                FirebaseAnalytics.instance.logEvent(
                                    name: 'competitions_dashboard',
                                    parameters: {
                                      "competitions_name":
                                          gainSkillMatchingComp[index].name ??
                                              '',
                                    });
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (BuildContext context) =>
                                            CompetitionDetail(
                                                competitionId:
                                                    gainSkillMatchingComp[index]
                                                        .id)));
                              },
                              child: viewCompetitionCard(
                                '${gainSkillMatchingComp[index].image ?? ''}',
                                '${gainSkillMatchingComp[index].name ?? ''}',
                                '${gainSkillMatchingComp[index].orgName ?? ''}',
                                '${gainSkillMatchingComp[index].competitionLevel ?? tr('easy')}',
                                '${gainSkillMatchingComp[index].gScore}',
                                '${gainSkillMatchingComp[index].startDate}',
                                '${gainSkillMatchingComp[index].endDate}',
                                true,
                                '${gainSkillMatchingComp[index].status}',
                              ));
                        }),
                  ],
                )),
          ),
        ],
      ),
    );
  }

  viewCompetitionCard(
    String competitionImg,
    String name,
    String companyName,
    String difficulty,
    String gScore,
    String startdate,
    String endDate,
    bool enablePStatus,
    String progressStatus,
  ) {
    return Stack(
      children: [
        Container(
          height: enablePStatus == true ? 110 : 122,
          width: double.infinity,
          padding: EdgeInsets.all(8),
          margin: EdgeInsets.symmetric(vertical: 7),
          decoration: BoxDecoration(
            color: ColorConstants.WHITE,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: ColorConstants.DIVIDER_COLOR_1, width: 1),
          ),
          child: Row(children: [
            Container(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: CachedNetworkImage(
                  imageUrl: competitionImg,
                  width: enablePStatus == true ? 90 : 95,
                  height: enablePStatus == true ? 90 : 90,
                  errorWidget: (context, url, error) => SvgPicture.asset(
                    'assets/images/event_default.svg',
                    height: 10,
                    width: 10,
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(width: 10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              //mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                SizedBox(
                  width: width(context) * 0.5,
                  child: Text(
                    name,
                    style: Styles.bold(size: 16),
                    maxLines: 1,
                    softWrap: true,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                companyName.isNotEmpty
                    ? SizedBox(
                        width: MediaQuery.of(context).size.width * 0.62,
                        child: Text(
                          companyName,
                          maxLines: 1,
                          style: Styles.semibold(
                              size: 11, color: ColorConstants.GREY_3),
                        ),
                      )
                    : SizedBox(),
                SizedBox(height: 2),

                //TODO: Show Live
                enablePStatus == true
                    ? progressStatus.toLowerCase() == 'live'
                        ? Row(
                            children: [
                              SvgPicture.asset(
                                'assets/images/live_icon.svg',
                                fit: BoxFit.fitHeight,
                              ),
                              SizedBox(width: 3),
                              progressStatus.toLowerCase() == 'live'
                                  ? FadeTransition(
                                      opacity: _animation,
                                      child: Text(
                                        '${'live'}',
                                        style: Styles.bold(
                                            color: ColorConstants.RED,
                                            size: 16),
                                      ).tr(),
                                    )
                                  : Text('$progressStatus',
                                      style: Styles.bold(
                                          color: ColorConstants.RED, size: 16))
                            ],
                          )
                        : SizedBox()
                    : SizedBox(),

                enablePStatus == true
                    ? SizedBox()
                    : Padding(
                        padding: const EdgeInsets.only(top: 3.0),
                        child: Row(
                          children: [
                            Text('${difficulty.capital()}',
                                style: Styles.regular(
                                    color: ColorConstants.GREEN_1, size: 12)),
                            SizedBox(
                              width: 4,
                            ),
                            Text('•',
                                style: Styles.regular(
                                    color: ColorConstants.GREY_2, size: 12)),
                            SizedBox(
                              width: 4,
                            ),
                            SizedBox(
                                height: 15,
                                child: Image.asset('assets/images/coin.png')),
                            SizedBox(
                              width: 4,
                            ),
                            Text('$gScore ${tr('points')}',
                                style: Styles.regular(
                                    color: ColorConstants.ORANGE_4, size: 12)),
                          ],
                        ),
                      ),
                enablePStatus == true && progressStatus.toLowerCase() == 'live'
                    ? SizedBox()
                    : Padding(
                        padding: const EdgeInsets.only(top: 2.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.calendar_month,
                              color: ColorConstants.BODY_TEXT,
                              size: 18,
                            ),
                            SizedBox(
                              width: 4,
                            ),

                            //TODO: Show start date Time
                            StrToTime(
                              time: startdate,
                              dateFormat: ' dd-MMM-yy ',
                              //appendString: Utility().isRTL(context) ? '' : tr('to'),
                              appendString: ',',
                              textStyle: Styles.regular(
                                  size: 12, color: Color(0xff5A5F73)),
                            ),

                            StrToTime(
                              time: startdate,
                              dateFormat: ' hh:mm a ',
                              //appendString: Utility().isRTL(context) ? '' : tr('to'),
                              appendString: '',
                              textStyle: Styles.regular(
                                  size: 12,
                                  lineHeight: 1,
                                  color: ColorConstants.BODY_TEXT),
                            ),
                          ],
                        ),
                      ),
                enablePStatus == true && progressStatus.toLowerCase() == 'live'
                    ? SizedBox()
                    : Padding(
                        padding: const EdgeInsets.only(top: 5.0),
                        child: Row(
                          children: [
                            /*Icon(
                              Icons.access_time,
                              color: ColorConstants.BODY_TEXT,
                              size: 16,
                            ),*/
                            Text(
                              'To',
                              style: Styles.bold(
                                  size: 13,
                                  lineHeight: 1,
                                  color: ColorConstants.BODY_TEXT),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            //TODO: Show end date time
                            StrToTime(
                              time: endDate,
                              dateFormat: ' dd-MMM-yy ',
                              //appendString: Utility().isRTL(context) ? tr('to') : '',
                              appendString: ',',
                              textStyle: Styles.regular(
                                  size: 12, color: Color(0xff5A5F73)),
                            ),

                            StrToTime(
                              time: endDate,
                              dateFormat: ' hh:mm aa ',
                              appendString:
                                  Utility().isRTL(context) ? tr('to') : '',
                              textStyle: Styles.regular(
                                  size: 12,
                                  lineHeight: 1,
                                  color: Color.fromARGB(255, 4, 6, 16)),
                            ),
                          ],
                        ),
                      ),
              ],
            ),
          ]),
        ),
        Positioned(
            right: Utility().isRTL(context) ? null : 8,
            left: Utility().isRTL(context) ? 8 : null,
            top: 10,
            bottom: 10,
            child: Icon(
              (Icons.arrow_forward_ios),
              size: 20,
            )),
      ],
    );
  }

  //TODO: Member Ship Widget
  Widget _memberShipWidget(
      {List<Mentorship>? gainSkillMentorship,
      int? clickIndex,
      String? name,
      String? image,
      int? experience,
      String? educationQualif,
      String? functionalArea}) {
    return InkWell(
      onTap: () {},
      child: Container(
        width: MediaQuery.of(context).size.width * 0.7,
        margin: EdgeInsets.only(
            right: Utility().isRTL(context) ? 0 : 15,
            left: Utility().isRTL(context) ? 15 : 10),
        decoration: BoxDecoration(
            color: ColorConstants.WHITE,
            borderRadius: BorderRadius.circular(10)),
        child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 10,
              ),
              SizedBox(
                //height: MediaQuery.of(context).size.width * 0.5,
                //width: MediaQuery.of(context).size.width,
                child: ClipRRect(
                  borderRadius: BorderRadius.all(Radius.circular(100)),
                  child: Image.network(
                    '${image}',
                    height: 100,
                    width: 100,
                    errorBuilder: (context, error, stackTrace) {
                      return SvgPicture.asset(
                        'assets/images/gscore_postnow_bg.svg',
                      );
                    },
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8, right: 8),
                child: Text('${name}',
                    overflow: TextOverflow.ellipsis,
                    style: Styles.bold(
                        size: 16, color: ColorConstants.HEADING_TITLE)),
              ),
              SizedBox(
                height: 4,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8, right: 8),
                child: Text(
                    '${Utility().decrypted128('${experience}' ?? '')} ${tr('year_exp')} ',
                    style: Styles.regular(
                        size: 12, color: ColorConstants.BODY_TEXT)),
              ),
              SizedBox(
                height: 5,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8, right: 8, top: 20),
                child: Text('${educationQualif}',
                    overflow: TextOverflow.clip,
                    maxLines: 1,
                    style: Styles.regular(
                        size: 12, color: ColorConstants.BODY_TEXT)),
              ),
              Divider(),
              Padding(
                padding: const EdgeInsets.only(left: 8, right: 8, top: 2),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      'specialties',
                      style: Styles.regular(size: 10),
                    ).tr(),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 8, right: 8, top: 6),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '${functionalArea}',
                      style: Styles.regular(size: 12, color: Colors.black),
                    ),
                  ],
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.only(left: 10.0, right: 10.0, top: 30.0),
                child: ElevatedButton(
                  style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(ColorConstants.PRIMARY_COLOR),
                    shape: WidgetStateProperty.all(RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5))),
                  ),
                  child: SizedBox(
                    //width: 130,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text('book_an_appointment').tr(),
                      ],
                    ),
                  ),
                  onPressed: () {
                    /*Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) =>
                                MentorshipViewDetailsPage(
                                    gainSkillMentorship: gainSkillMentorship,
                                index: clickIndex,)));*/
                    AlertsWidget.showCustomDialog(
                        context: context,
                        showCancel: false,
                        title: tr('your_appointment_book_msg'),
                        oKText: tr('ok'),
                        icon: 'assets/images/circle_alert_fill.svg',
                        onOkClick: () async {});
                  },
                ),
              ),
            ]),
      ),
    );
  }

  void _onLoadingForJob() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        Future.delayed(Duration(seconds: 2), () {
          Navigator.of(context).pop(true);
          Get.rawSnackbar(
            messageText: Text(
              'application_submitted',
              style: Styles.regular(size: 14, color: ColorConstants.WHITE),
            ).tr(),
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: ColorConstants.BLACK,
            borderRadius: 4,
            duration: Duration(seconds: 3),
            boxShadows: [
              BoxShadow(
                  color: Color(0xff898989).withValues(alpha: 0.1),
                  offset: Offset(0, 4.0),
                  blurRadius: 11)
            ],
          );
        });

        return Dialog(
          child: Container(
            padding: EdgeInsets.only(left: 20, right: 10),
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: new Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                new CircularProgressIndicator(
                  color: Colors.blue,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10.0),
                  child: new Text("${tr('job_apply')}..."),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String calculateTimeDifferenceBetween(DateTime startDate, DateTime endDate) {
    int seconds = endDate.difference(startDate).inSeconds;
    if (seconds < 60) {
      if (seconds.abs() < 4) return '${tr('now')}';
      return '${seconds.abs()} ${tr('second')}';
    } else if (seconds >= 60 && seconds < 3600)
      return '${startDate.difference(endDate).inMinutes.abs()} ${tr('mins')}';
    else if (seconds >= 3600 && seconds < 86400)
      return '${startDate.difference(endDate).inHours.abs()} ${tr('hour')}';
    else {
      // convert day to month
      int days = startDate.difference(endDate).inDays.abs();
      if (days < 30 && days > 7) {
        return '${(startDate.difference(endDate).inDays ~/ 7).abs()} ${tr('week')}';
      }
      if (days > 30) {
        int month = (startDate.difference(endDate).inDays ~/ 30).abs();
        return '$month ${tr('month')}';
      } else
        return '${startDate.difference(endDate).inDays.abs()} ${tr('day')}';
    }
  }
}

extension on String {
  String capital() {
    return this[0].toUpperCase() + this.substring(1);
  }
}
