import 'dart:async';

import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/pages/explore_job/explore_job_bottom_sheet.dart';
import 'package:masterg/pages/explore_job/explore_job_details_page.dart';
import 'package:masterg/pages/explore_job/widgets/filter_pop.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/infinite_rotate.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/resource/size_constants.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

import '../../../blocs/bloc_manager.dart';
import '../../../blocs/home_bloc.dart';
import '../../../data/models/response/home_response/competition_response.dart';
import '../../../data/models/response/home_response/domain_filter_list.dart';
import '../../../data/models/response/home_response/domain_list_response.dart';
import '../../../data/models/response/home_response/training_module_response.dart';
import '../../../utils/Styles.dart';
import '../../data/models/response/home_response/ExploreJobListResponse.dart';
import '../../data/models/response/home_response/new_portfolio_response.dart';
import '../../local/pref/Preference.dart';
import '../custom_pages/ScreenWithLoader.dart';
import '../custom_pages/custom_widgets/NextPageRouting.dart';
import '../singularis/job/job_details_page.dart';
import 'explore_job_proivder.dart';
import 'widgets/resume_pop.dart';
import 'widgets/skill_match_pop.dart';

class ExploreJobListPage extends StatefulWidget {
  final int? indexNo;
  final bool fromVideoResume;
  const ExploreJobListPage(
      {Key? key, required this.indexNo, this.fromVideoResume = false})
      : super(key: key);

  @override
  State<ExploreJobListPage> createState() => _ExploreJobListPageState();
}

class _ExploreJobListPageState extends State<ExploreJobListPage> {
  late ExploreJobProvider jobProvider;
  bool? isJobLoading;
  bool? competitionDetailLoading = true;
  bool? domainListLoading = true;
  bool? jobApplyLoading = true;

  //CompetitionResponse? myJobResponse, allJobListResponse;
  CompetitionResponse? myJobResponse;

  ExploreJobListResponse? allJobListResponse;

  List<ExploreJobListResponse> filteredJob = [];

  TrainingModuleResponse? competitionDetail;
  DomainListResponse? domainList;
  DomainFilterListResponse? domainFilterList;
  int? programId;
  int selectedIndex = 0;
  String seletedIds = '';
  List<int> selectedIdList = <int>[];
  int? applied;
  var _scaffoldKey = new GlobalKey<ScaffoldState>();
  bool myJobRecall = false;

  TextEditingController searchController = TextEditingController();
  FocusNode searchFocus = FocusNode();
  bool searching = false;

  Timer? _debounce;

  _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      setState(() {});
    });
  }

  @override
  void initState() {
    super.initState();
    getMyJobList(pageNo: widget.indexNo);
    getPortfolio();
    searchController.addListener(() {
      _onSearchChanged('');
    });
  }

  void getPortfolio() {
    BlocProvider.of<HomeBloc>(context).add(PortfolioEvent(userId: null));
  }

  @override
  void dispose() {
    jobProvider.reset();
    super.dispose();
  }

  PortfolioResponse? portfolioResponse;

  void getMyJobList({required int? pageNo}) {
    BlocProvider.of<HomeBloc>(context)
        .add(ExploreJobListEvent(indexNo: pageNo));
  }

  void handlePortfolioState(PortfolioState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PortfolioState Loading...................");
          jobProvider.resetResume();

          break;
        case ApiStatus.SUCCESS:
          portfolioResponse = portfolioState.response;
          jobProvider.updateResumeDetail(
              data: portfolioState.response?.data.resume.first);

          if ('${portfolioState.response?.data.name}' != '')
            Preference.setString(
                Preference.FIRST_NAME, '${portfolioState.response?.data.name}');
          if (portfolioState.response?.data.image.contains(
                  '${Preference.getString(Preference.PROFILE_IMAGE)}') ==
              true) {
            Preference.setString(Preference.PROFILE_IMAGE,
                '${portfolioState.response?.data.image}');
          }

          Preference.setString(Preference.USER_EMAIL,
              '${portfolioResponse?.data.portfolioSocial.first.email}');
          Preference.setString(Preference.PHONE,
              '${portfolioResponse?.data.portfolioSocial.first.mobNum}');

          if ('${portfolioState.response?.data.image}' != '')
            Preference.setString(Preference.PROFILE_IMAGE,
                '${portfolioState.response?.data.image}');

          Preference.setString(Preference.PROFILE_VIDEO,
              '${portfolioState.response?.data.profileVideo}');

          Preference.setInt(Preference.PROFILE_PERCENT,
              portfolioState.response!.data.profileCompletion);
          Preference.setString(Preference.RESUME_URL,
              '${portfolioState.response?.data.resume.first.url}');
          Preference.setInt(Preference.RESUME_PARSER_DATA_COUNT,
              portfolioState.response!.data.resumeParserDataCount!);

          if (portfolioState.response?.data.portfolioProfile.isNotEmpty ==
              true) {
            Preference.setString(Preference.ABOUT_ME,
                '${portfolioState.response?.data.portfolioProfile.first.aboutMe}');

            Preference.setString(Preference.USER_HEADLINE,
                '${portfolioState.response?.data.portfolioProfile.first.headline}');
            Preference.setString(Preference.LOCATION,
                '${portfolioState.response?.data.portfolioProfile.first.city}, ${portfolioState.response?.data.portfolioProfile.first.country}');
          }
          setState(() {});
          break;

        case ApiStatus.ERROR:
          Log.v("PortfolioState Error..........................");
          Log.v(
              "PortfolioState Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'explore_job_list', parameters: {
            "ERROR": '${portfolioState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handlecompetitionListResponse(ExploreJobListState state) {
    switch (state.apiState) {
      case ApiStatus.LOADING:
        Log.v("Loading....................");
        jobProvider.reset();
        jobProvider.setLoading(isLoading: true);

        break;
      case ApiStatus.SUCCESS:
        allJobListResponse = state.response;
        jobProvider.addJobs(allJobListResponse!.data!.exploreJob!);
        jobProvider.incrpageIndex();

        jobProvider.setLoading(isLoading: false);

        break;
      case ApiStatus.ERROR:
        Log.v(
            "Error CompetitionListIDState .....................${state.response!.error}");
        jobProvider.setLoading(isLoading: false);
        FirebaseAnalytics.instance
            .logEvent(name: 'explore_job_list', parameters: {
          "ERROR": '${state.response!.error}',
        });
        break;
      case ApiStatus.INITIAL:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    jobProvider = Provider.of<ExploreJobProvider>(context);

    return BlocManager(
      initState: (context) {
        jobProvider.reset();
        jobProvider.updateFromVideoResume(
            fromVideoResume: widget.fromVideoResume);
      },
      child: MultiBlocListener(
        listeners: [
          BlocListener<HomeBloc, HomeState>(
              listener: (BuildContext context, state) {
            if (state is ExploreJobListState) {
              _handlecompetitionListResponse(state);
            }
            if (state is PortfolioState) {
              handlePortfolioState(state);
            }
          }),
        ],
        child: Scaffold(
          key: _scaffoldKey,
          appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            title: Text(
              'smart_job_matching',
              style: Styles.bold(),
            ).tr(),
            iconTheme: IconThemeData(
              color: Colors.black, //change your color here
            ),
          ),
          //endDrawer: new AppDrawer(),
          backgroundColor: ColorConstants.JOB_BG_COLOR,
          body: ScreenWithLoader(
              isLoading: jobProvider.loading, body: _makeBody()),
        ),
      ),
    );
  }

  void getCompetitionList(bool isFilter, String? ids) {
    BlocProvider.of<HomeBloc>(context).add(
        CompetitionListEvent(isPopular: false, isFilter: isFilter, ids: ids));
  }

  String calculateTimeDifferenceBetween(DateTime startDate, DateTime endDate) {
    int seconds = endDate.difference(startDate).inSeconds;
    if (seconds < 60) {
      // if (seconds.abs() < 4) return '${tr('now')}';
      return '${seconds.abs()} ${tr('second')}';
    } else if (seconds >= 60 && seconds < 3600)
      return '${startDate.difference(endDate).inMinutes.abs()} ${tr('mins')}';
    else if (seconds >= 3600 && seconds < 86400)
      return '${startDate.difference(endDate).inHours.abs()} ${tr('hour')}';
    else {
      // convert day to month
      int days = startDate.difference(endDate).inDays.abs();
      if (days < 30 && days > 7) {
        return '${(startDate.difference(endDate).inDays ~/ 7).abs()} ${tr('week')}';
      }
      if (days > 30) {
        int month = (startDate.difference(endDate).inDays ~/ 30).abs();
        return '$month ${tr('months')}';
      } else
        return '${startDate.difference(endDate).inDays.abs()} ${tr('day')}';
    }
  }

  Widget _makeBody() {
    return Container(
      margin: EdgeInsets.only(bottom: SizeConstants.JOB_BOTTOM_SCREEN_MGN),
      width: MediaQuery.of(context).size.width,

      child: Column(
        children: [
          SizedBox(
            height: height(context) * 0.01,
          ),
          Container(
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      blurRadius: 16,
                      color: Color.fromRGBO(0, 0, 0, 0.05),
                    ),
                  ],
                  color: ColorConstants.WHITE,
                  borderRadius: BorderRadius.circular(10)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "base_on_profile_com",
                        style: Styles.regular(size: 12),
                      ).tr(),
                      Text(
                        "job_matching_to_profile",
                        style: Styles.bold(size: 12),
                      ).tr(args: [
                        '${allJobListResponse?.data?.totalJobs ?? 0}'
                      ]),
                    ],
                  ),
                  InkWell(
                      onTap: () async {
                        isJobLoading = true;
                        setState(() {});
                        await Future.delayed(Duration(seconds: 2));
                        isJobLoading = false;
                        setState(() {});
                      },
                      child: Padding(
                          // color: Colors.red,
                          padding: const EdgeInsets.only(
                              left: 6, right: 18, top: 10, bottom: 10),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              if (allJobListResponse?.data != null)
                                Text(
                                  // "last updated  ${allJobListResponse?.data?.maxDate} day ago ",
                                  "job_last_time_refresh",
                                  style: Styles.regular(size: 10),
                                ).tr(args: [
                                  allJobListResponse!.data!.maxDate != null
                                      ? '${calculateTimeDifferenceBetween(DateTime.fromMillisecondsSinceEpoch(allJobListResponse!.data!.maxDate! * 1000), DateTime.now())}'
                                      // '${Utility.convertSeconds(int.tryParse('${(currentIndiaTime!.millisecondsSinceEpoch) - allJobListResponse!.data!.maxDate!}') ?? 0)}'
                                      : ''
                                ]),
                              InkWell(
                                onTap: () {
                                  BlocProvider.of<HomeBloc>(context).add(
                                      ExploreJobListEvent(
                                          indexNo: widget.indexNo,
                                          doRefresh: true,
                                          resumeUrl:
                                              jobProvider.resumeDetail?.url));
                                },
                                child: RichText(
                                  text: TextSpan(
                                    children: [
                                      WidgetSpan(
                                          alignment:
                                              ui.PlaceholderAlignment.middle,
                                          child: Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 4),
                                              child: isJobLoading == true
                                                  ? InfiniteRotate(
                                                      child:
                                                          Icon(Icons.autorenew))
                                                  : Icon(Icons.autorenew))),
                                      TextSpan(
                                          text: '${tr('refresh_now')}',
                                          style: Styles.bold(
                                              size: 14,
                                              color: ColorConstants()
                                                  .primaryColorbtnAlways())),
                                    ],
                                  ),
                                ),
                              )
                            ],
                          )))
                ],
              )),
          //toggle btn and resume
          Padding(
            padding: const EdgeInsets.only(top: 0.0, left: 10.0, right: 10.0),
            child: Row(
              children: [
                Icon(Icons.verified, size: 18, color: Color(0xff2ABD65)),
                Text(
                  'verified_jobs',
                  style: Styles.regular(size: 14),
                ).tr(),
                Transform.scale(
                  scale: 0.7,
                  child: CupertinoSwitch(
                    activeTrackColor: Color(0xff3EBDA0),
                    value: jobProvider.isVerified,
                    onChanged: (value) {
                      jobProvider.updateIsVerified(
                          isVerified: !jobProvider.isVerified);
                    },
                  ),
                ),
                Spacer(),
                InkWell(
                  onTap: () {
                    ExploreJobBottomSheet.showBottomSheet(
                        isDismissible: true,
                        widget: ResumePop(),
                        context: context);
                  },
                  child: SvgPicture.asset(
                    'assets/images/resume.svg',
                    colorFilter:
                        ColorFilter.mode(ColorConstants.BLACK, BlendMode.srcIn),
                  ),
                ),
                IconButton(
                    onPressed: () {
                      jobProvider.updateFilter();
                      ExploreJobBottomSheet.showBottomSheet(
                          isDismissible: true,
                          widget: FilterPop(),
                          context: context);
                    },
                    icon: Icon(Icons.filter_list))
              ],
            ),
          ),
          // Padding(
          //   padding: const EdgeInsets.only(top: 0.0, left: 10.0, right: 10.0),
          //   //child: _searchFilter(),
          //   child: _filterTextView(),
          // ),

          // myJobResponse?.data != null ? _myJobSectionCard() : SizedBox(),

          ///Complete Profile
          SizedBox(
            height: 10,
          ),
          Expanded(
            child: allJobListResponse?.data != null
                ? SingleChildScrollView(child: renderJobSecondPositionList())
                : BlankPage(),
          )
        ],
      ),
      //),
    );
  }

//https://stackoverflow.com/questions/50567295/listview-filter-search-in-flutter
  Widget _filterTextView() {
    return Container(
      //height: 60,
      width: double.infinity,
      decoration: BoxDecoration(
          color: ColorConstants.WHITE,
          borderRadius: BorderRadius.all(Radius.circular(8))),

      child: Card(
        child: new ListTile(
          leading: new Icon(Icons.search),
          title: new TextField(
            controller: searchController,
            decoration: new InputDecoration(
                hintText: '${tr('search')}', border: InputBorder.none),
          ),
          trailing: new IconButton(
            icon: new Icon(Icons.cancel),
            onPressed: () {
              searchController.clear();

              searching = false;
              //filtered.value = [];
              if (searchFocus.hasFocus) searchFocus.unfocus();
            },
          ),
        ),
      ),
    );
  }

  //Widget renderJobSecondPositionList(int position) {
  Widget renderJobSecondPositionList() {
    /* return Consumer<CompetitionResponseProvider>(
        builder: (context, competitionProvider, child) => put list View);*/
    List<ExploreJob>? exploreJob = jobProvider.filterJobs;
    //     allJobListResponse!.data!.exploreJob!.where((job) {
    //   if (isVerifiedEnabled) {
    //     if (job.isVerified == 1) {
    //       return true;
    //     } else
    //       return false;
    //   }
    //   return true;
    //   // return searchController.value.text == ''
    //   //     ? true
    //   //     : (job.post?.toLowerCase().replaceAll(' ', '').contains(
    //   //                 searchController.value.text
    //   //                     .toLowerCase()
    //   //                     .replaceAll(' ', '')) ==
    //   //             true
    //   //         ? true
    //   //         : false);
    // }).toList();

    if (exploreJob?.length == 0)
      return SizedBox(
          height: height(context) * 0.85,
          child: Center(
              child: Text('no_jobs_found', style: Styles.regular()).tr()));

    return ListView.builder(
        cacheExtent: 100,
        key: new Key(Utility.getRandomString(4)),
        itemCount: exploreJob?.length,
        shrinkWrap: true,
        primary: false,
        itemBuilder: (BuildContext context, int index) {
          //int index = index + 4;
          return Container(
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            width: double.infinity,
            decoration: BoxDecoration(
                boxShadow: [
                  const BoxShadow(
                    blurRadius: 16,
                    color: Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                ],
                color: ColorConstants.WHITE,
                borderRadius: BorderRadius.circular(10)),
            child: Column(
              children: [
                Container(
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10)),
                    color: const Color(0xffF5F5F5),
                  ),
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    children: [
                      if (exploreJob?[index].ratingDetail.length != 0)
                        Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 4, horizontal: 12),
                            decoration: BoxDecoration(
                                color: exploreJob?[index]
                                            .ratingDetail['color_class'] ==
                                        'weakMatch'
                                    ? ColorConstants.ORANGE
                                    : Color(0xff2ABD65),
                                borderRadius: BorderRadius.circular(8)),
                            child: Text(
                              '${exploreJob?[index].ratingDetail['rating_label']}',
                              style: Styles.regular(
                                  size: 12, color: ColorConstants.WHITE),
                            )),
                      Spacer(),
                      if ((int.tryParse(
                                  '${exploreJob![index].jobPostedDateTime}') ??
                              0) !=
                          0)
                        Text(
                          '${Utility.convertDateFromMillis(exploreJob[index].jobPostedDateTime, 'MMM, dd, yyyy')}',
                          style: Styles.regular(size: 12),
                        )
                      // if (exploreJob[index].jobPostedDateTime != null &&
                      //     !(exploreJob[index].jobPostedDateTime is bool))
                      //   Text(
                      //     '${exploreJob![index].jobPostedDateTime ? Utility.convertDateFromMillis(exploreJob[index].jobPostedDateTime, 'MMM, dd, yyyy') : ''}',
                      //     style: Styles.regular(size: 12),
                      //   )
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                          padding: EdgeInsets.only(
                            right: 10.0,
                          ),
                          child: exploreJob[index].logo != null ||
                                  exploreJob[index].logo == ''
                              ? Image.network(
                                  '${exploreJob[index].logo}',
                                  height: 60,
                                  width: 60,
                                  errorBuilder: (context, url, error) {
                                    return Image.asset(
                                      'assets/images/pb_2.png',
                                      height: 60,
                                      width: 60,
                                    );
                                  },
                                )
                              : Image.asset(
                                  'assets/images/pb_2.png',
                                  height: 60,
                                  width: 60,
                                )),
                      InkWell(
                        onTap: () {
                          if (exploreJob[index].isVerified == 1) {
                            Navigator.push(
                                context,
                                NextPageRoute(JobDetailsPage(
                                  title: exploreJob[index].post,
                                  description: '',
                                  location: exploreJob[index].location,
                                  skillNames: '',
                                  companyName: exploreJob[index].company,
                                  domain: '',
                                  companyThumbnail: exploreJob[index].logo,
                                  experience: exploreJob[index].experience,
                                  id: int.tryParse(
                                      '${exploreJob[index].programId}'),
                                  skillMatching:
                                      exploreJob[index].skills?.length == 0
                                          ? ''
                                          : "${tr('out_of_skill', args: [
                                                  '${exploreJob[index].skills!.where((element) => element.userMatch != null && element.userMatch! > 0).length}',
                                                  '${exploreJob[index].skills?.length}'
                                                ])}",
                                  //both required
                                  jobStatus: '',
                                  jobStatusNumeric: 0,
                                )));
                          } else
                            Navigator.push(
                                context,
                                NextPageRoute(
                                    ExploreJobDetailsPage(
                                      id: exploreJob[index].jobId.toString(),
                                      skillMatching:
                                          exploreJob[index].skills?.length == 0
                                              ? ''
                                              : "${tr('out_of_skill', args: [
                                                      '${exploreJob[index].skills!.where((element) => element.userMatch != null && element.userMatch! > 0).length}',
                                                      '${exploreJob[index].skills?.length}'
                                                    ])}",
                                    ),
                                    isMaintainState: false));
                        },
                        child: Container(
                          padding: EdgeInsets.only(left: 5.0, right: 5.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                width: width(context) * 0.6,
                                child: RichText(
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                          text:
                                              '${exploreJob[index].post ?? ''}',
                                          style: Styles.bold(
                                              size: 14,
                                              color: ColorConstants()
                                                  .primaryColorbtnAlways())),
                                      if (exploreJob[index].isVerified == 1)
                                        WidgetSpan(
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 4),
                                            child: Icon(Icons.verified,
                                                size: 18,
                                                color: Color(0xff2ABD65)),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                              Padding(
                                padding:
                                    const EdgeInsets.only(top: 6.0, right: 5.0),
                                child: SizedBox(
                                  width: width(context) * 0.55,
                                  child: Text(
                                      '${exploreJob[index].company ?? ''}',
                                      overflow: TextOverflow.ellipsis,
                                      style: Styles.regular(
                                          size: 12, color: Color(0xff3E4245))),
                                ),
                              ),
                              exploreJob[index].experience != null
                                  ? Padding(
                                      padding: const EdgeInsets.only(top: 5.0),
                                      child: Row(
                                        children: [
                                          // Image.asset(
                                          //     'assets/images/jobicon.png'),
                                          Icon(Icons.work_outline,
                                              size: 16,
                                              color: ColorConstants.GREY_6),
                                          Padding(
                                            padding: EdgeInsets.only(
                                                left: Utility().isRTL(context)
                                                    ? 0
                                                    : 5.0,
                                                right: Utility().isRTL(context)
                                                    ? 5.0
                                                    : 0.0),
                                            child: Text('${tr('exp')}: ',
                                                style: Styles.regular(
                                                    size: 12,
                                                    color:
                                                        ColorConstants.GREY_6)),
                                          ),
                                          Text(
                                              '${exploreJob[index].experience}',
                                              style: Styles.regular(
                                                  size: 12,
                                                  color:
                                                      ColorConstants.GREY_6)),
                                        ],
                                      ),
                                    )
                                  : SizedBox(),
                              exploreJob[index].location != null
                                  ? Padding(
                                      padding: const EdgeInsets.only(top: 6.0),
                                      child: Row(
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.only(),
                                            child: Icon(
                                              Icons.location_on_outlined,
                                              size: 16,
                                              color: ColorConstants.GREY_3,
                                            ),
                                          ),
                                          Padding(
                                            padding: EdgeInsets.only(
                                                left: Utility().isRTL(context)
                                                    ? 0.0
                                                    : 5.0,
                                                right: 20),
                                            child: SizedBox(
                                              width: width(context) * 0.55,
                                              child: Text(
                                                '${exploreJob[index].location}',
                                                style: Styles.regular(
                                                  size: 12,
                                                  color: ColorConstants.GREY_6,
                                                ),
                                                overflow: TextOverflow.ellipsis,
                                                maxLines: 1,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  : SizedBox(),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                if (exploreJob[index].skills?.length != 0) ...[
                  Divider(),
                  InkWell(
                    onTap: () {
                      ExploreJobBottomSheet.showBottomSheet(
                          isDismissible: true,
                          widget: SkillMatchPop(
                            index: index,
                            onSkillAdd: (int skillId) {
                              Utility.tryCatch(fn: () {
                                jobProvider.updateSkillMatch(
                                    jobID: exploreJob[index].jobId!.toString(),
                                    skillID: skillId);
                                setState(() {});
                              });
                            },
                          ),
                          context: context);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(10),
                      child: RichText(
                          text: TextSpan(children: [
                        TextSpan(
                            text: "${tr('out_of_skill', args: [
                                  '${exploreJob[index].skills!.where((element) => element.userMatch != null && element.userMatch! > 0).length}',
                                  '${exploreJob[index].skills?.length}'
                                ])}",
                            // text: '5 out of 5 skills',
                            style: Styles.textExtraBoldUnderline()),
                        TextSpan(
                            text: ' ', style: Styles.textExtraBoldUnderline()),
                        TextSpan(
                            text: '${tr('from_your_profile_matching_jp')}',
                            style: Styles.regular()),
                      ])),
                    ),
                  )
                ]
              ],
            ),
          );
        });
  }
}

class BlankPage extends StatelessWidget {
  const BlankPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(
                      right: 10.0,
                    ),
                    child: Image.asset('assets/images/blank.png'),
                  ),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 190,
                              decoration: BoxDecoration(
                                color: Colors.white,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 160,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: Color(0xffe6e4e6),
                                highlightColor: Color(0xffeaf0f3),
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: Color(0xffe6e4e6),
                                  highlightColor: Color(0xffeaf0f3),
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Divider(
          height: 1,
          color: ColorConstants.GREY_3,
        ),
        Container(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(
                      right: 10.0,
                    ),
                    child: Image.asset('assets/images/blank.png'),
                  ),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 190,
                              decoration: BoxDecoration(
                                color: Colors.white,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 160,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: Color(0xffe6e4e6),
                                highlightColor: Color(0xffeaf0f3),
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: Color(0xffe6e4e6),
                                  highlightColor: Color(0xffeaf0f3),
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Divider(
          height: 1,
          color: ColorConstants.GREY_3,
        ),
        Container(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(
                      right: 10.0,
                    ),
                    child: Image.asset('assets/images/blank.png'),
                  ),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 190,
                              decoration: BoxDecoration(
                                color: Colors.white,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 160,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: Color(0xffe6e4e6),
                                highlightColor: Color(0xffeaf0f3),
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: Color(0xffe6e4e6),
                                  highlightColor: Color(0xffeaf0f3),
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Divider(
          height: 1,
          color: ColorConstants.GREY_3,
        ),
        Container(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(
                      right: 10.0,
                    ),
                    child: Image.asset('assets/images/blank.png'),
                  ),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 190,
                              decoration: BoxDecoration(
                                color: Colors.white,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 160,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: Color(0xffe6e4e6),
                                highlightColor: Color(0xffeaf0f3),
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: Color(0xffe6e4e6),
                                  highlightColor: Color(0xffeaf0f3),
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Divider(
          height: 1,
          color: ColorConstants.GREY_3,
        ),
        Container(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(
                      right: 10.0,
                    ),
                    child: Image.asset('assets/images/blank.png'),
                  ),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 190,
                              decoration: BoxDecoration(
                                color: Colors.white,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 160,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: Color(0xffe6e4e6),
                                highlightColor: Color(0xffeaf0f3),
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: Color(0xffe6e4e6),
                                  highlightColor: Color(0xffeaf0f3),
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Divider(
          height: 1,
          color: ColorConstants.GREY_3,
        ),
        Container(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.only(
                left: 10.0, top: 15.0, right: 10.0, bottom: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.only(
                      right: 10.0,
                    ),
                    child: Image.asset('assets/images/blank.png'),
                  ),
                ),
                Expanded(
                  flex: 9,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 5.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Shimmer.fromColors(
                          baseColor: Color(0xffe6e4e6),
                          highlightColor: Color(0xffeaf0f3),
                          child: Container(
                              height: 13,
                              margin: EdgeInsets.only(left: 2),
                              width: 190,
                              decoration: BoxDecoration(
                                color: Colors.white,
                              )),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Shimmer.fromColors(
                            baseColor: Color(0xffe6e4e6),
                            highlightColor: Color(0xffeaf0f3),
                            child: Container(
                                height: 13,
                                margin: EdgeInsets.only(left: 2),
                                width: 160,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                )),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: Color(0xffe6e4e6),
                                highlightColor: Color(0xffeaf0f3),
                                child: Container(
                                    height: 13,
                                    margin: EdgeInsets.only(left: 2),
                                    width: 60,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                    )),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Shimmer.fromColors(
                                  baseColor: Color(0xffe6e4e6),
                                  highlightColor: Color(0xffeaf0f3),
                                  child: Container(
                                      height: 13,
                                      margin: EdgeInsets.only(left: 2),
                                      width: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                      )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
