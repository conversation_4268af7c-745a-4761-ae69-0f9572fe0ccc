import 'dart:developer';
import 'dart:io';
import 'dart:isolate';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:dio/dio.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_isolate/flutter_isolate.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart' as GETX;
import 'package:lottie/lottie.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/video_resume/refresh_button.dart';
import 'package:masterg/pages/video_resume/refresh_notifier.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:masterg/utils/video_compress_isolate.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shimmer/shimmer.dart';
//import 'package:video_compress/video_compress.dart';
import 'package:video_player/video_player.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

import '../../blocs/home_bloc.dart';
import '../../data/api/api_constants.dart';
import '../../data/api/api_service.dart';
import '../../data/models/response/auth_response/user_session.dart';
import '../../data/models/response/home_response/list_resume_response.dart';
import '../../main.dart';
import '../../utils/Log.dart';
import '../../utils/Styles.dart';
import '../custom_pages/alert_widgets/alerts_widget.dart';
import '../reels/video_recording/video_recording_camera_page.dart';
import 'instruction_popup.dart';
import 'video_resume_report.dart';

enum UploadStatus { init, started, uploading, uploaded, error }

class UploadProgress extends GETX.GetxController {
  var percent = 0.obs;
  var uploadStatus = UploadStatus.init.obs;
  var compressPer = 0.0.obs;
  var isCompressing = false.obs;

  updateValue(int per) => percent.value = per;
  updateUploadStaus(UploadStatus status) => uploadStatus.value = status;
  updateCompressionPer({required double comPer}) => compressPer.value = comPer;
  updateIsCompressing({required bool isCom}) {
    log("update value is $isCom");
    isCompressing.value = isCom;
  }
}

class VideoResume extends StatefulWidget {
  const VideoResume({Key? key}) : super(key: key);

  @override
  State<VideoResume> createState() => _VideoResumeState();
}

class _VideoResumeState extends State<VideoResume> {
  ListVideoResumeResponse? yourLibrary;
  Video? selectedVideo;
  FlickManager? flickManager;
  CancelToken? cancelToken = CancelToken();
  // VideoPlayerController? currentVideoController;
  bool screenloading = false;
  String dotString = '';
  FilePickerStatus? filePickStatus;
  DateTime? currentBackPressTime;
  // Subscription? _subscription;
  var port = ReceivePort();
  var isolate;
  bool pageClosed = false;

  @override
  void initState() {
    super.initState();
    deleteCache();
    BlocProvider.of<HomeBloc>(context).add(ListVideoResumeEvent(null));
  }

  void cancelRequest() {
    if (cancelToken?.isCancelled == false) {
      cancelToken?.cancel();
      cancelToken = CancelToken();
    }
  }

  void closePort() async {
    port.close();
    //await VideoCompress.cancelCompression();
  }

  @override
  void dispose() {
    flickManager?.dispose();
    setPageClose();

    closePort();
    if (isolate != null) {
      // Terminate the previous isolate
      isolate.kill(priority: Isolate.immediate);
    }
    cancelRequest();
    deleteCache();
    // currentVideoController?.dispose();

    // _subscription?.unsubscribe();

    super.dispose();
  }

  Future<bool?> deleteCache() async {
    String targetDirectory =
        '/storage/emulated/0/Android/data/${APK_DETAILS['package_name']}/files/video_compress';

    bool directoryExists = await Utility.checkDirectoryExists(targetDirectory);

    if (directoryExists) {
      print('Directory exists: $targetDirectory');
      // Proceed with further actions if needed
      try {
        //return await VideoCompress.deleteAllCache();
      } catch (e) {
        log("exception while compression $e");
      }
    } else {
      print('Directory does not exist: $targetDirectory');
      // Handle the case where the directory does not exist
    }

    return null;
  }

  void setPageClose() {
    pageClosed = true;
  }

  void updateDot() async {
    while (true) {
      await Future.delayed(Duration(milliseconds: 300));
      setState(() {
        if (dotString == '...') {
          dotString = '';
        } else
          dotString = dotString + '.';
      });
    }
  }

  Future<String?> getThumnail(String url, bool generateThumnail) async {
    // final directory = await getExternalStorageDirectory();
    if (!generateThumnail) return null;
    int index = url.lastIndexOf('/');
    String fileName =
        url.split('/').last.replaceAll(' ', '_').split('.').first + '.jpg';
    String thumbnailPath = url.replaceRange(index, null, '/$fileName');
    log("get thumanil of $thumbnailPath", name: "your_filename.dart");

    try {
      final fileName = await VideoThumbnail.thumbnailFile(
        video: url,
        // thumbnailPath: directory?.absolute.path,
        thumbnailPath: thumbnailPath,
        imageFormat: ImageFormat.JPEG,
        quality: 100,
        timeMs: 0,
      );
      log("get thumanil of $fileName", name: "your_filename.dart");
      return fileName;
    } catch (e) {
      log("thumnail url is e $e", name: "your_filename.dart");
    }
    return null;
  }

  Future<bool> uploadVideo(
      {required String filePath,
      String? title,
      required String thumnailPath}) async {
    c.uploadStatus(UploadStatus.uploading);
    ApiService api = ApiService();
    Map<String, dynamic> data = Map();
    // data['video_thumbnail'] = await MultipartFile.fromFile(thumnailPath,
    //     filename: '${thumnailPath.split('/').last}');

    data['video_title'] = title ?? '';
    data['video'] = await MultipartFile.fromFile(filePath,
        filename: filePath.split('/').last);
    try {
      data['video_thumbnail'] = await Utility().s3UploadFile(thumnailPath);
      uploadProgressController.add(0);
      final response = await api.dio.post(ApiConstants.UPLOAD_VIDEO_RESUME,
          data: FormData.fromMap(data), onSendProgress: (int sent, int total) {
        try {
          c.updateValue(((sent / total) * 100).toInt());
          uploadProgressController.add(sent / total * 100);
        } catch (e) {
          log('exception 111111 $e');
        }
        // controller?.increment(sent / total);
      },
          cancelToken: cancelToken,
          options: Options(
              method: 'POST',
              headers: {
                "Authorization": "Bearer ${UserSession.userToken}",
                ApiConstants.API_KEY: ApiConstants().APIKeyValue()
              },
              responseType: ResponseType.json));
      //final endTime = DateTime.now().millisecondsSinceEpoch;
      log("upload response ${response.data}", name: "your_filename.dart");

      if (response.statusCode == 200 || response.statusCode == 201) {
        c.uploadStatus(UploadStatus.uploaded);
        c.uploadStatus(UploadStatus.init);
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('resume_uploaded_successfully').tr(),
        ));
        //delete recorded or picked file
        Utility.deleteFile(File(filePath));
        Utility.deleteFile(File(thumnailPath));
        c.updateValue(0);

        return true;
      } else {
        c.uploadStatus(UploadStatus.error);
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('video uploaded error').tr(),
        ));
        c.updateValue(0);

        return false;
      }
    } catch (e, stackTrace) {
      log('exception $stackTrace');
    }
    c.updateValue(0);

    return false;
  }

  Future openBottomSheet({
    Widget? widget,
    double? heightFactor = 1.0,
    bool isDismissible = true,
  }) async {
    await showModalBottomSheet(
      isDismissible: isDismissible,
      enableDrag: isDismissible,
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      backgroundColor: Colors.white,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async {
            return isDismissible == false
                ? false
                : !(filePickStatus == FilePickerStatus.picking ||
                    c.isCompressing.value);
          },
          child: Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: FractionallySizedBox(
                heightFactor: heightFactor, child: widget!),
          ),
        );
      },
    ).then((value) => true);
  }

  Future<String?> pickFile(bool closePop, bool showPreparingPop) async {
    await openBottomSheet(
        heightFactor: 0.8,
        widget: InstructionPopup(
          onContinue: () {
            Navigator.pop(context);
          },
        ));

    dynamic result;
    if (showPreparingPop)
      openBottomSheet(
          isDismissible: false,
          heightFactor: 0.5,
          widget: uploadFile(
              hideHelp: true,
              update: () {
                setState(() {});
              }));

    try {
      if (Platform.isIOS) {
        result = await FilePicker.platform.pickFiles(
            onFileLoading: (FilePickerStatus status) {
              log("pick stattus is $status", name: "your_filename.dart");
              filePickStatus = status;
              setState(() {});
            },
            allowMultiple: false,
            // type: FileType.custom,
            // allowedExtensions: ['mp4', 'mov'],
            type: FileType.video);
      } else {
        result = await FilePicker.platform.pickFiles(
          allowMultiple: false,
          type: FileType.custom,
          allowedExtensions: ['mp4', 'mov'],
          onFileLoading: (FilePickerStatus status) {
            filePickStatus = status;
            setState(() {});
          },
        );
      }
    } catch (e) {
      log("error in picking file : $e", name: "your_filename.dart");
    }

    try {
      if (result != null) {
        if (!['mp4', 'mov']
            .contains(result.paths.first.split('/').last.split('.').last)) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('only_video_allowed_upload').tr(),
          ));
          Navigator.pop(context);
          return null;
        }
        // await Utility().s3UploadFile('${result.paths.first}');
        // return null;
        double fileSize = (await File(result.paths.first).length() / 1000000);
        log("getting file size $fileSize", name: "your_filename.dart");

        if (fileSize > 525) {
          log("getting file size extne $fileSize", name: "your_filename.dart");
          if (closePop) Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              behavior: SnackBarBehavior.floating,
              content: Text('upload_upto_mb').tr()));
        } else {
          log("getting file size return ", name: "your_filename.dart");
          return result.paths.first;
        }
      }
    } catch (e) {
      log("exception while getting file size: $e", name: "your_filename.dart");
      return null;
    }

    return null;
  }

  Future<String?> recordVideo() async {
    String? recordedPath;
    await openBottomSheet(
        heightFactor: 0.8,
        widget: InstructionPopup(
          onContinue: () {
            Navigator.pop(context);
          },
        ));
    // Navigator.pop(context);
    log("record chedk 1", name: "your_filename.dart");
    await showDialog(
        context: context,
        builder: (context) => VideoRecordingCameraPage(
              runOnRecordFinish: true,
              infiniteRecording: false,
              isFrontCamera: true,
              //set default duration to 5 mins
              recordDuration: 5 * 60,
              hideFilePicker: true,
              enableQualityDowngrade: true,
              onRecordFinish: (filePath, isRecorded) {
                recordedPath = filePath;
              },
            ));

    return recordedPath;
  }

  final UploadProgress c = GETX.Get.put(new UploadProgress());

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
        providers: [
          ChangeNotifierProvider<RefreshNotifier>(
            create: (context) => RefreshNotifier(),
          ),
        ],
        child: Consumer<RefreshNotifier>(
            builder: ((context, refreshNotifier, child) =>
                BlocListener<HomeBloc, HomeState>(
                    //handle state
                    listener: (context, state) async {
                      if (state is ListVideoResumeState) {
                        switch (state.apiState) {
                          case ApiStatus.LOADING:
                            if (state.isRefresh == false)
                              setState(() {
                                screenloading = true;
                              });
                            Log.v(
                                "Loading....................ListVideoResumeState");
                            break;
                          case ApiStatus.SUCCESS:
                            Log.v(
                                "can not update re Success....................ListVideoResumeState ${state.response?.data!} and ${state.index}");
                            if (state.isRefresh == true) {
                              refreshNotifier.setRefreshingStop(state.index!);
                              try {
                                if (yourLibrary?.data
                                        ?.indexOf(selectedVideo!) ==
                                    state.index) {
                                  selectedVideo = state.response!.data!.first;
                                }

                                yourLibrary?.data![state.index!] =
                                    state.response!.data!.first;
                              } catch (e) {
                                log("can not update reload $e",
                                    name: "your_filename.dart");
                              }
                              setState(() {});
                              refreshNotifier.updateList(int.tryParse(
                                      '${yourLibrary?.data?.length}') ??
                                  0);
                              break;
                            }
                            if (state.response?.data?.length == 1 &&
                                state.response?.data?.first.type != 'default') {
                              screenloading = false;

                              setState(() {});
                              refreshNotifier.updateList(int.tryParse(
                                      '${yourLibrary?.data?.length}') ??
                                  0);
                              break;
                            }

                            flickManager?.dispose();
                            flickManager = null;
                            setState((() {}));
                            await Future.delayed(Duration(seconds: 1));

                            if (selectedVideo != null) {
                              setState(() {
                                yourLibrary = state.response;

                                selectedVideo = yourLibrary?.data?.firstWhere(
                                    (element) => element.type != 'default',
                                    orElse: null);

                                flickManager = FlickManager(
                                    videoPlayerController:
                                        VideoPlayerController.networkUrl(
                                            Uri.parse(
                                                '${selectedVideo?.url}')));
                              });

                              screenloading = false;
                              refreshNotifier.updateList(int.tryParse(
                                      '${yourLibrary?.data?.length}') ??
                                  0);
                              break;
                            }

                            try {
                              setState(() {
                                yourLibrary = state.response;
                                selectedVideo = yourLibrary?.data?.firstWhere(
                                    (element) => element.type != 'default',
                                    orElse: null);

                                flickManager = FlickManager(
                                    videoPlayerController:
                                        VideoPlayerController.networkUrl(
                                            Uri.parse(
                                                '${selectedVideo?.url}')));
                              });

                              selectedVideo = yourLibrary?.data?.firstWhere(
                                  (element) => element.type == 'primary',
                                  orElse: () => selectedVideo!);

                              flickManager = FlickManager(
                                  videoPlayerController:
                                      VideoPlayerController.networkUrl(
                                          Uri.parse('${selectedVideo?.url}')));
                              setState((() {}));
                            } catch (e, stackTrace) {
                              log('exception:: $stackTrace');
                            }
                            screenloading = false;
                            //remove this line
                            // yourLibrary?.data?.removeWhere(((element) =>  element.url != null) );
                            // selectedVideo = null;

                            //handle set primary when your upload single resume

                            setState(() {});
                            Future.delayed(Duration(seconds: 2)).then((value) {
                              if (state.response?.data?.length == 2 &&
                                  state.response?.data?[1].type != 'primary') {
                                BlocProvider.of<HomeBloc>(context).add(
                                    DeleteVideoResumeEvent(
                                        isSetPrimary: true, videoIndex: 1));
                              }
                            });
                            refreshNotifier.updateList(
                                int.tryParse('${yourLibrary?.data?.length}') ??
                                    0);

                            break;
                          case ApiStatus.ERROR:
                            screenloading = false;
                            setState(() {});
                            Log.v(
                                "Error..........................${state.error}");
                            break;
                          case ApiStatus.INITIAL:
                            break;
                        }
                      }

                      if (state is DeleteVideoResumeState) {
                        switch (state.apiState) {
                          case ApiStatus.LOADING:
                            setState(() {
                              screenloading = true;
                            });
                            Log.v(
                                "Loading....................DeleteVideoResumeState");
                            break;
                          case ApiStatus.SUCCESS:
                            Log.v(
                                "Success....................DeleteVideoResumeState");

                            if (state.isSetPrimary == true) {
                              try {
                                yourLibrary?.data
                                    ?.firstWhere(
                                        (element) => element.type == 'primary')
                                    .type = '';
                              } catch (e) {
                                Log.v(
                                    'Exception no primary resume found video_resume.dart');
                              }
                              selectedVideo?.type = 'primary';
                            } else {
                              flickManager?.dispose();
                              flickManager = null;
                              setState((() {}));
                              await Future.delayed(Duration(seconds: 1));
                              yourLibrary?.data?.remove(selectedVideo);
                              if (yourLibrary?.data?.length == 1) {
                                selectedVideo = null;
                              } else {
                                try {
                                  selectedVideo = yourLibrary?.data?.firstWhere(
                                      (element) => element.type == 'primary');
                                  Log.v("check 1");
                                } catch (e) {
                                  Log.v("check 2");

                                  selectedVideo = yourLibrary?.data?.firstWhere(
                                      (element) => element.type != 'default');
                                } finally {
                                  Log.v("check 3 ${selectedVideo?.type}");

                                  flickManager = FlickManager(
                                      videoPlayerController:
                                          VideoPlayerController.networkUrl(
                                              Uri.parse(
                                                  '${selectedVideo?.url}')));
                                }
                              }
                            }
                            screenloading = false;
                            setState(() {});
                            break;
                          case ApiStatus.ERROR:
                            screenloading = false;
                            setState(() {});
                            Log.v(
                                "Error..........................DeleteVideoResumeState");
                            Log.v(
                                "Error..........................${state.error}");
                            break;
                          case ApiStatus.INITIAL:
                            break;
                        }
                      }
                    },
                    child: Scaffold(
                      backgroundColor: Color(0xffF9FBFC),
                      appBar: AppBar(
                          backgroundColor: ColorConstants.WHITE,
                          elevation: 0.2,
                          iconTheme: IconThemeData(color: ColorConstants.BLACK),
                          title: Text(
                            'video_upload',
                            style: Styles.bold(),
                          ).tr()),
                      body: ScreenWithLoader(
                        isLoading: yourLibrary != null && screenloading,
                        body: yourLibrary == null
                            ? Lottie.network(
                                'https://lottie.host/da5c4f3e-2633-443c-9235-99ee0fbe9ac9/tv8ta9jmd2.json',
                                fit: BoxFit.contain)
                            : Padding(
                                padding: const EdgeInsets.all(0.0),
                                child: ListView(children: [
                                  selectedVideo == null
                                      ? Container(
                                          width: width(context),
                                          padding: const EdgeInsets.all(10),
                                          decoration: BoxDecoration(
                                            color: ColorConstants.WHITE,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            // boxShadow: [
                                            //   BoxShadow(
                                            //     blurRadius: 10,
                                            //     color: ColorConstants()
                                            //         .primaryColorbtnAlways(),
                                            //   ),
                                            // ]
                                          ),
                                          child: Column(children: [
                                            Text(
                                              'upload_resume_video',
                                              style: Styles.bold(size: 18),
                                            ).tr(),
                                            SizedBox(height: 10),
                                            uploadFile(update: () {
                                              setState(() {});
                                            })
                                          ]),
                                        )

                                      //selected video player

                                      : Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(
                                              height: height(context) * 0.3,
                                              child: flickManager != null
                                                  ? FlickVideoPlayer(
                                                      flickVideoWithControls:
                                                          FlickVideoWithControls(
                                                        videoFit:
                                                            BoxFit.contain,
                                                        controls:
                                                            FlickPortraitControls(
                                                          progressBarSettings:
                                                              FlickProgressBarSettings(
                                                                  playedColor:
                                                                      Colors
                                                                          .white),
                                                        ),
                                                      ),
                                                      flickManager:
                                                          flickManager!,
                                                    )
                                                  : Center(
                                                      child:
                                                          CircularProgressIndicator()),
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  color: Colors.white,
                                                  width: MediaQuery.of(context)
                                                      .size
                                                      .width,
                                                  padding: const EdgeInsets.all(
                                                      10.0),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        '${selectedVideo?.videoTitle}',
                                                        style: Styles.bold(),
                                                        maxLines: 2,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                      ),
                                                      Text(
                                                        Utility.getDateFromDate(
                                                            '${selectedVideo?.date}'),
                                                        style: Styles.regular(
                                                            size: 13),
                                                        maxLines: 2,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                      ),
                                                      if (selectedVideo?.type !=
                                                          'default')
                                                        Row(
                                                          children: [
                                                            selectedVideo?.type
                                                                        .toLowerCase() ==
                                                                    'primary'
                                                                ? Container(
                                                                    padding: const EdgeInsets
                                                                        .symmetric(
                                                                        horizontal:
                                                                            4,
                                                                        vertical:
                                                                            4),
                                                                    decoration: BoxDecoration(
                                                                        color: ColorConstants()
                                                                            .primaryColorbtnAlways(),
                                                                        border: Border.all(
                                                                            width:
                                                                                1.2,
                                                                            color: ColorConstants()
                                                                                .primaryColorbtnAlways()),
                                                                        borderRadius:
                                                                            BorderRadius.circular(20)),
                                                                    child: Row(
                                                                      children: [
                                                                        Icon(
                                                                            Icons
                                                                                .check_circle,
                                                                            color:
                                                                                ColorConstants.WHITE),
                                                                        Padding(
                                                                          padding: const EdgeInsets
                                                                              .symmetric(
                                                                              horizontal: 8),
                                                                          child:
                                                                              Text(
                                                                            'primary',
                                                                            style:
                                                                                Styles.regular(color: ColorConstants.WHITE),
                                                                          ).tr(),
                                                                        )
                                                                      ],
                                                                    ),
                                                                  )
                                                                : InkWell(
                                                                    onTap: () {
                                                                      BlocProvider.of<HomeBloc>(context).add(DeleteVideoResumeEvent(
                                                                          isSetPrimary:
                                                                              true,
                                                                          videoIndex:
                                                                              int.tryParse('${yourLibrary?.data?.indexOf(selectedVideo!)}') ?? -1));
                                                                    },
                                                                    child: Container(
                                                                        width: width(context) * 0.35,
                                                                        height: 32,
                                                                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                                                        decoration: BoxDecoration(color: Color(0xffF4F7FD), border: Border.all(width: 1.2, color: ColorConstants().primaryColorbtnAlways()), borderRadius: BorderRadius.circular(20)),
                                                                        child: Center(
                                                                          child:
                                                                              Text('set_primary').tr(),
                                                                        )),
                                                                  ),
                                                            SizedBox(width: 10),
                                                            InkWell(
                                                              onTap: () async {
                                                                log("current index is ${yourLibrary!.data!.indexOf(selectedVideo!)}");
                                                                flickManager
                                                                    ?.flickControlManager
                                                                    ?.pause();
                                                                await openBottomSheet(
                                                                    heightFactor:
                                                                        0.8,
                                                                    widget:
                                                                        VideoResumeReport(
                                                                      videoText:
                                                                          '${selectedVideo?.videoText}',
                                                                      videoIndex: yourLibrary!
                                                                          .data!
                                                                          .indexOf(
                                                                              selectedVideo!),
                                                                      onCaption:
                                                                          (String
                                                                              caption) {
                                                                        selectedVideo?.videoText =
                                                                            caption;
                                                                        setState(
                                                                            () {});
                                                                      },
                                                                    ));
                                                                flickManager
                                                                    ?.flickControlManager
                                                                    ?.play();
                                                              },
                                                              child: Container(
                                                                  height: 32,
                                                                  padding: const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          10,
                                                                      vertical:
                                                                          4),
                                                                  decoration: BoxDecoration(
                                                                      color: Color(
                                                                          0xffF4F7FD),
                                                                      border: Border.all(
                                                                          width:
                                                                              1.2,
                                                                          color: ColorConstants
                                                                              .GREY_3),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              20)),
                                                                  child: Center(
                                                                    child: Text(
                                                                      'view_report',
                                                                      style: Styles.regular(
                                                                          size:
                                                                              14,
                                                                          lineHeight:
                                                                              1.4),
                                                                    ).tr(),
                                                                  )),
                                                            ),
                                                            Spacer(),
                                                            Container(
                                                              margin:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          4),
                                                              decoration: BoxDecoration(
                                                                  color: Color(
                                                                      0xffF4F7FD),
                                                                  shape: BoxShape
                                                                      .circle),
                                                              child: IconButton(
                                                                  onPressed:
                                                                      () {
                                                                    Utility.shortLink(
                                                                            '${selectedVideo?.url}')
                                                                        .then(
                                                                            (value) {
                                                                      Share.share(
                                                                          '$value');
                                                                      FirebaseAnalytics
                                                                          .instance
                                                                          .logEvent(
                                                                              name: 'share_url',
                                                                              parameters: {
                                                                            "type":
                                                                                "video_resume",
                                                                          });
                                                                    });
                                                                  },
                                                                  icon: Icon(Icons
                                                                      .share_outlined)),
                                                            ),
                                                            Container(
                                                              margin:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          4),
                                                              decoration: BoxDecoration(
                                                                  color: Color(
                                                                      0xffF4F7FD),
                                                                  shape: BoxShape
                                                                      .circle),
                                                              child: IconButton(
                                                                  onPressed:
                                                                      () {
                                                                    AlertsWidget.showCustomDialog(
                                                                        context: context,
                                                                        title: tr('delete'),
                                                                        text: tr('confirm_deletion_textone'),
                                                                        icon: 'assets/images/circle_alert_fill.svg',
                                                                        oKText: tr('ok'),
                                                                        onOkClick: () async {
                                                                          BlocProvider.of<HomeBloc>(context).add(DeleteVideoResumeEvent(
                                                                              isSetPrimary: false,
                                                                              videoIndex: int.tryParse('${yourLibrary?.data?.indexOf(selectedVideo!)}') ?? -1));
                                                                        });
                                                                  },
                                                                  icon: Icon(Icons
                                                                      .delete_outline_outlined)),
                                                            ),
                                                          ],
                                                        ),
                                                    ],
                                                  ),
                                                ),
                                                SizedBox(
                                                  height: 10,
                                                ),
                                                Column(
                                                  children: [
                                                    if (selectedVideo != null)
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                vertical: 16,
                                                                horizontal: 10),
                                                        width: width(context),
                                                        //color: Color(0xffF9FBFC),
                                                        color: Colors.white,
                                                        child: Row(
                                                          children: [
                                                            Text(
                                                              'update_resume',
                                                              style:
                                                                  Styles.bold(
                                                                      size: 15),
                                                            ).tr(),
                                                            Spacer(),
                                                            InkWell(
                                                              onTap: () async {
                                                                filePickStatus =
                                                                    null;
                                                                setState(() {});
                                                                flickManager
                                                                    ?.flickControlManager
                                                                    ?.pause();
                                                                await openBottomSheet(
                                                                    isDismissible:
                                                                        true,
                                                                    heightFactor:
                                                                        0.5,
                                                                    widget:
                                                                        Padding(
                                                                      padding: const EdgeInsets
                                                                          .all(
                                                                          12.0),
                                                                      child: uploadFile(
                                                                          hideHelp: true,
                                                                          closePop: true,
                                                                          update: () {
                                                                            setState(() {});
                                                                          }),
                                                                    ));
                                                                // currentVideoController
                                                                //     ?.play();
                                                              },
                                                              child: Row(
                                                                children: [
                                                                  Icon(
                                                                    Icons
                                                                        .cloud_upload_outlined,
                                                                    color: ColorConstants()
                                                                        .primaryColorbtnAlways(),
                                                                  ),
                                                                  SizedBox(
                                                                      width:
                                                                          10),
                                                                  Text(
                                                                    'upload_resume',
                                                                    style: Styles.bold(
                                                                        size:
                                                                            15,
                                                                        color: ColorConstants()
                                                                            .primaryColorbtnAlways()),
                                                                  ).tr()
                                                                ],
                                                              ),
                                                            )
                                                          ],
                                                        ),
                                                      )
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                  //your library
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 10, horizontal: 8),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'your_library',
                                          style: Styles.bold(size: 18),
                                        ).tr(),
                                        SizedBox(height: 10),
                                        if (yourLibrary != null &&
                                            yourLibrary?.data?.length != 0)
                                          ListView.builder(
                                              physics:
                                                  NeverScrollableScrollPhysics(),
                                              shrinkWrap: true,
                                              itemCount:
                                                  yourLibrary?.data?.length,
                                              itemBuilder: (context, index) {
                                                return InkWell(
                                                  onTap: () async {
                                                    print(
                                                        'selectedVideo:--${selectedVideo?.url}');
                                                    print(
                                                        'selectedVideo:--${selectedVideo?.thumbnail}');

                                                    if (screenloading == true)
                                                      return;
                                                    await flickManager
                                                        ?.flickControlManager
                                                        ?.pause();
                                                    flickManager?.dispose();
                                                    flickManager = null;
                                                    setState((() {
                                                      selectedVideo =
                                                          yourLibrary
                                                              ?.data?[index];
                                                    }));
                                                    await Future.delayed(
                                                        Duration(
                                                            milliseconds: 500));

                                                    flickManager = FlickManager(
                                                        videoPlayerController:
                                                            VideoPlayerController
                                                                .networkUrl(
                                                                    Uri.parse(
                                                                        '${selectedVideo?.url}')));
                                                    setState((() {}));
                                                  },
                                                  child: Column(
                                                    children: [
                                                      Stack(
                                                        children: [
                                                          Container(
                                                            margin:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    vertical:
                                                                        4),
                                                            padding:
                                                                EdgeInsets.all(
                                                                    4),
                                                            decoration: BoxDecoration(
                                                                color: yourLibrary?.data?[index] ==
                                                                        selectedVideo
                                                                    ? ColorConstants
                                                                        .GREY_3
                                                                        .withValues(
                                                                            alpha:
                                                                                0.2)
                                                                    : null,
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            8),
                                                                border: selectedVideo ==
                                                                        yourLibrary?.data?[
                                                                            index]
                                                                    ? Border.all(
                                                                        color:
                                                                            Color(0xffCED4E7))
                                                                    : null),
                                                            child: Row(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                SizedBox(
                                                                  width: width(
                                                                          context) *
                                                                      0.2,
                                                                  height: width(
                                                                          context) *
                                                                      0.2,
                                                                  child: yourLibrary
                                                                              ?.data?[index]
                                                                              .thumbnail !=
                                                                          ''
                                                                      ? ClipRRect(
                                                                          borderRadius:
                                                                              BorderRadius.circular(14),
                                                                          child:
                                                                              CachedNetworkImage(
                                                                            fit:
                                                                                BoxFit.cover,
                                                                            imageUrl:
                                                                                "${yourLibrary?.data?[index].thumbnail}",
                                                                            progressIndicatorBuilder: (context, url, downloadProgress) =>
                                                                                Shimmer.fromColors(
                                                                              baseColor: Color(0xffe6e4e6),
                                                                              highlightColor: Color(0xffeaf0f3),
                                                                              child: Container(
                                                                                // height: double
                                                                                //     .infinity,
                                                                                margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                                                                                width: MediaQuery.of(context).size.width,
                                                                                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(6)),
                                                                              ),
                                                                            ),
                                                                            errorWidget: (context, url, error) =>
                                                                                Center(
                                                                              child: SvgPicture.asset(
                                                                                'assets/images/image.svg',
                                                                                width: width(context) * 0.2,
                                                                                height: width(context) * 0.2,
                                                                                colorFilter: ColorFilter.mode(ColorConstants.GREY_4, BlendMode.srcIn),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        )
                                                                      : Center(
                                                                          child:
                                                                              SvgPicture.asset(
                                                                            'assets/images/image.svg',
                                                                            width:
                                                                                width(context) * 0.2,
                                                                            height:
                                                                                width(context) * 0.2,
                                                                            colorFilter:
                                                                                ColorFilter.mode(ColorConstants.GREY_4, BlendMode.srcIn),
                                                                          ),
                                                                        ),
                                                                  // child: FutureBuilder<
                                                                  //         String?>(
                                                                  //     future: getThumnail(
                                                                  //         '${yourLibrary?.data?[index].url}',
                                                                  //         false),
                                                                  //     builder: (context,
                                                                  //             snapshot) =>
                                                                  //         snapshot.hasData
                                                                  //             ? ClipRRect(
                                                                  //                 borderRadius:
                                                                  //                     BorderRadius.circular(
                                                                  //                         10),
                                                                  //                 child: Image
                                                                  //                     .file(
                                                                  //                   File(
                                                                  //                     '${snapshot.data}',
                                                                  //                   ),
                                                                  //                   fit: BoxFit
                                                                  //                       .cover,
                                                                  //                 ))
                                                                  //             : SizedBox(
                                                                  //                 width: 30,
                                                                  //                 height:
                                                                  //                     30,
                                                                  //                 child:
                                                                  //                     Center(
                                                                  //                   /*child: Text(
                                                                  //                   "Loading.....${snapshot.connectionState}")*/
                                                                  //                   child: Image.asset(
                                                                  //                       'assets/images/blank.png'),
                                                                  //                 ),
                                                                  //               ))
                                                                ),
                                                                SizedBox(
                                                                    width: 10),
                                                                Container(
                                                                  width: width(
                                                                          context) *
                                                                      0.7,
                                                                  //height: width(context) * 0.2,
                                                                  height: 71,

                                                                  child: Column(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .start,
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      Text(
                                                                        '${yourLibrary?.data?[index].videoTitle}',
                                                                        style: Styles
                                                                            .bold(),
                                                                        maxLines:
                                                                            1,
                                                                        overflow:
                                                                            TextOverflow.ellipsis,
                                                                      ),
                                                                      SizedBox(
                                                                          height:
                                                                              2),
                                                                      Text(
                                                                        Utility.getDateFromDate(
                                                                            '${yourLibrary?.data?[index].date}'),
                                                                        //Text('${yourLibrary?.data?[index].date}',
                                                                        style: Styles.regular(
                                                                            size:
                                                                                13),
                                                                        maxLines:
                                                                            2,
                                                                        overflow:
                                                                            TextOverflow.ellipsis,
                                                                      ),
                                                                    ],
                                                                  ),
                                                                )
                                                              ],
                                                            ),
                                                          ),
                                                          if (yourLibrary
                                                                  ?.data?[index]
                                                                  .type
                                                                  .toLowerCase() ==
                                                              'primary')
                                                            Positioned(
                                                              right: Utility()
                                                                      .isRTL(
                                                                          context)
                                                                  ? null
                                                                  : 4,
                                                              left: Utility()
                                                                      .isRTL(
                                                                          context)
                                                                  ? 4
                                                                  : null,
                                                              top: 0,
                                                              bottom: 0,
                                                              child: Icon(
                                                                  Icons
                                                                      .check_circle,
                                                                  size: 32,
                                                                  color:
                                                                      ColorConstants
                                                                          .GREEN),
                                                            ),
                                                          if (yourLibrary
                                                                      ?.data?[
                                                                          index]
                                                                      .type
                                                                      .toLowerCase() !=
                                                                  'default' &&
                                                              yourLibrary
                                                                      ?.data?[
                                                                          index]
                                                                      .videoText ==
                                                                  '')
                                                            Positioned(
                                                              left: Utility()
                                                                      .isRTL(
                                                                          context)
                                                                  ? null
                                                                  : width(context) *
                                                                      0.24,
                                                              right: Utility()
                                                                      .isRTL(
                                                                          context)
                                                                  ? width(context) *
                                                                      0.24
                                                                  : null,
                                                              bottom: 0,
                                                              child: Row(
                                                                children: [
                                                                  Text(
                                                                    'analysing',
                                                                    style: Styles
                                                                        .regular(
                                                                            size:
                                                                                12),
                                                                    maxLines: 1,
                                                                    overflow:
                                                                        TextOverflow
                                                                            .ellipsis,
                                                                  ).tr(),
                                                                  // SizedBox(
                                                                  //   width: 20,
                                                                  //   child: Text(
                                                                  //     '$dotString',
                                                                  //     style: Styles
                                                                  //         .regular(),
                                                                  //   ),
                                                                  // ),
                                                                  RefreshButton(
                                                                      index:
                                                                          index,
                                                                      onRefresh:
                                                                          () {
                                                                        refreshNotifier
                                                                            .setRefreshing(index);
                                                                        BlocProvider.of<HomeBloc>(context).add(ListVideoResumeEvent(
                                                                            index,
                                                                            isRefresh:
                                                                                true));
                                                                      })
                                                                ],
                                                              ),
                                                            )
                                                        ],
                                                      ),
                                                      Divider()
                                                    ],
                                                  ),
                                                );
                                              })
                                      ],
                                    ),
                                  ),
                                ]),
                              ),
                      ),
                    )))));
  }

  Widget uploadPop({required String selectedFilePath, bool closePop = false}) {
    File? thumnailFile;
    c.uploadStatus(UploadStatus.init);
    c.updateValue(0);
    TextEditingController titleController = TextEditingController();
    GlobalKey<FormState> _formKey = GlobalKey<FormState>();

    return Form(
      key: _formKey,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Text(
                        'selected_video',
                        style: Styles.bold(),
                      ).tr()),
                  Spacer(),
                  IconButton(
                      onPressed: () {
                        AlertsWidget.showCustomDialog(
                            context: context,
                            // title: tr('Close'),
                            text: tr('close_msg'),
                            icon: 'assets/images/circle_alert_fill.svg',
                            oKText: tr('ok'),
                            onOkClick: () async {
                              // await VideoCompress.cancelCompression();
                              closePort();
                              try {
                                cancelRequest();
                                log("dio request canceled",
                                    name: "your_filename.dart");
                              } catch (e) {
                                log("exception: cancel dio request:: $e",
                                    name: "your_filename.dart");
                              }
                              Navigator.pop(context);
                            },
                            onCancelClick: () {});

                        // Navigator.pop(context);
                      },
                      icon: Icon(Icons.close))
                ],
              ),
              Padding(
                  padding: const EdgeInsets.symmetric(vertical: 6),
                  child: Text(
                    'selected_video_subheading',
                    style: Styles.regular(size: 14, lineHeight: 1.3),
                  ).tr()),
              Container(
                padding: const EdgeInsets.all(6),
                margin: const EdgeInsets.symmetric(vertical: 4),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Color(0xffA1A9C0))),
                child: Row(
                  children: [
                    Stack(
                      children: [
                        SizedBox(
                            width: width(context) * 0.2,
                            height: width(context) * 0.2,
                            child: FutureBuilder<String?>(
                                future: getThumnail('$selectedFilePath', true),
                                builder: (context, snapshot) {
                                  // return Text('${snapshot.data}');
                                  if (snapshot.hasData) {
                                    thumnailFile = File(
                                      '${snapshot.data}',
                                    );
                                    return ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: Image.file(
                                          thumnailFile!,
                                          fit: BoxFit.cover,
                                        ));
                                  }
                                  return SizedBox();
                                })),
                        // Positioned(
                        //     left: 0,
                        //     right: 0,
                        //     top: 0,
                        //     bottom: 0,
                        //     child: Icon(
                        //       Icons.play_circle_filled,
                        //       color: ColorConstants.WHITE,
                        //     ))
                      ],
                    ),
                    SizedBox(width: 6),
                    SizedBox(
                      width: width(context) * 0.65,
                      child: Text(
                        '${selectedFilePath.split('/').last}',
                        style: Styles.semibold(size: 16),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    )
                  ],
                ),
              ),
              StreamBuilder<double>(
                stream: uploadProgressController.stream,
                builder: (context, snapshot) {
                  double progress = snapshot.data ?? 0.0;
                  return Stack(
                    children: [
                      Container(
                        width: width(context),
                        height: 8,
                        decoration: BoxDecoration(
                            color: Color(0xffD9D9D9),
                            borderRadius: BorderRadius.circular(10)),
                      ),
                      Container(
                        width: width(context) * (progress / 100),
                        height: 8,
                        decoration: BoxDecoration(
                            color: ColorConstants().primaryColorbtnAlways(),
                            borderRadius: BorderRadius.circular(10)),
                      )
                    ],
                  );
                  // return Text(
                  //     'Upload Progress: ${(progress).toStringAsFixed(2)}%');
                },
              ),
              SizedBox(height: 16),
              Row(
                children: [
                  Text(
                    'resume_headline',
                    style: Styles.semibold(size: 12),
                  ).tr(),
                  Text(
                    '*',
                    style: Styles.bold(size: 12, color: Colors.red),
                  ).tr(),
                ],
              ),
              SizedBox(height: 10),
              TextFormField(
                controller: titleController,
                maxLength: 60,
                style: Styles.regular(),
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                      borderSide:
                          const BorderSide(width: 1, color: Color(0xffE5E5E5)),
                      borderRadius: BorderRadius.circular(10)),
                  hintText: tr('enter_title_name'),
                  helperStyle: Styles.regular(color: ColorConstants.GREY_4),
                  // counterText: "",
                  suffixIconConstraints: BoxConstraints(minWidth: 0),
                ),
                onChanged: (value) {},
                validator: (value) {
                  if (value == null || value == '')
                    return tr('please_enter_title');
                  return null;
                },
              ),
              GETX.Obx(() => Center(
                    child: InkWell(
                        onTap: () async {
                          if (c.uploadStatus.value == UploadStatus.uploading)
                            return;
                          if (!_formKey.currentState!.validate()) return;

                          bool isuploaded = await uploadVideo(
                              thumnailPath: '${thumnailFile?.path}',
                              filePath: selectedFilePath,
                              title: titleController.value.text);

                          if (isuploaded) {
                            Navigator.pop(context);
                            await openBottomSheet(
                                heightFactor: 0.8,
                                widget: VideoResumeReport(
                                    videoText: '',
                                    videoIndex: 1,
                                    onCaption: (String caption) {}));
                            BlocProvider.of<HomeBloc>(context)
                                .add(ListVideoResumeEvent(null));
                          }

                          log("close pop up value $closePop",
                              name: "your_filename.dart");
                          if (closePop == true) Navigator.pop(context);
                        },
                        child: Container(
                            margin: const EdgeInsets.only(top: 20),
                            padding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 48),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                color:
                                    ColorConstants().primaryColorbtnAlways()),
                            child: Text(
                                c.uploadStatus.value == UploadStatus.uploading
                                    ? "uploading"
                                    : "submit",
                                style: Styles.regular(
                                  color: ColorConstants.WHITE,
                                )).tr())),
                  ))
            ],
          ),
        ),
      ),
    );
  }

  Future<bool> onWillPop() {
    DateTime now = DateTime.now();
    if (currentBackPressTime == null ||
        now.difference(currentBackPressTime!) > Duration(seconds: 2)) {
      currentBackPressTime = now;
      Utility.showSnackBar(
          scaffoldContext: context, message: tr('close_app_confirm'));
      return Future.value(false);
    }

    return Future.value(true);
  }

  Widget uploadFile(
      {bool? hideHelp = false,
      bool closePop = false,
      required Function? update}) {
    return StatefulBuilder(builder:
        (BuildContext context, StateSetter setState /*You can rename this!*/) {
      return Stack(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: DottedBorder(
              options: RectDottedBorderOptions(
                dashPattern: [10, 6],
                color: hideHelp == true ? Colors.white : Color(0xffA1A9C0),
                // borderType: BorderType.RRect,
                // radius: Radius.circular(12),
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 40),
              ),
              // dashPattern: [10, 6],
              // color: hideHelp == true ? Colors.white : Color(0xffA1A9C0),
              // borderType: BorderType.RRect,
              // radius: Radius.circular(12),
              // padding: EdgeInsets.symmetric(horizontal: 10, vertical: 40),
              child: ClipRRect(
                borderRadius: BorderRadius.all(Radius.circular(12)),
                child: Container(
                  width: width(context),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      if (filePickStatus == FilePickerStatus.picking ||
                          c.isCompressing.value) ...[
                        SizedBox(
                          child: GETX.Obx(
                            () => !c.isCompressing.value
                                ? Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        'validate_file',
                                        style: Styles.semibold(),
                                      ).tr(),
                                      SizedBox(width: 6),
                                      SizedBox(
                                          width: 15,
                                          height: 15,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                          )),
                                    ],
                                  )
                                : Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        'preparing_your_file',
                                        style: Styles.semibold(),
                                      ).tr(),
                                      SizedBox(width: 6),
                                      Text(
                                        '${c.compressPer.value.toString().split('.').first}%',
                                        style: Styles.semibold(),
                                      ),
                                    ],
                                  ),
                          ),
                        ),
                      ] else ...[
                        InkWell(
                          onTap: () async {
                            filePickStatus = FilePickerStatus.picking;
                            setState(() {});
                            //  await openBottomSheet(
                            //       heightFactor: 0.9,
                            //       widget: uploadPop(selectedFilePath: '/data/user/0/com.singulariswow.mec/cache/REC3897004414300899123.mp4'));
                            await recordVideo().then((pickedFile) async {
                              filePickStatus = FilePickerStatus.done;
                              if (closePop == true) Navigator.pop(context);
                              setState(() {});
                              try {
                                await openBottomSheet(
                                        heightFactor: 0.5,
                                        isDismissible: false,
                                        widget: uploadPop(
                                            selectedFilePath: pickedFile!,
                                            closePop: false))
                                    .then((value) {
                                  // cancelRequest();
                                });
                              } catch (e) {
                                log('video file is e $e');
                              }
                            });

                            filePickStatus = FilePickerStatus.done;
                            setState(() {});
                          },
                          child: Container(
                            height: 40,
                            width: 220,
                            margin: const EdgeInsets.only(left: 10, right: 10),
                            padding: const EdgeInsets.symmetric(
                                vertical: 4, horizontal: 10),
                            decoration: BoxDecoration(
                                border: Border.all(
                                    color: ColorConstants()
                                        .primaryColorbtnAlways()),
                                borderRadius: BorderRadius.circular(6)),
                            child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                    'assets/images/photo_camera.svg',
                                    colorFilter: ColorFilter.mode(
                                        ColorConstants()
                                            .primaryColorbtnAlways(),
                                        BlendMode.srcIn),
                                    height: 22,
                                    width: 22,
                                  ),
                                  SizedBox(width: 10),
                                  Text(
                                    'record_video',
                                    style: Styles.regular(
                                        size: 14,
                                        color: ColorConstants()
                                            .primaryColorbtnAlways()),
                                  ).tr()
                                ]),
                          ),
                        ),
                        SizedBox(height: 10),
                        InkWell(
                          onTap: () async {
                            pageClosed = false;
                            c.updateCompressionPer(comPer: 0);

                            filePickStatus = FilePickerStatus.picking;
                            setState(() {});
                            if (update != null) {
                              update();
                            }
                            dynamic pickedFile =
                                await pickFile(closePop, hideHelp!);

                            //  .then(() async {
                            if (pickedFile == null) {
                              filePickStatus = FilePickerStatus.done;
                              if (hideHelp) Navigator.pop(context);
                              setState(() {});
                              if (update != null) {
                                update();
                              }
                              return;
                            }
                            log("page close status is $pageClosed");
                            if (!pageClosed) {
                              try {
                                port = ReceivePort();
                                c.updateIsCompressing(isCom: true);
                                if (update != null) {
                                  update();
                                }
                                isolate = await FlutterIsolate.spawn(
                                    FileCompressionIsolate.compressIsolate, {
                                  'filePath': pickedFile,
                                  'sendPort': port.sendPort
                                });

                                port.listen((dynamic message) async {
                                  if (message is Map<String, dynamic>) {
                                    log("message is $message");
                                    if (message.containsKey('progress')) {
                                      log("page close status is $pageClosed");
                                      if (pageClosed) {
                                        closePort();
                                        if (isolate != null) {
                                          // Terminate the previous isolate
                                          isolate.kill(
                                              priority: Isolate.immediate);
                                        }
                                      }
                                      double progress = message['progress'];
                                      // Handle progress updates if needed
                                      c.updateCompressionPer(comPer: progress);

                                      log('Compression progress: $progress');
                                    } else if (message.containsKey('path')) {
                                      pickedFile = message['path'];
                                      closePort();
                                    }
                                  }
                                }, onDone: () async {
                                  // deleteCacheCompression();
                                  closePort();

                                  // _subscription?.unsubscribe();
                                  c.updateIsCompressing(isCom: false);
                                  filePickStatus = FilePickerStatus.done;
                                  setState(() {});
                                  if (update != null) {
                                    update();
                                  }
                                  if (hideHelp == true) Navigator.pop(context);

                                  //show progress upload

                                  await openBottomSheet(
                                          heightFactor: 0.5,
                                          isDismissible: false,
                                          widget: uploadPop(
                                              selectedFilePath: '$pickedFile',
                                              closePop: closePop))
                                      .then((value) {
                                    // cancelRequest();
                                  });
                                });
                              } catch (e, stackTrace) {
                                closePort();

                                log("Error during file compression: $stackTrace");
                                filePickStatus = FilePickerStatus.done;
                                setState(() {});
                                if (update != null) {
                                  update();
                                }
                                Navigator.pop(context);
                                return;
                              }
                            }
                            // uploadVideo(filePath: pickedFile);
                            // });
                          },
                          child: Container(
                            height: 40,
                            width: 220,
                            margin: const EdgeInsets.only(left: 10, right: 10),
                            padding: const EdgeInsets.symmetric(
                                vertical: 4, horizontal: 10),
                            decoration: BoxDecoration(
                                color: ColorConstants().primaryColorbtnAlways(),
                                borderRadius: BorderRadius.circular(6)),
                            child: Center(
                              child: Text(
                                'choose_file',
                                style: Styles.regular(
                                    size: 14, color: ColorConstants.WHITE),
                              ).tr(),
                            ),
                          ),
                        ),
                      ],
                      if (hideHelp == false)
                        InkWell(
                          onTap: () async {
                            flickManager?.dispose();
                            flickManager = null;
                            setState((() {}));
                            await Future.delayed(Duration(seconds: 1));
                            selectedVideo = yourLibrary?.data?.first;

                            flickManager = FlickManager(
                                videoPlayerController:
                                    VideoPlayerController.networkUrl(
                                        Uri.parse('${selectedVideo?.url}')));

                            setState(() {});
                            if (update != null) {
                              update();
                            }
                          },
                          child: Column(
                            children: [
                              SizedBox(height: 20),
                              SizedBox(
                                  width: width(context) * 0.4,
                                  child: Divider(
                                    thickness: 1,
                                  )),
                              SizedBox(height: 2),
                              Text('get_help',
                                      style: Styles.regular(
                                          color: ColorConstants.GREY_3))
                                  .tr(),
                              SizedBox(height: 10),
                              SvgPicture.asset('assets/images/play_resume.svg'),
                              SizedBox(height: 10),
                              SizedBox(
                                  width: width(context) * 0.4,
                                  child: Text(
                                    'watch_introduction',
                                    style: Styles.regular(
                                        color: ColorConstants()
                                            .primaryColorbtnAlways()),
                                    textAlign: TextAlign.center,
                                  ).tr()),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          if (hideHelp == true &&
              (filePickStatus == FilePickerStatus.picking ||
                  c.isCompressing.value))
            Row(
              children: [
                Spacer(),
                IconButton(
                    onPressed: () {
                      AlertsWidget.showCustomDialog(
                          context: context,
                          // title: tr('Close'),
                          text: tr('close_msg'),
                          icon: 'assets/images/circle_alert_fill.svg',
                          oKText: tr('ok'),
                          onOkClick: () async {
                            // await VideoCompress.cancelCompression();
                            closePort();
                            try {
                              cancelRequest();
                              log("dio request canceled",
                                  name: "your_filename.dart");
                            } catch (e) {
                              log("exception: cancel dio request:: $e",
                                  name: "your_filename.dart");
                            }
                            Navigator.pop(context);
                          },
                          onCancelClick: () {});

                      // Navigator.pop(context);
                    },
                    icon: Icon(Icons.close))
              ],
            ),
        ],
      );
    });
  }
}
