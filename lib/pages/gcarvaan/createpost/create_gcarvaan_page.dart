import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:dio/dio.dart' as Dio;
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:injector/injector.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/create_post_response.dart';
import 'package:masterg/data/models/response/home_response/gcarvaan_post_reponse.dart';
import 'package:masterg/data/providers/reel_controller.dart';
import 'package:masterg/data/repositories/home_repository.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/gcarvaan/createpost/create_post_provider.dart';
import 'package:masterg/pages/gcarvaan/createpost/pdf_view.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:video_thumbnail/video_thumbnail.dart' as Thumbnail;

import '../../../main.dart';
import '../../../utils/click_picker.dart';
import '../../../utils/config.dart';

List<String?>? croppedList;
String? thumnailUrl;

class CreateGCarvaanPage extends StatefulWidget {
  final List<Dio.MultipartFile>? fileToUpload;
  List<String?>? filesPath;
  final bool isReelsPost;
  final CreatePostProvider? provider;

  CreateGCarvaanPage(
      {Key? key,
      this.fileToUpload,
      this.filesPath,
      this.isReelsPost = false,
      this.provider})
      : super(key: key);

  @override
  _CreateGCarvaanPageState createState() => _CreateGCarvaanPageState();
}

class _CreateGCarvaanPageState extends State<CreateGCarvaanPage> {
  bool isPostedLoading = false;
  CreatePostResponse? responseData;
  TextEditingController postDescriptionController = TextEditingController();
  List<GCarvaanPostElement>? gcarvaanPosts;

  bool isRTL = false;
  double? screenWidth;
  dynamic hashTags;

  void getHashTags() async {
    try {
      ApiService api = ApiService();
      dynamic response =
          await api.dio.get('${APK_DETAILS["domain_url"]}event_tags.json');
      setState(() {
        hashTags = jsonDecode('$response');
      });
    } catch (e) {
      Log.v(
        "unable to fetch hashtags $e",
      );
    }
  }

  @override
  void initState() {
    super.initState();
    getHashTags();
  }

  @override
  void dispose() {
    //flickManager.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    isRTL = Utility().isRTL(context);
    screenWidth = width(context);

    return MultiProvider(
        providers: [
          ChangeNotifierProvider<CreatePostProvider>(
            create: (context) => CreatePostProvider(widget.filesPath, false),
          ),
          ChangeNotifierProvider<MenuListProvider>(
            create: (context) => MenuListProvider([]),
          ),
          ChangeNotifierProvider<GCarvaanListModel>(
            create: (context) => GCarvaanListModel(gcarvaanPosts),
          ),
        ],
        child: WillPopScope(
            // ignore: missing_return
            onWillPop: () async {
              widget.provider?.clearList();
              return true;
            },
            child: GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              child: Scaffold(
                backgroundColor: Colors.white,
                appBar: AppBar(
                  backgroundColor: Colors.white,
                  elevation: 0,
                  automaticallyImplyLeading: false,
                  leading: IconButton(
                      onPressed: () {
                        widget.provider?.clearList();
                        Navigator.pop(context);
                        //Navigator.pop(context);
                      },
                      icon: Icon(Icons.close, color: ColorConstants.BLACK)),
                  title: Column(children: [
                    SizedBox(
                      height: 10,
                    ),
                    Text(
                      '${tr('create_post')} ',
                      style: Styles.bold(size: 14, color: ColorConstants.BLACK),
                    )
                  ]),
                  centerTitle: true,
                ),
                body: Consumer2<CreatePostProvider, GCarvaanListModel>(
                  builder: (context, value, gcarvaanListModel, child) =>
                      BlocManager(
                          initState: (BuildContext context) {
                            //createPost();
                          },
                          child: BlocListener<HomeBloc, HomeState>(
                            listener: (context, state) {
                              if (state is CreatePostState)
                                _handleCreatePostResponse(state, value);
                            },
                            child: ScreenWithLoader(
                                isLoading: isPostedLoading,
                                body: _content(value)),
                          )),
                ),
              ),
            )));
  }

  Widget _content(CreatePostProvider value) {
    Size size = MediaQuery.of(context).size;

    return SafeArea(
      child: Container(
        margin: EdgeInsets.only(top: 4),
        height: size.height,
        child: Stack(children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: SingleChildScrollView(
              child: Form(
                child: Column(
                  children: [
                    if (APK_DETAILS["package_name"] == "com.singulariswow.mec")
                      if (hashTags != null && hashTags.length != 0)
                        SizedBox(
                          height: 60,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: hashTags['tags'].length,
                            itemBuilder: (context, index) => InkWell(
                              onTap: () {
                                print('hashTags==${hashTags.length}');
                                postDescriptionController.text =
                                    postDescriptionController.text +
                                        ' ${hashTags['tags'][index]} ';
                                postDescriptionController.selection =
                                    TextSelection.collapsed(
                                        offset: postDescriptionController
                                            .text.length);
                              },
                              child: Container(
                                  margin:
                                      const EdgeInsets.symmetric(horizontal: 5),
                                  padding: const EdgeInsets.only(
                                      top: 8, bottom: 2, left: 2, right: 2),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(4)),
                                  child: Text('${hashTags['tags'][index]}',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: ColorConstants.PRIMARY_BLUE,
                                        decoration: TextDecoration.underline,
                                      ))),
                            ),
                          ),
                        ),
                    Container(
                      margin: EdgeInsets.only(top: size.height * 0.02),
                      padding:
                          EdgeInsets.symmetric(horizontal: size.width * 0.05),
                      height: size.height * 0.2,
                      width: size.width * 0.9,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: ColorConstants.GREY,
                      ),
                      child: TextFormField(
                        maxLength: 1000,
                        keyboardType: TextInputType.multiline,
                        maxLines: null,
                        controller: postDescriptionController,
                        onChanged: (value) {
                          setState(() {});
                        },
                        validator: (value) {
                          if (value!.isEmpty) {
                            return tr('write_your_post');
                          }
                          return null;
                        },
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            counterText: '',
                            hintText: '${tr('write_your_post')}....',
                            hintStyle: Styles.regular(
                                size: 14, color: ColorConstants.GREY_3),
                            helperMaxLines: 4),
                      ),
                    ),
                    Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 4),
                        child: Text(
                          '${1000 - postDescriptionController.text.length} /1000',
                          style: Styles.semibold(
                              size: 12,
                              color:
                                  postDescriptionController.text.length == 1000
                                      ? ColorConstants.RED
                                      : ColorConstants.BLACK),
                        ),
                      )
                    ]),
                    SizedBox(height: size.height * 0.03),
                    if (!widget.isReelsPost)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          InkWell(
                            onTap: () async {
                              _initFilePiker(value);
                            },
                            child: Row(
                              children: [
                                SvgPicture.asset(
                                  'assets/images/image.svg',
                                  colorFilter: ColorFilter.mode(
                                      ColorConstants().primaryColor()!,
                                      BlendMode.srcIn),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 5.0, right: 5.0),
                                  child: Text('photo_video',
                                          style: Styles.regular(
                                              size: 14,
                                              color: ColorConstants.BLACK))
                                      .tr(),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          InkWell(
                            onTap: () async {
                              if (value.files!.length >= 4) {
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(SnackBar(
                                  content:
                                      Text('only_four_files_are_allowed').tr(),
                                ));
                                return;
                              }
                              final cameras = await availableCameras();
                              final firstCamera = cameras.first;
                              print('the value is $firstCamera');

                              await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => TakePictureScreen(
                                            camera: firstCamera,
                                            cameras: cameras,
                                          ))).then((files) async {
                                if (files != null) {
                                  value.addToList(files);
                                  croppedList = value.files?.toList();
                                }
                              });
                            },
                            child: Row(
                              children: [
                                SvgPicture.asset(
                                  'assets/images/camera_y.svg',
                                  colorFilter: ColorFilter.mode(
                                      ColorConstants().primaryColor()!,
                                      BlendMode.srcIn),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 5.0, right: 5),
                                  child: Text('camera',
                                          style: Styles.regular(
                                              size: 14,
                                              color: ColorConstants.BLACK))
                                      .tr(),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    value.files != null
                        ? ShowReadyToPost(
                            provider: value,
                            isReelPost: widget.isReelsPost,
                          )
                        : SizedBox(),
                  ],
                ),
              ),
            ),
          ),
          Consumer<MenuListProvider>(
              builder: (context, menuProvider, child) => Positioned(
                    bottom: 0,
                    child: Container(
                      width: size.width,
                      height: size.height * 0.09,
                      decoration: BoxDecoration(
                        color: ColorConstants.WHITE,
                      ),
                      child: InkWell(
                        onTap: () {
                          widget.provider?.updateList(croppedList);
                          if (value.files!.length != 0) {
                            String? firstExtension = value.files?.first
                                ?.split('/')
                                .last
                                .split('.')
                                .last
                                .toString();
                            bool isVideo = true;
                            if (firstExtension == 'mp4' ||
                                firstExtension == 'mov') isVideo = true;
                            createPost(menuProvider, isVideo, thumnailUrl);
                          } else {
                            AlertsWidget.showCustomDialog(
                                context: context,
                                title: '',
                                text: tr('please_upload_file'),
                                icon: 'assets/images/circle_alert_fill.svg',
                                showCancel: false,
                                oKText: tr('ok'),
                                onOkClick: () async {});
                          }
                        },
                        child: Container(
                          margin: EdgeInsets.symmetric(
                              horizontal: size.width * 0.05,
                              vertical: size.width * 0.03),
                          decoration: BoxDecoration(
                            color: ColorConstants().buttonColor(),
                            borderRadius: BorderRadius.circular(10),
                            gradient: LinearGradient(colors: [
                              ColorConstants().gradientLeft(),
                              ColorConstants().gradientRight(),
                            ]),
                          ),
                          child: Center(
                            child: Text('${tr('share')} ',
                                style: Styles.regular(
                                    size: 16,
                                    color: ColorConstants()
                                        .primaryForgroundColor())),
                          ),
                        ),
                      ),
                    ),
                  ))
        ]),
      ),
    );
  }

  void createPost(MenuListProvider provider, bool isVideo, String? thumbnail) {
    final ReelUploadController controller = Get.put(ReelUploadController());
    controller.reset();

    setState(() {
      widget.filesPath = widget.provider?.getFiles();
    });

    if (!widget.isReelsPost) {
      try {
        Get.rawSnackbar(
          padding: const EdgeInsets.all(0),
          messageText: Card(
            child: StreamBuilder<double>(
                stream: uploadProgressController.stream,
                builder: (context, snapshot) {
                  double progress = (snapshot.data ?? 0.0) / 100;
                  return Column(
                    children: [
                      Container(
                        width: screenWidth!,
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(
                            Radius.circular(4),
                          ),
                          gradient: LinearGradient(colors: [
                            ColorConstants().gradientLeft(),
                            ColorConstants().gradientRight(),
                          ]),
                        ),
                        child: Align(
                            alignment: isRTL
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                            child: Text('uploading_post',
                                    style: Styles.regular(
                                        color: ColorConstants.WHITE))
                                .tr()),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Icon(
                              Icons.file_copy,
                              color: ColorConstants.GREY_3,
                            ),
                            Text('${(progress * 100).toStringAsFixed(0)}%')
                          ],
                        ).reverse(isRTL),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Transform.rotate(
                          angle: isRTL ? -math.pi : 0,
                          child: LinearProgressIndicator(
                            value: progress,
                            color: ColorConstants().gradientRight(),
                          ),
                        ),
                      ),
                    ],
                  );
                }),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
          snackPosition: SnackPosition.TOP,
          duration: Duration(minutes: 20),
          isDismissible: false,
          backgroundColor: Colors.transparent,
          borderRadius: 4,
          boxShadows: [
            BoxShadow(
                color: Color(0xff898989).withValues(alpha: 0.1),
                offset: Offset(0, 4.0),
                blurRadius: 11)
          ],
        );

        final homeRepository = Injector.appInstance.get<HomeRepository>();
        controller.changeStatus(UploadingStatus.process);
        homeRepository.CreatePost(
          null,
          isVideo == true ? 2 : 1,
          'caravan',
          '',
          postDescriptionController.value.text,
          widget.filesPath,
        ).then((CreatePostResponse value) {
          controller.changeStatus(UploadingStatus.end);

          Get.closeAllSnackbars();
          Get.rawSnackbar(
            messageText: Align(
              alignment: isRTL ? Alignment.centerRight : Alignment.centerLeft,
              child: Text(
                value.status == 1
                    ? tr('post_uploaded')
                    : tr('error_post_uploading'),
                style: Styles.regular(size: 14),
              ),
            ),
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
            snackPosition: SnackPosition.TOP,
            backgroundColor: ColorConstants.WHITE,
            borderRadius: 4,
            boxShadows: [
              BoxShadow(
                  color: Color(0xff898989).withValues(alpha: 0.1),
                  offset: Offset(0, 4.0),
                  blurRadius: 11)
            ],
          );
        });
      } catch (e) {
        Get.closeAllSnackbars();
        setState(() {
          isPostedLoading = false;
        });
        Get.rawSnackbar(
          messageText: Align(
            alignment: isRTL ? Alignment.centerRight : Alignment.centerLeft,
            child: Text(
              'error_post_uploading',
              style: Styles.regular(size: 14),
            ).tr(),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
          snackPosition: SnackPosition.TOP,
          backgroundColor: ColorConstants.WHITE,
          borderRadius: 4,
          boxShadows: [
            BoxShadow(
                color: Color(0xff898989).withValues(alpha: 0.1),
                offset: Offset(0, 4.0),
                blurRadius: 11)
          ],
        );
      }

      Future.delayed(Duration(seconds: 1))
          .then((value) => Navigator.pop(context));
    } else {
      try {
        Get.rawSnackbar(
          padding: const EdgeInsets.all(0),
          messageText: Card(
            child: StreamBuilder<double>(
                stream: uploadProgressController.stream,
                builder: (context, snapshot) {
                  double progess = (snapshot.data ?? 0.0) / 100;
                  return Column(
                    children: [
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        width: screenWidth,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(
                            Radius.circular(4),
                          ),
                          gradient: LinearGradient(colors: [
                            ColorConstants().gradientLeft(),
                            ColorConstants().gradientRight(),
                          ]),
                        ),
                        child: Align(
                            alignment: isRTL
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                            child: Text('uploading_your_reel_please_wait',
                                    style: Styles.regular(
                                        color: ColorConstants.WHITE))
                                .tr()),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Icon(
                              Icons.file_copy,
                              color: ColorConstants.GREY_3,
                            ),
                            Text('${(progess * 100).toStringAsFixed(0)}%')
                          ],
                        ).reverse(isRTL),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Transform.rotate(
                          angle: isRTL ? -math.pi : 0,
                          child: LinearProgressIndicator(
                            value: progess,
                            color: ColorConstants().gradientRight(),
                          ),
                        ),
                      ),
                    ],
                  );
                }),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
          snackPosition: SnackPosition.TOP,
          duration: Duration(minutes: 20),
          isDismissible: false,
          backgroundColor: Colors.transparent,
          borderRadius: 4,
          boxShadows: [
            BoxShadow(
                color: Color(0xff898989).withValues(alpha: 0.1),
                offset: Offset(0, 4.0),
                blurRadius: 11)
          ],
        );

        final homeRepository = Injector.appInstance.get<HomeRepository>();
        homeRepository.CreatePost(
          thumnailUrl,
          2,
          'reels',
          '',
          postDescriptionController.value.text,
          widget.filesPath,
        ).then((CreatePostResponse value) {
          Get.closeAllSnackbars();
          Get.rawSnackbar(
            messageText: Align(
              alignment: isRTL ? Alignment.centerRight : Alignment.centerLeft,
              child: Text(
                value.status == 1
                    ? tr('reel_uploaded')
                    : tr('error__uploading_reel'),
                style: Styles.regular(size: 14),
              ),
            ),
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
            snackPosition: SnackPosition.TOP,
            backgroundColor: ColorConstants.WHITE,
            borderRadius: 4,
            boxShadows: [
              BoxShadow(
                  color: Color(0xff898989).withValues(alpha: 0.1),
                  offset: Offset(0, 4.0),
                  blurRadius: 11)
            ],
          );
        });
      } catch (e) {
        Get.closeAllSnackbars();
        setState(() {
          isPostedLoading = false;
        });
        Get.rawSnackbar(
          messageText: Text(
            tr('error__uploading_reel'),
            style: Styles.regular(size: 14),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
          snackPosition: SnackPosition.TOP,
          backgroundColor: ColorConstants.WHITE,
          borderRadius: 4,
          boxShadows: [
            BoxShadow(
                color: Color(0xff898989).withValues(alpha: 0.1),
                offset: Offset(0, 4.0),
                blurRadius: 11)
          ],
        );
      }

      Navigator.pop(context);
      Navigator.pop(context);
    }
  }

  void _handleCreatePostResponse(
      CreatePostState state, CreatePostProvider provider) {
    var loginState = state;
    setState(() async {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isPostedLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success.................... create post");

          isPostedLoading = false;
          responseData = state.response;
          widget.provider?.clearList();

          if (responseData!.status == 1) {
            if (widget.isReelsPost == true) Navigator.pop(context);
            Navigator.pop(context);
          }

          break;
        case ApiStatus.ERROR:
          isPostedLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Future<String?> _getImages(CreatePostProvider provider) async {
    final pickedFileC = await ImagePicker()
        .pickImage(source: ImageSource.camera, maxWidth: 900, maxHeight: 450);
    if (pickedFileC != null) {
      provider.addToList(pickedFileC.path);
      croppedList = provider.files?.toList();
    }
    return null;
  }

  void _initFilePiker(CreatePostProvider provider) async {
    if (provider.files!.length > 4) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('only_four_files_are_allowed').tr(),
      ));
      return;
    }
    FilePickerResult? result;

    if (Platform.isIOS) {
      result = await FilePicker.platform.pickFiles(
          allowMultiple: true, type: FileType.media, allowedExtensions: []);
    } else {
      result = await FilePicker.platform.pickFiles(
          allowMultiple: true,
          type: FileType.custom,
          onFileLoading: (path) {},
          allowedExtensions: ['jpg', 'jpeg', 'png', 'mp4']);
    }

    if (result != null) {
      for (int i = 0; i < result.paths.length; i++) {
        if (provider.files!.length >= 4) {
          break;
        }

        if (((File(result.paths[i]!).lengthSync() / 1000000) +
                provider.totalMBSize) >
            50.0) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('${tr('content_file_size_larger_than')} 50 MB'),
          ));
        } else {
          provider.addSize(File(result.paths[i]!).lengthSync() ~/ 1000000);
          provider.addToList(result.paths[i]);
        }

        croppedList = provider.files?.toList();
      }

      if (provider.files!.length >= 4) {
        log('break here 2');

        provider.updateList(
          provider.files?.sublist(provider.files!.length - 4),
        );
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('only_four_files_are_allowed').tr(),
        ));
      }
    }
    //}
  }
}

class ShowReadyToPost extends StatefulWidget {
  final CreatePostProvider? provider;
  final bool isReelPost;
  const ShowReadyToPost({Key? key, this.provider, this.isReelPost = false})
      : super(key: key);

  @override
  _ShowReadyToPostState createState() => _ShowReadyToPostState();
}

class _ShowReadyToPostState extends State<ShowReadyToPost> {
  List<PlatformUiSettings>? buildUiSettings(BuildContext context) {
    return [
      AndroidUiSettings(
          toolbarTitle: '',
          toolbarColor: Colors.black,
          toolbarWidgetColor: Colors.white,
          hideBottomControls: !Platform.isAndroid,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false),
      IOSUiSettings(
        title: '',
      ),
    ];
  }

  bool _isVideoFile(String filePath) {
    final videoExtensions = ['.mp4', '.mov', '.hevc', '.h.265', '.temp'];
    final lowerPath = filePath.toLowerCase();
    return videoExtensions.any((ext) => lowerPath.contains(ext));
  }

  @override
  void initState() {
    super.initState();
    setValue();
  }

  void setValue() {
    List<String?>? readyToPost;
    readyToPost = widget.provider!.files;
    croppedList = readyToPost?.toList();
    if (croppedList!.length > 4) {
      log('break here 3');
      croppedList = croppedList!.sublist(0, 4);
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('only_four_files_are_allowed').tr(),
      ));
    }
  }

  /// Generate thumbnail from video file for cropping
  Future<String?> _generateThumbnailForCropping(String videoPath) async {
    try {
      // Check if video file exists first
      if (!await File(videoPath).exists()) {
        log("Video file does not exist: $videoPath");
        return null;
      }

      final tempDir = await getTemporaryDirectory();
      final fileName =
          'crop_thumb_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final thumbnailPath = '${tempDir.path}/$fileName';

      final thumbnailFile = await Thumbnail.VideoThumbnail.thumbnailFile(
        video: videoPath,
        thumbnailPath: thumbnailPath,
        imageFormat: Thumbnail.ImageFormat.JPEG,
        maxHeight: 1080,
        quality: 85,
      );

      if (thumbnailFile != null && await File(thumbnailFile).exists()) {
        log("Thumbnail generated for cropping: $thumbnailFile");
        return thumbnailFile;
      }
    } catch (e) {
      log('Error generating thumbnail for cropping: $e');
    }
    return null;
  }

  Future<String> _cropImage(_pickedFile) async {
    // Check if file exists before attempting to crop
    if (!await File(_pickedFile).exists()) {
      log('File does not exist for cropping: $_pickedFile');
      return _pickedFile;
    }

    String fileToProcess = _pickedFile;

    // If it's a video file, generate a thumbnail first
    if (_isVideoFile(_pickedFile)) {
      log('Video file detected, generating thumbnail: $_pickedFile');
      try {
        final thumbnailPath = await _generateThumbnailForCropping(_pickedFile);
        if (thumbnailPath != null) {
          fileToProcess = thumbnailPath;
          log('Thumbnail generated successfully: $thumbnailPath');
        } else {
          log('Failed to generate thumbnail, returning original video file');
          return _pickedFile;
        }
      } catch (e) {
        log('Error generating thumbnail for cropping: $e');
        return _pickedFile;
      }
    }
    if (_pickedFile != null) {
      CroppedFile? croppedFile = await ImageCropper().cropImage(
        maxHeight: MediaQuery.sizeOf(context).height.toInt() - 100,
        sourcePath: fileToProcess,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 100,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: '',
            toolbarColor: Colors.black,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.original,
            lockAspectRatio: false,
            showCropGrid: false,
            hideBottomControls: false,
            aspectRatioPresets: [
              CropAspectRatioPreset.original,
              CropAspectRatioPreset.ratio4x3,
              CropAspectRatioPreset.ratio16x9
            ],
          ),
          IOSUiSettings(
            title: '',
            aspectRatioPresets: [
              CropAspectRatioPreset.original,
              CropAspectRatioPreset.ratio4x3,
              CropAspectRatioPreset.ratio16x9
            ],
          ),
        ],
      );
      if (croppedFile != null) {
        return croppedFile.path;
      }
    }
    return _pickedFile;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 10),
      height: MediaQuery.of(context).size.height * 0.5,
      width: double.infinity,
      child: ListView.builder(
          scrollDirection: Axis.horizontal,
          shrinkWrap: true,
          itemCount: croppedList!.length,
          itemBuilder: (context, index) {
            File pickedFile = File(croppedList![index]!);

            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(2),
                child: Stack(children: [
                  Container(
                    color: ColorConstants.GREY,
                    width: MediaQuery.of(context).size.width * 0.8,
                    child: pickedFile != null
                        ? pickedFile.path.contains('.pdf')
                            ? InkWell(child: PDFScreen(path: pickedFile.path))
                            : pickedFile.path.contains('.mp4') ||
                                    pickedFile.path.contains(".temp") ||
                                    pickedFile.path.contains('.mov') ||
                                    pickedFile.path.contains('.hevc') ||
                                    pickedFile.path.contains('.h.265')
                                ? ShowImage(path: pickedFile.path)
                                : Image.file(
                                    pickedFile,
                                    height: 240,
                                    fit: BoxFit.contain,
                                  )
                        : SizedBox(),
                  ),
                  if (pickedFile.path.contains('.mp4') ||
                      pickedFile.path.contains(".temp") ||
                      pickedFile.path.contains('.mov') ||
                      pickedFile.path.contains('.hevc') ||
                      pickedFile.path.contains('.h.265'))
                    Positioned.fill(
                        child: Align(
                            alignment: Alignment.center,
                            child: SvgPicture.asset(
                              'assets/images/play_video_icon.svg',
                              height: 50,
                              width: 50,
                              allowDrawingOutsideViewBox: true,
                            ))),
                  Positioned(
                    right: 5,
                    top: 5,
                    child: Container(
                      padding: EdgeInsets.all(2),
                      decoration: BoxDecoration(
                          color: ColorConstants.BLACK, shape: BoxShape.circle),
                      child: IconButton(
                        onPressed: () {
                          AlertsWidget.showCustomDialog(
                              context: context,
                              title: tr('deletePost'),
                              text: tr('confirm_deletion_textone'),
                              icon: 'assets/images/circle_alert_fill.svg',
                              onOkClick: () async {
                                widget.provider?.subSize(
                                    File('${widget.provider?.files?[index]}')
                                            .lengthSync() ~/
                                        1000000);

                                widget.provider!.removeFromList(index);

                                setState(() {
                                  croppedList = widget.provider?.files;
                                  if (widget.isReelPost &&
                                      croppedList?.length == 0) {
                                    Navigator.pop(context);
                                  }
                                });
                              });
                        },
                        padding: EdgeInsets.zero,
                        constraints: BoxConstraints(),
                        icon: Icon(Icons.delete_forever,
                            size: 18, color: Colors.white),
                      ),
                    ),
                  ),
                  if (!(pickedFile.path.contains('.mp4') ||
                      pickedFile.path.contains('.mov') ||
                      pickedFile.path.contains('.hevc') ||
                      pickedFile.path.contains('.h.265')))
                    Positioned(
                      left: 5,
                      top: 5,
                      child: Container(
                        padding: EdgeInsets.all(2),
                        decoration: BoxDecoration(
                            color: ColorConstants.BLACK,
                            shape: BoxShape.circle),
                        child: IconButton(
                          onPressed: () {
                            AlertsWidget.showCustomDialog(
                                context: context,
                                title: "",
                                text: tr('want_to_crop'),
                                icon: 'assets/images/circle_alert_fill.svg',
                                oKText: tr('ok'),
                                onOkClick: () async {
                                  String croppedPath = await _cropImage(
                                      widget.provider!.files?[index]);
                                  if (widget.provider!.files?[index] !=
                                      croppedPath)
                                    setState(() {
                                      croppedList![index] = croppedPath;
                                    });
                                });
                          },
                          padding: EdgeInsets.zero,
                          constraints: BoxConstraints(),
                          icon: Icon(Icons.crop, size: 15, color: Colors.white),
                        ),
                      ),
                    ),
                ]),
              ),
            );
          }),
    );
  }
}

class ShowImage extends StatelessWidget {
  final String? path;
  ShowImage({Key? key, this.path}) : super(key: key);

  Uint8List? imageFile;
  Future<File> writeToFile(Uint8List data) async {
    final directory = await getTemporaryDirectory();
    final file = File('${directory.path}/example.jpg');
    await file.writeAsBytes(data);
    return file;
  }

  Future<Uint8List?> getFile() async {
    final uint8list = await Thumbnail.VideoThumbnail.thumbnailData(
      video: path!,
      imageFormat: Thumbnail.ImageFormat.JPEG,
      quality: 10,
    );
    File file = await writeToFile(uint8list!);
    thumnailUrl = file.path;

    return uint8list;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Uint8List?>(
      future: getFile(), // async work
      builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.hasData) {
          return Image.memory(
            snapshot.data,
            fit: BoxFit.cover,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
          );
        }

        return Shimmer.fromColors(
          baseColor: Color(0xffe6e4e6),
          highlightColor: Color(0xffeaf0f3),
          child: Container(
              height: 400,
              margin: EdgeInsets.only(left: 2),
              width: 150,
              decoration: BoxDecoration(
                color: Colors.white,
              )),
        );
      },
    );
  }
}

extension on Row {
  Widget reverse(bool isReverse) {
    return Row(
        mainAxisAlignment: mainAxisAlignment,
        children: isReverse ? children.reversed.toList() : children);
  }
}
