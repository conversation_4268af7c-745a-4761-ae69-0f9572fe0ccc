import 'dart:async';
import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/home_request/user_program_subscribe.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/home_response/course_category_list_id_response.dart';
import 'package:masterg/data/models/response/home_response/learning_space_response.dart';
import 'package:masterg/data/models/response/home_response/my_assignment_response.dart';
import 'package:masterg/data/models/response/home_response/onboard_sessions.dart';
import 'package:masterg/data/models/response/home_response/popular_courses_response.dart';
import 'package:masterg/data/models/response/home_response/user_analytics_response.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/rounded_appbar.dart';
import 'package:masterg/pages/ghome/my_assessments.dart';
import 'package:masterg/pages/ghome/my_assignments.dart';
import 'package:masterg/pages/ghome/my_classes.dart';
import 'package:masterg/pages/ghome/my_courses.dart';
import 'package:masterg/pages/singularis/app_drawer_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/call_once.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/str_to_time.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';
import '../training_pages/new_screen/courses_details_page.dart';
import '../user_profile_page/portfolio_create_form/portfolio_page.dart';

import 'dart:ui' as ui;

class ProgramDetailsPage extends StatefulWidget {
  const ProgramDetailsPage({Key? key}) : super(key: key);

  @override
  _ProgramDetailsPageState createState() => _ProgramDetailsPageState();
}

class _ProgramDetailsPageState extends State<ProgramDetailsPage>
    with TickerProviderStateMixin {
  List<AssignmentList>? assignmentList = [];
  List<UserAnalyticsData>? userAnalytics = [];
  List<LearningSpace>? learningSpace = [];

  bool isJoyCategoryLoading = true;
  bool isCourseList1Loading = true;
  bool isProgramListLoading = true;
  bool isNotLiveclass = false;
  Timer? timer;
  int nocourseAssigned = 0;
  List<MProgram>? courseList1;
  List<Liveclass>? liveclassList;
  List<PopularCourses>? popularcoursess = [];
  List<Recommended>? recommendedcourses = [];
  List<OtherLearners>? otherLearners = [];
  List<ShortTerm>? shortTerm = [];
  List<HighlyRated>? highlyRated = [];
  List<MostViewed>? mostViewed = [];
  AnimationController? controller;
  int selectedSwiperIndex = 0;
  bool? selectedVewAttendance = true;
  bool? selectedVewRecording = false;

  var _scaffoldKey = new GlobalKey<ScaffoldState>();
  MenuListProvider? menuProvider;

  String? currentZoomUrl;
  String? currentOpenUrl;

  Box? box;

  @override
  void initState() {
    super.initState();
    _getLiveClass();
    _getUserAnalytics();
    _getFilteredPopularCourses();
//_getLiveClass() event commented because this event also used there are not any used with timer
//date : 17/01/2024 :by Sanjay maddheshiya after discussion with santosh sir
    // timer = Timer.periodic(Duration(seconds: 60), (Timer t) => _getLiveClass());
    //timer =Timer.periodic(Duration(seconds: 20), (Timer t) => _getUserAnalytics());
    //timer = Timer.periodic(Duration(seconds: 20), (Timer t) => _getPopularCourses());
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ColorConstants.GREY,
        key: _scaffoldKey,
        endDrawer: new AppDrawer(),
        body: BlocManager(
          initState: (context) {},
          child: Consumer<MenuListProvider>(
            builder: (context, mp, child) => BlocListener<HomeBloc, HomeState>(
                listener: (context, state) async {
                  if (state is getLiveClassState)
                    _handleLiveClassResponse(state);
                  if (state is PopularCoursesState)
                    _handlePopularCoursesStateResponse(state);
                  if (state is FilteredPopularCoursesState)
                    _handlePopularFilteredCourses(state);
                  if (state is CourseCategoryListIDState) {
                    _handleCourseList1Response(state);
                  }
                  if (state is MyAssignmentState) _handleAnnouncmentData(state);
                  if (state is UserAnalyticsState)
                    _handleUserAnalyticsData(state);
                  if (state is LearningSpaceState)
                    _handleLearningSpaceData(state);
                  setState(() {
                    menuProvider = mp;
                  });
                  if (state is ZoomOpenUrlState) handleOpenUrlState(state);
                },
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _customAppBar(),
                      liveclassList != null && liveclassList!.length > 0
                          ? _getTodayClass()
                          : isNotLiveclass == true
                              ? Container()
                              : Container(),

                      Container(
                        color: ColorConstants.GREY,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 14, vertical: 8),
                          child: Row(children: [
                            Text(
                              APK_DETAILS["package_name"] ==
                                      "com.singulariswow.mec"
                                  ? 'Programme Details'
                                  : 'Programme Details',
                              style: Styles.bold(size: 16),
                            ).tr(),
                          ]),
                        ),
                      ),

                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Divider(),
                      ),
                      programDetails(),
                      SizedBox(height: 10),
                      Center(
                        child: SizedBox(
                          height: 20,
                          child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: min(4, 8),
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                return Align(
                                  alignment: Alignment.center,
                                  child: Container(
                                      height: 8,
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 5),
                                      width: 14,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(7.0),
                                          color: selectedSwiperIndex == index
                                              ? const Color(0xff4D5DCC)
                                                  .withValues(alpha: 0.7)
                                              : ColorConstants.BG_GREY,
                                          border: Border.all(
                                              width: 1,
                                              color: ColorConstants
                                                  .TEXT_DARK_BLACK
                                                  .withValues(alpha: 0.7))),
                                      child: Container()),
                                );
                              }),
                        ),
                      ),

                      SizedBox(height: 20),
                      Container(
                        color: ColorConstants.GREY,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 14, vertical: 8),
                          child: Row(children: [
                            Text(
                              APK_DETAILS["package_name"] ==
                                      "com.singulariswow.mec"
                                  ? 'Faculty Console'
                                  : 'Faculty Console',
                              style: Styles.bold(size: 16),
                            ).tr(),
                          ]),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Divider(),
                      ),
                      facultyConsol(),
                      SizedBox(height: 10),

                      Center(
                        child: SizedBox(
                          height: 20,
                          child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: min(4, 8),
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                return Align(
                                  alignment: Alignment.center,
                                  child: Container(
                                      height: 8,
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 5),
                                      width: 14,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(7.0),
                                          color: selectedSwiperIndex == index
                                              ? const Color(0xff4D5DCC)
                                                  .withValues(alpha: 0.7)
                                              : ColorConstants.BG_GREY,
                                          border: Border.all(
                                              width: 1,
                                              color: ColorConstants
                                                  .TEXT_DARK_BLACK
                                                  .withValues(alpha: 0.7))),
                                      child: Container()),
                                );
                              }),
                        ),
                      ),
                      Container(
                        color: ColorConstants.GREY,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 14, vertical: 4),
                          child: Text(
                            APK_DETAILS["package_name"] ==
                                    "com.singulariswow.mec"
                                ? 'Summary'
                                : 'Summary',
                            style: Styles.regular(size: 16),
                          ).tr(),
                        ),
                      ),
                      summary(),

                      //  summary(),
                      Container(
                        color: ColorConstants.GREY,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 14, vertical: 4),
                          child: Row(children: [
                            Column(
                              children: [
                                Text(
                                  APK_DETAILS["package_name"] ==
                                          "com.singulariswow.mec"
                                      ? 'Todays Classes'
                                      : 'Todays Classes',
                                  style: Styles.bold(size: 16),
                                ).tr(),
                                Text(
                                  'Live and Scheduled',
                                  style: Styles.textRegular(size: 12),
                                )
                              ],
                            ),
                          ]),
                        ),
                      ),

                      todayClasses(),

                      SizedBox(
                        height: 10,
                      ),
                      Container(
                        color: ColorConstants.GREY,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 14, vertical: 4),
                          child: Text(
                            APK_DETAILS["package_name"] ==
                                    "com.singulariswow.mec"
                                ? 'Concluded'
                                : 'Concluded',
                            style: Styles.regular(size: 16),
                          ).tr(),
                        ),
                      ),
                      concludedClassess(),
                      // Center(
                      //   child: Container(
                      //     color: ColorConstants.GREY,
                      //     //padding: EdgeInsets.only(top: 5.0),
                      //     height: height(context) * (kIsWeb ? 0.3 : 0.18),
                      //     child: ListView(
                      //       scrollDirection: Axis.horizontal,
                      //       shrinkWrap: true,
                      //       children: [
                      //         topRoundedCard(
                      //             'assets/images/my_classes.svg', tr('classes'),
                      //             () {
                      //           Navigator.push(
                      //               context,
                      //               MaterialPageRoute(
                      //                   builder: (context) => MyClasses()));
                      //         }),
                      //         topRoundedCard(
                      //             'assets/images/my_assignment_card.svg',
                      //             tr('assignments'), () {
                      //           Navigator.push(
                      //               context,
                      //               MaterialPageRoute(
                      //                   builder: (context) =>
                      //                       MyAssignmentPage()));
                      //         }),
                      //         topRoundedCard('assets/images/my_quiz_card.svg',
                      //             tr('quizzes'), () {
                      //           Navigator.push(
                      //               context,
                      //               MaterialPageRoute(
                      //                   builder: (context) =>
                      //                       MyAssessmentPage()));
                      //         }),
                      //       ],
                      //     ),
                      //   ),
                      // ),

                      // SizedBox(height: 12),
                      // _getOtherLearnerTopics(context),
                      // _getRecommendedCourses(context)
                    ],
                  ),
                )),
          ),
        ));
  }

  Widget topRoundedCard(String img, String name, Function action) {
    return Container(
      height: height(context),
      width: width(context) * 0.3,
      margin: EdgeInsets.only(left: 4.0, right: 4.0),
      decoration: BoxDecoration(
          color: Colors.green,
          gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: <Color>[
                ColorConstants().gradientLeft(),
                ColorConstants().gradientRight()
              ]),
          borderRadius: BorderRadius.only(
              topRight: Radius.circular(50),
              bottomRight: Radius.circular(12),
              topLeft: Radius.circular(12),
              bottomLeft: Radius.circular(12))),
      child: Stack(
        children: [
          Positioned(
              left: -12,
              top: -15,
              child: Container(
                width: height(context) * 0.09,
                height: height(context) * 0.09,
                decoration: BoxDecoration(
                    color: ColorConstants.WHITE.withValues(alpha: 0.3),
                    shape: BoxShape.circle),
                child: Center(
                    child: SvgPicture.asset(
                  img,
                  width: height(context) * 0.03,
                )),
              )),
          Positioned(
              left: 10,
              top: height(context) * 0.08,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('my',
                          style: Styles.semibold(
                              size: 12, color: ColorConstants.WHITE))
                      .tr(),
                  Text('$name',
                      style: Styles.semibold(
                          size: 12, color: ColorConstants.WHITE)),
                ],
              )),
          Positioned(
              right: 10,
              bottom: 10,
              child: InkWell(
                onTap: () {
                  action();
                },
                child: Container(
                  width: height(context) * 0.04,
                  height: height(context) * 0.04,
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(boxShadow: <BoxShadow>[
                    BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 4.0,
                        offset: Offset(0.0, 4.07))
                  ], color: ColorConstants.WHITE, shape: BoxShape.circle),
                  child: SvgPicture.asset(
                    'assets/images/gradient_arrow.svg',
                    colorFilter: ColorFilter.mode(
                        ColorConstants().primaryColor()!, BlendMode.srcIn),
                    // width: height(context) * 0.05,
                    // height: height(context) * 0.05,
                  ),
                ),
              ))
        ],
      ),
    );
  }

  programDetails() {
    return Stack(children: [
      SingleChildScrollView(
        child: Container(
          height: MediaQuery.of(context).size.height * 0.3,
          margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          width: MediaQuery.of(context).size.width * 0.9,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(6)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 85,
                      height: 90,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: CachedNetworkImage(
                          imageUrl:
                              'https://images.pexels.com/photos/7147484/pexels-photo-7147484.jpeg',
                          width: 100,
                          height: 120,
                          errorWidget: (context, url, error) =>
                              SvgPicture.asset(
                            'assets/images/gscore_postnow_bg.svg',
                          ),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                                margin: EdgeInsets.only(left: 9, top: 3),
                                child: Text('Game Art',
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    softWrap: false,
                                    style: Styles.semibold(size: 16))),
                            SizedBox(width: 80),
                            Container(
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    border: Border.all(
                                        color: Color(0xffF0F1FA), width: 2)),
                                margin: EdgeInsets.only(left: 9, top: 3),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Text('Batch 1',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      softWrap: false,
                                      style: Styles.semibold(
                                          size: 12, color: Color(0xff4F5AED))),
                                )),
                          ],
                        ),
                        Container(
                            width: width(context) * 0.5,
                            margin: EdgeInsets.only(left: 9, top: 3),
                            child: Text('Lorem ipsum dolor sit amet',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                softWrap: false,
                                style: Styles.regular(size: 12))),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                  width: width(context) * 0.5,
                  margin: EdgeInsets.only(left: 9, top: 3),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_month_outlined, size: 20),
                      SizedBox(width: 10),
                      Text('01-Jan-24 to 02-Jan-25',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          softWrap: false,
                          style: Styles.regular(size: 12)),
                    ],
                  )),
              Container(
                width: width(context),
                margin: EdgeInsets.only(left: 9, top: 3, right: 9),
                child: Row(
                  children: [
                    SvgPicture.asset(
                      'assets/images/person.svg',
                      height: 20.0,
                      width: 20.0,
                      allowDrawingOutsideViewBox: true,
                    ),
                    SizedBox(width: 10),
                    Text('Mr. Rohan Bajaj',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        softWrap: false,
                        style: Styles.regular(size: 12)),
                    Spacer(),
                    Row(
                      children: [
                        SvgPicture.asset(
                          'assets/images/local_library.svg',
                          height: 20.0,
                          width: 20.0,
                          allowDrawingOutsideViewBox: true,
                        ),
                        SizedBox(width: 10),
                        Text('440',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: Styles.regular(size: 12)),
                      ],
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Container(
          height: 50,
          margin: EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
              color: Color(0xffF1FBFF),
              borderRadius: BorderRadius.vertical(bottom: Radius.circular(20)),
              border: Border.all(color: Color(0xffF1FBFF))),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Text('Batch Completion ', style: Styles.bold(size: 16)),
              ],
            ),
          ),
        ),
      ),
    ]);
  }

  facultyConsol() {
    return Stack(children: [
      SingleChildScrollView(
        child: Container(
          height: MediaQuery.of(context).size.height * 0.3,
          margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          width: MediaQuery.of(context).size.width * 0.9,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(6)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 85,
                      height: 90,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: CachedNetworkImage(
                          imageUrl:
                              'https://images.pexels.com/photos/7147484/pexels-photo-7147484.jpeg',
                          width: 100,
                          height: 120,
                          errorWidget: (context, url, error) =>
                              SvgPicture.asset(
                            'assets/images/gscore_postnow_bg.svg',
                          ),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                                margin: EdgeInsets.only(left: 9, top: 3),
                                child: Text('Game Art',
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    softWrap: false,
                                    style: Styles.semibold(size: 16))),
                            SizedBox(width: 80),
                            Container(
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    border: Border.all(
                                        color: Color(0xffF0F1FA), width: 2)),
                                margin: EdgeInsets.only(left: 9, top: 3),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Text('Batch 1',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      softWrap: false,
                                      style: Styles.semibold(
                                          size: 12, color: Color(0xff4F5AED))),
                                )),
                          ],
                        ),
                        Container(
                            width: width(context) * 0.5,
                            margin: EdgeInsets.only(left: 9, top: 3),
                            child: Text('Lorem ipsum dolor sit amet',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                softWrap: false,
                                style: Styles.regular(size: 12))),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                  width: width(context) * 0.5,
                  margin: EdgeInsets.only(left: 9, top: 3),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_month_outlined, size: 20),
                      SizedBox(width: 10),
                      Text('01-Jan-24 to 02-Jan-25',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          softWrap: false,
                          style: Styles.regular(size: 12)),
                    ],
                  )),
              Container(
                width: width(context),
                margin: EdgeInsets.only(left: 9, top: 3, right: 9),
                child: Row(
                  children: [
                    SvgPicture.asset(
                      'assets/images/person.svg',
                      height: 20.0,
                      width: 20.0,
                      allowDrawingOutsideViewBox: true,
                    ),
                    SizedBox(width: 10),
                    Text('Mr. Rohan Bajaj',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        softWrap: false,
                        style: Styles.regular(size: 12)),
                    Spacer(),
                    Row(
                      children: [
                        SvgPicture.asset(
                          'assets/images/local_library.svg',
                          height: 20.0,
                          width: 20.0,
                          allowDrawingOutsideViewBox: true,
                        ),
                        SizedBox(width: 10),
                        Text('440',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: Styles.regular(size: 12)),
                      ],
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Container(
          height: 50,
          margin: EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
              color: Color(0xffF1FBFF),
              borderRadius: BorderRadius.vertical(bottom: Radius.circular(20)),
              border: Border.all(color: Color(0xffF1FBFF))),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Text('Batch Completion ', style: Styles.bold(size: 16)),
              ],
            ),
          ),
        ),
      ),
    ]);
  }

  todayClasses() {
    return Container(
      child: ListView.builder(
        shrinkWrap: true,
        physics: BouncingScrollPhysics(),
        itemCount: 10,
        itemBuilder: (BuildContext context, int index) {
          // This function is called for each item in the list
          return Container(
            height: MediaQuery.of(context).size.height * 0.23,
            margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            width: MediaQuery.of(context).size.width * 0.9,
            decoration: BoxDecoration(
                color: ColorConstants.ACCENT_COLOR,
                borderRadius: BorderRadius.circular(6)),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border:
                                Border.all(color: Color(0xffF0F1FA), width: 2)),
                        margin: EdgeInsets.only(left: 9, top: 3),
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Text(
                            'Batch 1',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: Styles.semibold(
                                size: 12, color: Color(0xff4F5AED)),
                          ),
                        ),
                      ),
                      Spacer(),
                      Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border:
                                Border.all(color: Color(0xffF0F1FA), width: 2)),
                        margin: EdgeInsets.only(left: 9, top: 3),
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                'assets/images/live_icon.svg',
                                height: 20.0,
                                width: 20.0,
                                allowDrawingOutsideViewBox: true,
                              ),
                              SizedBox(
                                  width:
                                      4), // Added spacing between icon and text
                              Text(
                                'Live',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                softWrap: false,
                                style: Styles.semibold(
                                    size: 12, color: Color(0xffEA575E)),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 9, top: 3),
                    child: Text('Offline class name',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        softWrap: false,
                        style: Styles.bold(size: 16)),
                  ),
                  SizedBox(
                      height: 8), // Added spacing between class name and time
                  Row(
                    children: [
                      SizedBox(
                        child: Row(
                          children: [
                            Icon(Icons.alarm, size: 20),
                            SizedBox(
                                width:
                                    4), // Added spacing between icon and text
                            Text('9:00 AM to 12 PM',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                softWrap: false,
                                style: Styles.regular(size: 12)),
                          ],
                        ),
                      ),
                      Spacer(),
                      SizedBox(
                        child: Row(
                          children: [
                            Icon(Icons.calendar_month_outlined, size: 20),
                            SizedBox(
                                width:
                                    4), // Added spacing between icon and text
                            Text('01-Jan-24, Tuesday',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                softWrap: false,
                                style: Styles.regular(size: 12)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  Divider(),
                  //  SizedBox(height: 10),
                  Center(
                    child: Container(
                      height: height(context) * 0.05,
                      width: width(context) * 0.6,
                      decoration: BoxDecoration(
                        color: Color(0xff3CA4D2),
                        borderRadius: BorderRadius.circular(10),
                        // border: Border.all(color: Color(0xffF0F1FA), width: 2)
                      ),
                      margin: EdgeInsets.only(left: 9, top: 3),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Center(
                            child: Text('Mark Attendance',
                                style: Styles.textBold(
                                    size: 12,
                                    color: ColorConstants.ACCENT_COLOR))),
                      ),
                    ),
                  )
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  concludedClassess() {
    return Container(
      child: ListView.builder(
        shrinkWrap: true,
        physics: BouncingScrollPhysics(),
        itemCount: 10,
        itemBuilder: (BuildContext context, int index) {
          // This function is called for each item in the list
          return Container(
            height: MediaQuery.of(context).size.height * 0.23,
            margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            width: MediaQuery.of(context).size.width * 0.9,
            decoration: BoxDecoration(
                color: ColorConstants.ACCENT_COLOR,
                borderRadius: BorderRadius.circular(6)),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border:
                                Border.all(color: Color(0xffF0F1FA), width: 2)),
                        margin: EdgeInsets.only(left: 9, top: 3),
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Text(
                            'Batch 1',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                            style: Styles.semibold(
                                size: 12, color: Color(0xff4F5AED)),
                          ),
                        ),
                      ),
                      Spacer(),
                      Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border:
                                Border.all(color: Color(0xffF0F1FA), width: 2)),
                        margin: EdgeInsets.only(left: 9, top: 3),
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                'assets/images/live_icon.svg',
                                height: 20.0,
                                width: 20.0,
                                allowDrawingOutsideViewBox: true,
                              ),
                              SizedBox(
                                  width:
                                      4), // Added spacing between icon and text
                              Text(
                                'Live',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                softWrap: false,
                                style: Styles.semibold(
                                    size: 12, color: Color(0xffEA575E)),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 9, top: 3),
                    child: Text('Offline class name',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        softWrap: false,
                        style: Styles.bold(size: 16)),
                  ),
                  SizedBox(
                      height: 8), // Added spacing between class name and time
                  Row(
                    children: [
                      SizedBox(
                        child: Row(
                          children: [
                            Icon(Icons.alarm, size: 20),
                            SizedBox(
                                width:
                                    4), // Added spacing between icon and text
                            Text('9:00 AM to 12 PM',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                softWrap: false,
                                style: Styles.regular(size: 12)),
                          ],
                        ),
                      ),
                      Spacer(),
                      SizedBox(
                        child: Row(
                          children: [
                            Icon(Icons.calendar_month_outlined, size: 20),
                            SizedBox(
                                width:
                                    4), // Added spacing between icon and text
                            Text('01-Jan-24, Tuesday',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                softWrap: false,
                                style: Styles.regular(size: 12)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  Divider(),
                  //  SizedBox(height: 10),
                  Row(
                    children: [
                      InkWell(
                        onTap: () {
                          setState(() {
                            selectedVewAttendance = false;
                            selectedVewRecording = true;
                          });
                        },
                        child: Container(
                          height: height(context) * 0.05,
                          width: width(context) * 0.4,
                          decoration: BoxDecoration(
                              color: selectedVewRecording == true
                                  ? Color(0xff3CA4D2)
                                  : ColorConstants.WHITE,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                  color: Color(0xffF0F1FA), width: 2)),
                          margin: EdgeInsets.only(left: 9, top: 3),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Center(
                                child: Text('View Recording',
                                    style: Styles.textBold(
                                        size: 12,
                                        color: selectedVewRecording == true
                                            ? ColorConstants.WHITE
                                            : Color(0xff3CA4D2)))),
                          ),
                        ),
                      ),
                      Spacer(),
                      InkWell(
                        onTap: () {
                          setState(() {
                            selectedVewAttendance = true;
                            selectedVewRecording = false;
                          });
                        },
                        child: Container(
                          height: height(context) * 0.05,
                          width: width(context) * 0.4,
                          decoration: BoxDecoration(
                              color: selectedVewAttendance == true
                                  ? Color(0xff3CA4D2)
                                  : ColorConstants.WHITE,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                  color: Color(0xffF0F1FA), width: 2)),
                          margin: EdgeInsets.only(left: 9, top: 3),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Center(
                                child: Text('View Attendance',
                                    style: Styles.textBold(
                                        size: 12,
                                        color: selectedVewAttendance == true
                                            ? ColorConstants.WHITE
                                            : Color(0xff3CA4D2)))),
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  summary() {
    return Container(
        height: MediaQuery.of(context).size.height * 0.5,
        margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        width: MediaQuery.of(context).size.width * 0.9,
        decoration: BoxDecoration(
            color: ColorConstants.ACCENT_COLOR,
            borderRadius: BorderRadius.circular(6)),
        child: Container(
            child: ListView.builder(
                shrinkWrap: true,
                physics: BouncingScrollPhysics(),
                itemCount: 3,
                itemBuilder: (BuildContext context, int index) {
                  // This function is called for each item in the list
                  return Stack(children: [
                    Container(
                        height: MediaQuery.of(context).size.height * 0.18,
                        margin:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                        width: MediaQuery.of(context).size.width * 0.9,
                        decoration: BoxDecoration(
                            color: ColorConstants.GREY,
                            borderRadius: BorderRadius.circular(6)),
                        child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        height: 100,
                                        width: 10,
                                        margin:
                                            EdgeInsets.only(left: 9, top: 3),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          color: Color(0xff3CA4D2),
                                        ),
                                      ),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Container(
                                            margin: EdgeInsets.only(
                                                left: 9, top: 3),
                                            child: Text('Total classes',
                                                style:
                                                    Styles.regular(size: 18)),
                                          ),
                                          SizedBox(height: 20),
                                          Container(
                                              margin: EdgeInsets.only(
                                                  left: 9, top: 3),
                                              child: Text('12',
                                                  style: Styles.bold(size: 36)))
                                        ],
                                      ),
                                    ],
                                  ),
                                ]))),
                    Positioned(
                        right: 20,
                        top: 60,
                        child: Icon(Icons.arrow_forward_ios_outlined))
                  ]);
                })));
  }

  void _handleAnnouncmentData(MyAssignmentState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          break;
        case ApiStatus.SUCCESS:
          assignmentList = box!
              .get("myassignment")
              .map((e) => AssignmentList.fromJson(Map<String, dynamic>.from(e)))
              .cast<AssignmentList>()
              .toList();

          break;
        case ApiStatus.ERROR:
          Log.v("Error..........................");
          Log.v(
              "ErrorAnnoucement..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'my_assignment', parameters: {
            "ERROR": loginState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handleUserAnalyticsData(UserAnalyticsState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          break;
        case ApiStatus.SUCCESS:
          userAnalytics = loginState.response!.data;

          break;
        case ApiStatus.ERROR:
          Log.v("Error..........................");
          Log.v(
              "UserAnalyticsData..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'user_analystics', parameters: {
            "ERROR": loginState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handleLearningSpaceData(LearningSpaceState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          break;
        case ApiStatus.SUCCESS:
          Log.v(loginState.response!.data!.learningSpace);
          break;
        case ApiStatus.ERROR:
          //_isLoading = false;
          Log.v("Error..........................");
          Log.v(
              "LearningSpaceData..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'learning_space', parameters: {
            "ERROR": loginState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleOpenUrlState(ZoomOpenUrlState state) {
    switch (state.apiState) {
      case ApiStatus.LOADING:
        Log.v("Zoom Open Url Loading....................");
        // isJoinClassLoading = true;
        setState(() {});
        break;
      case ApiStatus.SUCCESS:
        Log.v(
            "Zoom Open Url Success.................... now ${state.response?.status}");
        // isJoinClassLoading = false;
        setState(() {});
        if (currentZoomUrl != null) return;

        if (state.response?.status == 0) {
          if (currentOpenUrl != null)
            launchUrl(Uri.parse('$currentOpenUrl'),
                mode: LaunchMode.externalApplication);
          else if (currentZoomUrl != null)
            launchUrl(Uri.parse('$currentZoomUrl'),
                mode: LaunchMode.externalApplication);
          // else
          //   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //     content: Text('${state.response?.error?.first}'),
          //     duration: Duration(milliseconds: 700),
          //   ));
        } else if (state.response?.data?.list?.joinUrl != null)
          launchUrl(Uri.parse('${state.response?.data?.list?.joinUrl}'),
              mode: LaunchMode.externalApplication);
        else if (currentOpenUrl != null)
          launchUrl(Uri.parse('$currentOpenUrl'),
              mode: LaunchMode.externalApplication);
        else
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('class_not_started_yet').tr(),
          ));
        break;

      case ApiStatus.ERROR:
        // isJoinClassLoading = false;
        setState(() {});
        Log.v("Zoom open url Error..........................");
        FirebaseAnalytics.instance.logEvent(name: 'zoom_class', parameters: {
          "ERROR": state.response?.error ?? '',
        });
        break;
      case ApiStatus.INITIAL:
        break;
    }
  }

  void _handleCourseList1Response(CourseCategoryListIDState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isCourseList1Loading = true;

          break;
        case ApiStatus.SUCCESS:
          Log.v("CourseCategoryState....................");
          // courseCategoryList.add(state.response.data.);

          courseList1 = state.response!.data!.programs;

          if (courseList1!.length <= 0) nocourseAssigned = 1;
          isCourseList1Loading = false;

          break;
        case ApiStatus.ERROR:
          Log.v("Error..........................");
          courseList1 = state.response!.data!.programs;

          if (courseList1 == null || courseList1!.length <= 0)
            nocourseAssigned = 1;

          isCourseList1Loading = false;

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _getUserAnalytics() {
    BlocProvider.of<HomeBloc>(context).add(UserAnalyticsEvent());
  }

  void _handlePopularCoursesStateResponse(PopularCoursesState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isJoyCategoryLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("PopularCourses....................");
          Log.v(state.response!.data);
          //liveclassList = state.response.data;
          Log.v("PopularCourses Done ....................$liveclassList");
          isJoyCategoryLoading = false;
          break;
        case ApiStatus.ERROR:
          isJoyCategoryLoading = false;
          Log.v("Error..........................");
          Log.v("ErrorHome..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'popular_course', parameters: {
            "ERROR": loginState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handlePopularFilteredCourses(FilteredPopularCoursesState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          isJoyCategoryLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("LiveClassState Done ....................$liveclassList");
          Log.v(state.response!.data);

          isJoyCategoryLoading = false;
          break;
        case ApiStatus.ERROR:
          isJoyCategoryLoading = false;
          Log.v("Error..........................");
          Log.v("ErrorHome..........................${loginState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'popular_filter_course', parameters: {
            "ERROR": loginState.error ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handleLiveClassResponse(getLiveClassState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");

          isJoyCategoryLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("LiveClassState....................");
          Log.v(state.response!.data!.modules!.liveclass.toString());

          liveclassList = state.response!.data!.modules!.liveclass;

          liveclassList = liveclassList
              ?.where((element) =>
                  element.liveclassStatus?.toLowerCase() == 'upcoming')
              .toList();

          Log.v("LiveClassState Done ....................$liveclassList");

          isJoyCategoryLoading = false;
          break;
        case ApiStatus.ERROR:
          isNotLiveclass = true;
          isJoyCategoryLoading = false;
          Log.v("Error..........................");
          Log.v("ErrorHome..........................${loginState.error}");

          FirebaseAnalytics.instance.logEvent(name: 'live_class', parameters: {
            "ERROR": loginState.error ?? '',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _getLiveClass() {
    BlocProvider.of<HomeBloc>(context).add(getLiveClassEvent());
  }

  void _getLearningSpace() {
    box = Hive.box(DB.CONTENT);
    BlocProvider.of<HomeBloc>(context).add(LearningSpaceEvent(box: box));
  }

  void _getPopularCourses() {
    box = Hive.box(DB.CONTENT);
    BlocProvider.of<HomeBloc>(context).add(PopularCoursesEvent());
  }

  void _getFilteredPopularCourses() {
    box = Hive.box(DB.CONTENT);
    BlocProvider.of<HomeBloc>(context).add(FilteredPopularCoursesEvent());
  }

  Widget _customAppBar() {
    return RoundedAppBar(
        appBarHeight: height(context) * 0.1,
        child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 12),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => NewPortfolioPage()))
                              .then((value) {
                            if (value != null)
                              menuProvider?.updateCurrentIndex(value);
                          });
                        },
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(200),
                          child: SizedBox(
                              width: 50,
                              child: CachedNetworkImage(
                                imageUrl:
                                    '${Preference.getString(Preference.PROFILE_IMAGE)}',
                                height: 50,
                                width: 50,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => SvgPicture.asset(
                                  'assets/images/default_user.svg',
                                  width: 50,
                                ),
                                errorWidget: (context, url, error) =>
                                    SvgPicture.asset(
                                  'assets/images/default_user.svg',
                                  width: 50,
                                ),
                              )),
                        ),
                      ),
                      Spacer(),
                      InkWell(
                        onTap: () {
                          _scaffoldKey.currentState?.openEndDrawer();
                        },
                        child: SvgPicture.asset(
                            'assets/images/hamburger_menu.svg'),
                      )
                    ],
                  ),
                ])));
  }

  Widget _getTodayClass() {
    List<String> months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return Container(
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(color: ColorConstants.GREY),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Padding(
              padding: EdgeInsets.only(left: 10, top: 10),
              child: Text('upcoming_class', style: Styles.semibold(size: 18))
                  .tr()),
          ListView.builder(
            itemBuilder: (BuildContext context, int index) {
              return liveclassList!.length > 0
                  ? Container(
                      padding: EdgeInsets.all(10),
                      margin: EdgeInsets.symmetric(vertical: 10),
                      decoration: BoxDecoration(
                          color: ColorConstants.WHITE,
                          border:
                              Border.all(color: Colors.grey[350]!, width: 1),
                          borderRadius: BorderRadius.circular(10)),
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            liveclassList![index]
                                        .liveclassStatus!
                                        .toLowerCase() ==
                                    'live'
                                ? Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      liveclassList![index]
                                                  .contentType!
                                                  .toLowerCase() !=
                                              'offlineclass'
                                          ? SvgPicture.asset(
                                              'assets/images/live_icon.svg',
                                              width: 25,
                                              height: 25,
                                              allowDrawingOutsideViewBox: true,
                                            )
                                          : SvgPicture.asset(
                                              'assets/images/offline_live.svg',
                                              allowDrawingOutsideViewBox: true,
                                            ),
                                      SizedBox(width: 5),
                                      Text(
                                              liveclassList![index]
                                                          .contentType!
                                                          .toLowerCase() ==
                                                      'offlineclass'
                                                  ? 'ongoing'
                                                  : 'Live_now',
                                              style: Styles.regular(
                                                  size: 12,
                                                  color: ColorConstants()
                                                          .primaryColor() ??
                                                      ColorConstants()
                                                          .primaryColorAlways()))
                                          .tr(),
                                      Expanded(child: SizedBox()),
                                      Container(
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            color: ColorConstants.BG_GREY),
                                        padding: EdgeInsets.symmetric(
                                            vertical: 8, horizontal: 18),
                                        child: Text(
                                                liveclassList![index]
                                                            .contentType!
                                                            .toLowerCase() ==
                                                        'otherclass'
                                                    ? 'weblink'
                                                    : liveclassList![index]
                                                                .contentType!
                                                                .toLowerCase() ==
                                                            'teamsclass'
                                                        ? 'teams'
                                                        : liveclassList![index]
                                                                        .contentType!
                                                                        .toLowerCase() ==
                                                                    'liveclass' ||
                                                                liveclassList![
                                                                            index]
                                                                        .contentType!
                                                                        .toLowerCase() ==
                                                                    'zoomclass'
                                                            ? 'live'
                                                            : 'classroom',
                                                style: Styles.regular(
                                                    size: 10,
                                                    color:
                                                        ColorConstants.BLACK))
                                            .tr(),
                                      ),
                                    ],
                                  )
                                : liveclassList![index]
                                            .liveclassStatus!
                                            .toLowerCase() ==
                                        'upcoming'
                                    ? Row(children: [
                                        SvgPicture.asset(
                                          'assets/images/upcoming_live.svg',
                                          allowDrawingOutsideViewBox: true,
                                        ),
                                        SizedBox(width: 5),
                                        CallOnceWidget(
                                          onCallOnce: () {
                                            print('page cllaed oned');
                                          },
                                          child: StrToTime(
                                            appendString:
                                                Utility().isRTL(context)
                                                    ? ''
                                                    : ' - ',
                                            dateFormat: 'hh:mm a',
                                            time:
                                                '${liveclassList![index].startTime}',
                                            textStyle: Styles.regular(size: 14),
                                          ),
                                        ),
                                        CallOnceWidget(
                                          onCallOnce: () {
                                            print('page cllaed oned');
                                          },
                                          child: StrToTime(
                                            appendString:
                                                Utility().isRTL(context)
                                                    ? ' - '
                                                    : '',
                                            dateFormat: 'hh:mm a',
                                            time:
                                                '${liveclassList![index].endTime}',
                                            textStyle: Styles.regular(size: 14),
                                          ),
                                        ),
                                        Text(
                                          ' ${Utility().isRTL(context) ? '' : '|'} ${DateFormat('d').format(DateTime.fromMillisecondsSinceEpoch(liveclassList![index].fromDate! * 1000))} ${months[int.parse(DateFormat('M').format(DateTime.fromMillisecondsSinceEpoch(liveclassList![index].fromDate! * 1000))) - 1]} ${Utility().isRTL(context) ? '|' : ''}',
                                          style: Styles.regular(size: 14),
                                          textDirection: ui.TextDirection.ltr,
                                        ),
                                        // Text(
                                        //   '${liveclassList![index].startTime} - ${liveclassList![index].endTime} |${DateFormat('d').format(DateTime.fromMillisecondsSinceEpoch(liveclassList![index].fromDate! * 1000))} ${months[int.parse(DateFormat('M').format(DateTime.fromMillisecondsSinceEpoch(liveclassList![index].fromDate! * 1000))) - 1]}',
                                        //   style: Styles.regular(size: 14),
                                        //   textDirection: ui.TextDirection.ltr,
                                        // ),
                                        Expanded(child: SizedBox()),
                                        Container(
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              color: ColorConstants.BG_GREY),
                                          padding: EdgeInsets.symmetric(
                                              vertical: 8, horizontal: 18),
                                          child: Text(
                                                  liveclassList![index]
                                                              .contentType!
                                                              .toLowerCase() ==
                                                          'otherclass'
                                                      ? 'weblink'
                                                      : liveclassList![index]
                                                                  .contentType!
                                                                  .toLowerCase() ==
                                                              'teamsclass'
                                                          ? 'teams'
                                                          : liveclassList![
                                                                          index]
                                                                      .contentType!
                                                                      .toLowerCase() ==
                                                                  'offlineclass'
                                                              ? 'classroom'
                                                              : 'live',
                                                  style: Styles.regular(
                                                      size: 10,
                                                      color:
                                                          ColorConstants.BLACK))
                                              .tr(),
                                        ),
                                      ])
                                    : SizedBox(),
                            SizedBox(height: 10),
                            Text('${liveclassList![index].name}',
                                style: Styles.semibold(size: 16)),
                            SizedBox(height: 9),
                            Text(
                              '${liveclassList![index].description}',
                              style: Styles.regular(size: 14),
                            ),
                            SizedBox(height: 15),
                            Row(
                              children: [
                                liveclassList![index].trainerName != null &&
                                        liveclassList![index].trainerName != ''
                                    ? Text('${tr('by')} ${Utility().decrypted128('${liveclassList![index].trainerName}')} ',
                                            style: Styles.regular(size: 12))
                                        .tr()
                                    : Text(''),
                                Expanded(child: SizedBox()),
                                liveclassList![index]
                                                .liveclassStatus!
                                                .toLowerCase() ==
                                            'live' ||
                                        (currentIndiaTime!
                                                        .add(Duration(
                                                            minutes: 15))
                                                        .millisecondsSinceEpoch ~/
                                                    1000 >=
                                                liveclassList![index]
                                                    .startTimeTs! &&
                                            currentIndiaTime!
                                                        .millisecondsSinceEpoch ~/
                                                    1000 <
                                                liveclassList![index]
                                                    .endTimeTs!)
                                    ? InkWell(
                                        onTap: () {
                                          if (liveclassList![index]
                                                  .contentType!
                                                  .toLowerCase() ==
                                              "offlineclass") return;

                                          if (liveclassList![index]
                                                      .contentType!
                                                      .toLowerCase() ==
                                                  "zoomclass" ||
                                              liveclassList![index]
                                                      .contentType!
                                                      .toLowerCase() ==
                                                  'teamsclass' ||
                                              liveclassList![index]
                                                      .contentType!
                                                      .toLowerCase() ==
                                                  'otherclass') {
                                            setState(() {
                                              currentZoomUrl =
                                                  liveclassList![index].zoomUrl;
                                              currentOpenUrl =
                                                  liveclassList![index].openUrl;
                                            });

                                            if (currentZoomUrl != null) {
                                              BlocProvider.of<HomeBloc>(context)
                                                  .add(ZoomOpenUrlEvent(
                                                contentId:
                                                    liveclassList![index].id,
                                              ));
                                              launchUrl(
                                                  Uri.parse('$currentZoomUrl'),
                                                  mode: LaunchMode
                                                      .externalApplication);
                                            } else {
                                              BlocProvider.of<HomeBloc>(context)
                                                  .add(ZoomOpenUrlEvent(
                                                      contentId:
                                                          liveclassList![index]
                                                              .id));
                                            }
                                          } else {
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(SnackBar(
                                              content: Text("coming_soon").tr(),
                                            ));
                                          }
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                                colors:
                                                    liveclassList![index]
                                                                    .contentType!
                                                                    .toLowerCase() ==
                                                                "liveclass" ||
                                                            liveclassList![
                                                                        index]
                                                                    .contentType!
                                                                    .toLowerCase() ==
                                                                "zoomclass" ||
                                                            liveclassList![
                                                                        index]
                                                                    .contentType!
                                                                    .toLowerCase() ==
                                                                'teamsclass' ||
                                                            liveclassList![
                                                                        index]
                                                                    .contentType!
                                                                    .toLowerCase() ==
                                                                'otherclass'
                                                        ? [
                                                            ColorConstants()
                                                                .gradientLeft(),
                                                            ColorConstants()
                                                                .gradientRight(),
                                                          ]
                                                        : [
                                                            liveclassList![index]
                                                                        .contentType!
                                                                        .toLowerCase() ==
                                                                    "offlineclass"
                                                                ? ColorConstants
                                                                    .WHITE
                                                                : ColorConstants
                                                                    .GREY_3,
                                                            liveclassList![index]
                                                                        .contentType!
                                                                        .toLowerCase() ==
                                                                    "offlineclass"
                                                                ? ColorConstants
                                                                    .WHITE
                                                                : ColorConstants
                                                                    .GREY_3,
                                                          ]),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Padding(
                                              child: Text(
                                                      liveclassList![index].contentType!.toLowerCase() == "liveclass" ||
                                                              liveclassList![index]
                                                                      .contentType!
                                                                      .toLowerCase() ==
                                                                  "zoomclass" ||
                                                              liveclassList![index]
                                                                      .contentType!
                                                                      .toLowerCase() ==
                                                                  'teamsclass' ||
                                                              liveclassList![index]
                                                                      .contentType!
                                                                      .toLowerCase() ==
                                                                  'otherclass'
                                                          ? "join_now"
                                                          : 'in_progress',
                                                      style: Styles.regular(
                                                          size: 12,
                                                          color: liveclassList![index]
                                                                      .contentType!
                                                                      .toLowerCase() ==
                                                                  "offlineclass"
                                                              ? ColorConstants
                                                                  .BLACK
                                                              : ColorConstants.WHITE))
                                                  .tr(),
                                              padding: EdgeInsets.symmetric(horizontal: liveclassList![index].contentType!.toLowerCase() == "offlineclass" ? 4 : 18, vertical: 8)),
                                        ))
                                    : Text('upcoming',
                                            style: Styles.regular(size: 12))
                                        .tr(),
                                Visibility(
                                    child: ElevatedButton(
                                        style: ButtonStyle(
                                            foregroundColor:
                                                WidgetStateProperty.all<Color>(
                                                    Colors.white),
                                            backgroundColor:
                                                WidgetStateProperty.all<Color>(
                                                    ColorConstants().primaryColor() ??
                                                        ColorConstants()
                                                            .primaryColorAlways()),
                                            shape: WidgetStateProperty.all<
                                                    RoundedRectangleBorder>(
                                                RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(10),
                                                    side: BorderSide(color: ColorConstants().primaryColor() ?? ColorConstants().primaryColorAlways())))),
                                        onPressed: () {
                                          //launch(liveclassList[index].url);
                                        },
                                        child: Padding(
                                            child: Text(
                                              'concluded',
                                              style: Styles.regular(size: 12),
                                            ).tr(),
                                            padding: EdgeInsets.all(10))),
                                    visible: liveclassList![index].liveclassStatus!.toLowerCase() == 'completed')
                              ],
                            )
                          ]))
                  : Container(child: Text("no_active_class").tr());
            },
            itemCount: liveclassList?.length != 0
                ? liveclassList!.length >= 2
                    ? 2
                    : liveclassList?.length
                : 0,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
          ),
        ]));
  }

  Widget _getDashboard(context) {
    return Container(
        padding: EdgeInsets.only(left: 10, bottom: 10, top: 10, right: 10),
        decoration: BoxDecoration(color: ColorConstants.GREY),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
                padding: EdgeInsets.only(left: 10),
                child: Text('resume_learning', style: Styles.semibold(size: 18))
                    .tr()),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                dashboardClassCard(),
                dashboardAssignmentCard(),
              ],
            ),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                dashboardQuizCard(),
                dashboardCoursesCard(),
              ],
            )
          ],
        ));
  }

  Widget dashboardQuizCard() {
    int index;
    index = userAnalytics!.length > 0
        ? userAnalytics!.indexWhere((element) => element.order == 3)
        : 0;
    return InkWell(
        onTap: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => MyAssessmentPage(isViewAll: true)));
        },
        child: Container(
          width: MediaQuery.of(context).size.width * 0.45,
          height: MediaQuery.of(context).size.width * 0.45,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(10)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                  height: MediaQuery.of(context).size.height * 0.09,
                  child: CircleAvatar(
                      radius: 60,
                      backgroundColor: Color(0xFFFFF6DE),
                      child: CircleAvatar(
                          backgroundColor: Color(0xFFFFF6DE),
                          radius: 30,
                          child: SvgPicture.asset(
                            'assets/images/Quiz.svg',
                            allowDrawingOutsideViewBox: true,
                          )))),
              SizedBox(
                height: 15,
              ),
              Text('my_quiz', style: Styles.semibold(size: 16)).tr(),
              SizedBox(height: 4),
            ],
          ),
        ));
  }

  Widget dashboardCoursesCard() {
    int index;
    index = userAnalytics!.length > 0
        ? userAnalytics!.indexWhere((element) => element.order == 4)
        : 0;
    return InkWell(
        onTap: () {
          Navigator.push(
              context, MaterialPageRoute(builder: (context) => MyCourses()));
        },
        child: Container(
          width: MediaQuery.of(context).size.width * 0.45,
          height: MediaQuery.of(context).size.width * 0.45,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(10)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                  height: MediaQuery.of(context).size.height * 0.09,
                  child: CircleAvatar(
                      radius: 60,
                      backgroundColor: Color(0xFFFFF6DE),
                      child: CircleAvatar(
                          backgroundColor: Color(0xFFFFF6DE),
                          radius: 30,
                          child: SvgPicture.asset(
                            'assets/images/Courses.svg',
                            allowDrawingOutsideViewBox: true,
                          )))),
              SizedBox(
                height: 15,
              ),
              Text('my_course', style: Styles.semibold(size: 16)).tr(),
              SizedBox(height: 4),
            ],
          ),
        ));
  }

  Widget dashboardAssignmentCard() {
    int index;
    index = userAnalytics!.length > 0
        ? userAnalytics!.indexWhere((element) => element.order == 2)
        : 0;
    return InkWell(
        onTap: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => MyAssignmentPage(isViewAll: true)));
        },
        child: Container(
          width: MediaQuery.of(context).size.width * 0.45,
          height: MediaQuery.of(context).size.width * 0.45,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(10)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                  height: MediaQuery.of(context).size.height * 0.09,
                  child: CircleAvatar(
                      radius: 60,
                      backgroundColor: Color(0xFFFFF6DE),
                      child: SvgPicture.asset(
                        'assets/images/Assignment.svg',
                        allowDrawingOutsideViewBox: true,
                      ))),
              SizedBox(
                height: 15,
              ),
              Text('my_assignment',
                      style: Styles.semibold(size: 16),
                      textAlign: TextAlign.center)
                  .tr(),
              SizedBox(height: 4),
            ],
          ),
        ));
  }

  Widget dashboardClassCard() {
    int index;
    index = userAnalytics!.length > 0
        ? userAnalytics!.indexWhere((element) => element.order == 1)
        : 0;

    return InkWell(
        onTap: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (BuildContext context) => MyClasses()));
        },
        child: Container(
          width: MediaQuery.of(context).size.width * 0.45,
          height: MediaQuery.of(context).size.width * 0.45,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(11)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                  height: MediaQuery.of(context).size.height * 0.09,
                  child: CircleAvatar(
                      radius: 60,
                      backgroundColor: Color(0xFFFFF6DE),
                      child: CircleAvatar(
                          backgroundColor: Color(0xFFFFF6DE),
                          radius: 30,
                          child: SvgPicture.asset(
                            'assets/images/MyClasses.svg',
                            allowDrawingOutsideViewBox: true,
                          )))),
              SizedBox(
                height: 15,
              ),
              Text('my_classes', style: Styles.semibold(size: 16)).tr(),
              SizedBox(height: 4),
            ],
          ),
        ));
  }

  Widget _getOtherLearnerTopics(context) {
    return box != null
        ? ValueListenableBuilder(
            valueListenable: box!.listenable(),
            builder: (bc, Box box, child) {
              if (box.get("recommended") == null) {
                return Column(
                  children: [
                    Shimmer.fromColors(
                      baseColor: Color(0xffe6e4e6),
                      highlightColor: Color(0xffeaf0f3),
                      child: Container(
                        height: MediaQuery.of(context).size.height * 0.02,
                        margin:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 20),
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(6)),
                      ),
                    ),
                    ListView.builder(
                        itemCount: 4,
                        shrinkWrap: true,
                        itemBuilder: (context, index) => Shimmer.fromColors(
                              baseColor: Color(0xffe6e4e6),
                              highlightColor: Color(0xffeaf0f3),
                              child: Container(
                                height:
                                    MediaQuery.of(context).size.height * 0.12,
                                margin: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 6),
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(6)),
                              ),
                            )),
                  ],
                );
              } else if (box.get("recommended").isEmpty) {
                return Container();
              }

              otherLearners = box
                  .get("recommended")
                  .map((e) =>
                      OtherLearners.fromJson(Map<String, dynamic>.from(e)))
                  .cast<OtherLearners>()
                  .toList();

              return Container(
                  padding: EdgeInsets.only(left: 2, right: 2),
                  decoration: BoxDecoration(color: ColorConstants.GREY),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: (MediaQuery.of(context).size.height *
                              (Utility().isRTL(context) ? 0.48 : 0.46)),
                          child: ListView.builder(
                            itemBuilder: (BuildContext context, int index) {
                              return otherLearners!.length > 0
                                  ? InkWell(
                                      onTap: () {
                                        FirebaseAnalytics.instance.logEvent(
                                            name: 'learn_course_horizontal',
                                            parameters: {
                                              "course_name":
                                                  otherLearners![index].name ??
                                                      '',
                                            });

                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) => CoursesDetailsPage(
                                                  imgUrl: otherLearners![index]
                                                      .image,
                                                  indexc: index,
                                                  tagName: 'TagOther',
                                                  name: otherLearners![index]
                                                      .name,
                                                  description: otherLearners![
                                                          index]
                                                      .description,
                                                  regularPrice:
                                                      otherLearners![index]
                                                          .regularPrice,
                                                  salePrice:
                                                      otherLearners![index]
                                                          .salePrice,
                                                  trainer: otherLearners![index]
                                                      .trainer,
                                                  enrolmentCount:
                                                      otherLearners![index]
                                                          .enrolmentCount,
                                                  type: otherLearners![index]
                                                      .subscriptionType,
                                                  id: otherLearners![index].id,
                                                  shortCode:
                                                      otherLearners![index]
                                                          .shortCode,
                                                  isSubscribed:
                                                      otherLearners![index]
                                                          .isSubscribed)),
                                        ).then((isSuccess) {
                                          if (isSuccess == true) {
                                            _getFilteredPopularCourses();
                                          }
                                        });
                                      },
                                      child: _getCourseTemplate(
                                          context,
                                          otherLearners![index],
                                          index,
                                          'TagOther'))
                                  : Container(
                                      child: Text("no_active_class").tr());
                            },
                            itemCount: min(otherLearners?.length ?? 0, 2),
                            scrollDirection: Axis.horizontal,
                          ),
                        ),
                        ListView.builder(
                          itemBuilder: (BuildContext context, int index) {
                            index = index + 2;
                            return otherLearners!.length > 0
                                ? InkWell(
                                    onTap: () {
                                      FirebaseAnalytics.instance.logEvent(
                                          name: 'learn_course_vertical',
                                          parameters: {
                                            "course_name":
                                                otherLearners![index].name ??
                                                    '',
                                          });
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) => CoursesDetailsPage(
                                                imgUrl:
                                                    otherLearners![index].image,
                                                indexc: index,
                                                tagName: 'TagOther',
                                                name:
                                                    otherLearners![index].name,
                                                description:
                                                    otherLearners![index]
                                                        .description,
                                                regularPrice:
                                                    otherLearners![index]
                                                        .regularPrice,
                                                salePrice: otherLearners![index]
                                                    .salePrice,
                                                trainer: otherLearners![index]
                                                    .trainer,
                                                enrolmentCount:
                                                    otherLearners![index]
                                                        .enrolmentCount,
                                                type: otherLearners![index]
                                                    .subscriptionType,
                                                id: otherLearners![index].id,
                                                shortCode: otherLearners![index]
                                                    .shortCode,
                                                isSubscribed:
                                                    otherLearners![index]
                                                        .isSubscribed)),
                                      ).then((isSuccess) {
                                        if (isSuccess == true) {
                                          _getFilteredPopularCourses();
                                        }
                                      });
                                    },
                                    child: _getCourseVerticalTemplate(
                                        context,
                                        otherLearners![index],
                                        index,
                                        'TagOther'))
                                : Container(
                                    child: Text("no_active_class").tr());
                          },
                          itemCount: otherLearners!.length - 2 > 0
                              ? otherLearners!.length - 2
                              : 0,
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                        ),
                      ]));
            },
          )
        : Shimmer.fromColors(
            baseColor: Color(0xffe6e4e6),
            highlightColor: Color(0xffeaf0f3),
            child: Container(
              height: MediaQuery.of(context).size.height * 0.07,
              margin: EdgeInsets.symmetric(horizontal: 10, vertical: 20),
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(6)),
            ),
          );
  }

  sectionLoader() {
    return Shimmer.fromColors(
      baseColor: Color(0xffe6e4e6),
      highlightColor: Color(0xffeaf0f3),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.07,
        margin: EdgeInsets.symmetric(horizontal: 10, vertical: 20),
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(6)),
      ),
    );
  }

  // Widget _getRecommendedCourses(context) {
  //   var title = tr('explore_more_courses');

  //   return ValueListenableBuilder(
  //     valueListenable: box!.listenable(),
  //     builder: (bc, Box box, child) {
  //       if (box.get("recommended") == null) {
  //         // return Container();
  //         return Column(
  //           children: [
  //             Shimmer.fromColors(
  //               baseColor: Color(0xffe6e4e6),
  //               highlightColor: Color(0xffeaf0f3),
  //               child: Container(
  //                 height: MediaQuery.of(context).size.height * 0.02,
  //                 margin: EdgeInsets.symmetric(horizontal: 10, vertical: 20),
  //                 width: MediaQuery.of(context).size.width,
  //                 decoration: BoxDecoration(
  //                     color: Colors.white,
  //                     borderRadius: BorderRadius.circular(6)),
  //               ),
  //             ),
  //             ListView.builder(
  //                 itemCount: 4,
  //                 shrinkWrap: true,
  //                 itemBuilder: (context, index) => Shimmer.fromColors(
  //                       baseColor: Color(0xffe6e4e6),
  //                       highlightColor: Color(0xffeaf0f3),
  //                       child: Container(
  //                         height: MediaQuery.of(context).size.height * 0.12,
  //                         margin:
  //                             EdgeInsets.symmetric(horizontal: 10, vertical: 6),
  //                         width: MediaQuery.of(context).size.width,
  //                         decoration: BoxDecoration(
  //                             color: Colors.white,
  //                             borderRadius: BorderRadius.circular(6)),
  //                       ),
  //                     )),
  //           ],
  //         );
  //       } else if (box.get("recommended").isEmpty) {
  //         return Container();
  //       }

  //       recommendedcourses = box
  //           .get("recommended")
  //           .map((e) => Recommended.fromJson(Map<String, dynamic>.from(e)))
  //           .cast<Recommended>()
  //           .toList();

  //       // recommendedcourse.sor
  //       if (APK_DETAILS['package_name'] == 'com.learn_build')
  //         recommendedcourses
  //             ?.sort((a, b) => a.categoryName!.compareTo(b.categoryName!));
  //       //var list = _getFilterList();
  //       return Container(
  //           padding: EdgeInsets.all(10),
  //           decoration: BoxDecoration(color: ColorConstants.GREY),
  //           child:
  //               Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
  //             Padding(
  //                 padding: EdgeInsets.only(
  //                   left: 10,
  //                 ),
  //                 child: Text(title, style: Styles.DMSansbold(size: 18))),
  //             Container(
  //               height: height(context) * 0.36,
  //               child: ListView.builder(
  //                 scrollDirection: Axis.horizontal,
  //                 itemBuilder: (BuildContext context, int index) {
  //                   return Container(
  //                     width: min(width(context), 480) * 0.8,
  //                     child: Column(
  //                       crossAxisAlignment: CrossAxisAlignment.start,
  //                       children: [
  //                         if (APK_DETAILS['package_name'] ==
  //                             'com.learn_build') ...[
  //                           if (index == 0)
  //                             Container(
  //                                 margin: EdgeInsets.only(left: 9, top: 6),
  //                                 child: Text(
  //                                     '${recommendedcourses![index].categoryName}',
  //                                     maxLines: 2,
  //                                     overflow: TextOverflow.ellipsis,
  //                                     softWrap: false,
  //                                     style: Styles.semibold(size: 16))),
  //                           if (index > 0 &&
  //                               recommendedcourses![index].categoryName !=
  //                                   recommendedcourses![index - 1].categoryName)
  //                             Container(
  //                                 margin: EdgeInsets.only(left: 9, top: 6),
  //                                 child: Text(
  //                                     '${recommendedcourses![index].categoryName}',
  //                                     maxLines: 2,
  //                                     overflow: TextOverflow.ellipsis,
  //                                     softWrap: false,
  //                                     style: Styles.semibold(size: 16))),
  //                         ],
  //                         InkWell(
  //                             onTap: () {
  //                               Navigator.push(
  //                                 context,
  //                                 MaterialPageRoute(
  //                                     builder: (context) => CoursesDetailsPage(
  //                                         imgUrl:
  //                                             recommendedcourses![index].image,
  //                                         indexc: index,
  //                                         tagName: 'TagReco',
  //                                         name: recommendedcourses![index].name,
  //                                         description:
  //                                             recommendedcourses![index]
  //                                                     .description ??
  //                                                 '',
  //                                         regularPrice:
  //                                             recommendedcourses![index]
  //                                                 .regularPrice,
  //                                         salePrice: recommendedcourses![index]
  //                                             .salePrice,
  //                                         trainer: recommendedcourses![index]
  //                                             .trainer,
  //                                         enrolmentCount:
  //                                             recommendedcourses![index]
  //                                                 .enrolmentCount,
  //                                         type: recommendedcourses![index]
  //                                             .subscriptionType,
  //                                         id: recommendedcourses![index].id,
  //                                         shortCode: recommendedcourses![index]
  //                                             .shortCode,
  //                                         isSubscribed:
  //                                             recommendedcourses![index]
  //                                                 .isSubscribed)),
  //                               ).then((isSuccess) {
  //                                 if (isSuccess == true) {
  //                                   _getFilteredPopularCourses();
  //                                 }
  //                               });
  //                             },
  //                             child: _getCourseTemplate(
  //                                 context,
  //                                 recommendedcourses![index],
  //                                 index,
  //                                 'TagReco')),
  //                       ],
  //                     ),
  //                   );
  //                 },
  //                 itemCount: min(4, recommendedcourses!.length),
  //                 shrinkWrap: true,
  //               ),
  //             ),
  //             ListView.builder(
  //               itemBuilder: (BuildContext context, int index) {
  //                 index += 4;
  //                 return Column(
  //                   crossAxisAlignment: CrossAxisAlignment.start,
  //                   children: [
  //                     if (APK_DETAILS['package_name'] == 'com.learn_build') ...[
  //                       if (index == 0)
  //                         Container(
  //                             margin: EdgeInsets.only(left: 9, top: 6),
  //                             child: Text(
  //                                 '${recommendedcourses![index].categoryName}',
  //                                 maxLines: 2,
  //                                 overflow: TextOverflow.ellipsis,
  //                                 softWrap: false,
  //                                 style: Styles.semibold(size: 16))),
  //                       if (index > 0 &&
  //                           recommendedcourses![index].categoryName !=
  //                               recommendedcourses![index - 1].categoryName)
  //                         Container(
  //                             margin: EdgeInsets.only(left: 9, top: 6),
  //                             child: Text(
  //                                 '${recommendedcourses![index].categoryName}',
  //                                 maxLines: 2,
  //                                 overflow: TextOverflow.ellipsis,
  //                                 softWrap: false,
  //                                 style: Styles.semibold(size: 16))),
  //                     ],
  //                     InkWell(
  //                         onTap: () {
  //                           Navigator.push(
  //                             context,
  //                             MaterialPageRoute(
  //                                 builder: (context) => CoursesDetailsPage(
  //                                     imgUrl: recommendedcourses![index].image,
  //                                     indexc: index,
  //                                     tagName: 'TagReco',
  //                                     name: recommendedcourses![index].name,
  //                                     description: recommendedcourses![index]
  //                                             .description ??
  //                                         '',
  //                                     regularPrice: recommendedcourses![index]
  //                                         .regularPrice,
  //                                     salePrice:
  //                                         recommendedcourses![index].salePrice,
  //                                     trainer:
  //                                         recommendedcourses![index].trainer,
  //                                     enrolmentCount: recommendedcourses![index]
  //                                         .enrolmentCount,
  //                                     type: recommendedcourses![index]
  //                                         .subscriptionType,
  //                                     id: recommendedcourses![index].id,
  //                                     shortCode:
  //                                         recommendedcourses![index].shortCode,
  //                                     isSubscribed: recommendedcourses![index]
  //                                         .isSubscribed)),
  //                           ).then((isSuccess) {
  //                             if (isSuccess == true) {
  //                               _getFilteredPopularCourses();
  //                             }
  //                           });
  //                         },
  //                         child: _getCourseVerticalTemplate(context,
  //                             recommendedcourses![index], index, 'TagReco')),
  //                   ],
  //                 );
  //               },
  //               itemCount: max(0, recommendedcourses!.length - 4),
  //               shrinkWrap: true,
  //               physics: NeverScrollableScrollPhysics(),
  //             ),
  //           ]));
  //     },
  //   );
  // }

  Widget _getCourseVerticalTemplate(
      context, yourCourses, int index, String tag) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.14,
      margin: EdgeInsets.all(7),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: ColorConstants.WHITE,
      ),
      child: Row(children: [
        Padding(
            child: SizedBox(
              width: MediaQuery.of(context).size.height * 0.1,
              height: MediaQuery.of(context).size.height * 0.11,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(5),
                child: Hero(
                  tag: tag + "$index",
                  child: Image.network(
                    '${yourCourses.image}',
                    errorBuilder: (context, error, stackTrace) {
                      return SvgPicture.asset(
                        'assets/images/gscore_postnow_bg.svg',
                      );
                    },
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            padding: EdgeInsets.all(10)),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(left: 4.0, top: 9.0, bottom: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('${yourCourses.name}',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    softWrap: false,
                    style: Styles.semibold(size: 16)),
                if (APK_DETAILS['package_name'] == 'com.learn_build')
                  Text('${yourCourses.approvalStatus ?? ''}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      softWrap: false,
                      style: Styles.semibold(
                          size: 12, color: ColorConstants.YELLOW)),
                Row(
                  children: [
                    Text('${yourCourses.enrolmentCount} ${tr('enrollments')}',
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        softWrap: false,
                        style: Styles.regular(size: 14)),
                    Spacer(),
                    if (yourCourses.regularPrice != yourCourses.salePrice)
                      Text('₹${yourCourses.regularPrice}',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          softWrap: false,
                          style: TextStyle(
                            fontSize: 14,
                            decoration: TextDecoration.lineThrough,
                          )),
                    if (yourCourses.salePrice != null)
                      Text('₹${yourCourses.salePrice}',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          softWrap: false,
                          style: Styles.bold(
                              size: 18, color: ColorConstants.GREEN)),
                  ],
                )
              ],
            ),
          ),
        )
      ]),
    );
  }

  Widget _getCourseTemplate(context, yourCourses, int index, String tag) {
    return Container(
      height: (MediaQuery.of(context).size.height *
          (Utility().isRTL(context) ? 0.48 : 0.46)),
      margin: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
      width: min(width(context), 480) * 0.70,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: ColorConstants.WHITE,
      ),
      child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.width * 0.7,
              width: MediaQuery.of(context).size.width,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(5),
                child: Hero(
                  tag: tag + "$index",
                  child: Image.network(
                    '${yourCourses.image}',
                    errorBuilder: (context, error, stackTrace) {
                      return SvgPicture.asset(
                        'assets/images/gscore_postnow_bg.svg',
                      );
                    },
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: MediaQuery.of(context).size.width * 0.82,
                    child: Padding(
                      padding: const EdgeInsets.only(top: 12.0),
                      child: Text('${yourCourses.name}',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          softWrap: false,
                          style: Styles.bold(size: 14)),
                    ),
                  ),
                  SizedBox(
                    height: 4,
                  ),
                  if (APK_DETAILS['package_name'] == 'com.learn_build')
                    Text('${yourCourses.approvalStatus ?? ''}',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        softWrap: false,
                        style: Styles.semibold(
                            size: 12, color: ColorConstants.YELLOW)),
                  Text('${yourCourses.enrolmentCount} ${tr('enrollments')}',
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      softWrap: false,
                      style: Styles.regular(size: 12)),
                  SizedBox(
                    height: 8,
                  ),
                  SizedBox(
                    width: min(width(context), 480) * 0.5,
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          'assets/images/clock_icon.svg',
                          colorFilter: ColorFilter.mode(
                              ColorConstants.GREY_4, BlendMode.srcIn),
                          height: 18,
                        ),
                        SizedBox(
                          width: 8,
                        ),
                        Text(
                          '${yourCourses.duration}',
                          style: TextStyle(fontSize: 10),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ]),
    );
  }

  _subscribeRequest(type, id) {
    if (type == "paid") {
      AlertsWidget.showCustomDialog(
          context: context,
          title: "Contact Administrator to get access to this program!!",
          text: "",
          icon: 'assets/images/circle_alert_fill.svg',
          showCancel: false,
          oKText: "Ok",
          onOkClick: () async {});
    }

    if (type == "approve") {
      BlocProvider.of<HomeBloc>(context).add(UserProgramSubscribeEvent(
          subrReq: UserProgramSubscribeReq(programId: id)));

      AlertsWidget.showCustomDialog(
          context: context,
          title: tr('approval_request'),
          text: "You will be assigned to this course soon!!",
          icon: 'assets/images/circle_alert_fill.svg',
          showCancel: false,
          oKText: '${tr('ok')}',
          onOkClick: () async {});
    }

    if (type == "open") {
      BlocProvider.of<HomeBloc>(context).add(UserProgramSubscribeEvent(
          subrReq: UserProgramSubscribeReq(programId: id)));

      AlertsWidget.showCustomDialog(
          context: context,
          title: "Subscribed Sucessfully!!",
          text: "",
          icon: 'assets/images/circle_alert_fill.svg',
          showCancel: false,
          onOkClick: () async {});
    }
  }
}
