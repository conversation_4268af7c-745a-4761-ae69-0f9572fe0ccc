import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:flutter_svg/svg.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/new_portfolio_response.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/add_portfolio.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/video_screen.dart';
import 'package:page_transition/page_transition.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:video_player/video_player.dart';

class PortfolioDetail extends StatefulWidget {
  final Portfolio portfolio;
  final String? baseUrl;
  final userId;
  const PortfolioDetail(
      {Key? key, required this.portfolio, this.baseUrl, this.userId})
      : super(key: key);

  @override
  State<PortfolioDetail> createState() => _PortfolioDetailState();
}

class _PortfolioDetailState extends State<PortfolioDetail> {
  String? urlType;
  FlickManager? flickManager;
  bool deletePortfolio = false;
  late VideoPlayerController _controller;
  late Future<void> _initializeVideoPlayerFuture;
  @override
  void initState() {
    check();

    super.initState();
  }

  void check() {
    String url = widget.portfolio.portfolioFile;
    String type = url.split('/').last.split('.').last;
    if (type == 'pdf')
      urlType = 'pdf';
    else if (type == 'jpg' ||
        type == 'jpeg' ||
        type == 'png' ||
        type == 'gif') {
      urlType = 'img';
    } else if (type == 'mp4' || type == 'mov') {
      flickManager = FlickManager(
        videoPlayerController: VideoPlayerController.network(
            "https://flutter.github.io/assets-for-api-docs/assets/videos/butterfly.mp4"),
      );
      _controller = VideoPlayerController.network(
        'https://flutter.github.io/assets-for-api-docs/assets/videos/butterfly.mp4',
      );

      _initializeVideoPlayerFuture = _controller.initialize();

      _controller.setLooping(true);
      urlType = 'video';
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    void deleteResume(int id) {
      BlocProvider.of<HomeBloc>(context)
          .add(SingularisDeletePortfolioEvent(portfolioId: id));
    }

    void handleDeletePortfolio(SingularisDeletePortfolioState state) {
      setState(() {
        switch (state.apiState) {
          case ApiStatus.LOADING:
            Log.v("Loading Add  Certificate....................");
            deletePortfolio = true;
            break;

          case ApiStatus.SUCCESS:
            Log.v("Success Add  Certificate....................");
            deletePortfolio = false;
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text('portfolio_deleted_successfully').tr(),
            ));
            Navigator.pop(context);
            break;
          case ApiStatus.ERROR:
            Log.v("Error Add Certificate....................");
            deletePortfolio = false;
            FirebaseAnalytics.instance
                .logEvent(name: 'portfolio_detail_page', parameters: {
              "ERROR": '${state.response?.error}',
            });
            break;
          case ApiStatus.INITIAL:
            break;
        }
      });
    }

    return Scaffold(
        backgroundColor: ColorConstants.WHITE,
        body: ScreenWithLoader(
            isLoading: deletePortfolio,
            body: BlocListener<HomeBloc, HomeState>(
              listener: (context, state) async {
                if (state is SingularisDeletePortfolioState) {
                  handleDeletePortfolio(state);
                }
              },
              child: SafeArea(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          children: [
                            Spacer(),
                            IconButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                icon: Icon(Icons.close_outlined))
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 24),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                                width: width(context) * 0.7,
                                child: Text(
                                  '${widget.portfolio.portfolioTitle}',
                                  style: Styles.bold(size: 16),
                                )),
                            Spacer(),
                            InkWell(
                                onTap: () async {
                                  await Navigator.push(
                                      context,
                                      PageTransition(
                                          duration: Duration(milliseconds: 300),
                                          reverseDuration:
                                              Duration(milliseconds: 300),
                                          type: PageTransitionType.bottomToTop,
                                          child: AddPortfolio(
                                            baseUrl: '${widget.baseUrl}',
                                            editMode: true,
                                            portfolio: widget.portfolio,
                                          ))).then(
                                      (value) => Navigator.pop(context));
                                },
                                child: SvgPicture.asset(
                                    'assets/images/edit_portfolio.svg')),
                            SizedBox(
                              width: 20,
                            ),
                            InkWell(
                                onTap: () async {
                                  AlertsWidget.showCustomDialog(
                                      context: context,
                                      title: '',
                                      text: tr('confirm_deletion_textone'),
                                      icon:
                                          'assets/images/circle_alert_fill.svg',
                                      onOkClick: () async {
                                        deleteResume(widget.portfolio.id);
                                      });
                                },
                                child: SvgPicture.asset(
                                  'assets/images/delete.svg',
                                  colorFilter: ColorFilter.mode(
                                      Color(0xff0E1638), BlendMode.srcIn),
                                )),
                          ],
                        ),
                      ),
                      Padding(
                        padding:
                            const EdgeInsets.only(left: 16, right: 10, top: 12),
                        child: Text('${widget.portfolio.desc}',
                            style: Styles.regular(
                                size: 14, color: Color(0xff929BA3))),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 10),
                        child: InkWell(
                            onTap: () {
                              String url = "${widget.portfolio.portfolioLink}";
                              if (url.startsWith('www.')) {
                                url = url.replaceFirst('www.', 'https://');
                              } else if (!url.startsWith('http://') &&
                                  !url.startsWith('https://')) {
                                url = 'https://' + url;
                              }

                              try {
                                launchUrl(Uri.parse('$url'));
                              } catch (e) {
                                print('exception is $e');
                              }
                            },
                            child: Text('${widget.portfolio.portfolioLink}',
                                style: Styles.regular(
                                    size: 12, color: Color(0xff0094FF)))),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      if (urlType == 'pdf') ...[
                        SizedBox(
                          height: height(context) * 0.7,
                          width: double.infinity,
                          child: PDF(
                            enableSwipe: true,
                            autoSpacing: false,
                            gestureRecognizers: [
                              Factory(() => PanGestureRecognizer()),
                              Factory(() => VerticalDragGestureRecognizer())
                            ].toSet(),
                          ).cachedFromUrl(
                            '${widget.baseUrl}${widget.portfolio.portfolioFile}',
                            placeholder: (progress) =>
                                Center(child: Text('$progress %')),
                            errorWidget: (error) =>
                                Center(child: Text(error.toString())),
                          ),
                        )
                      ] else if (urlType == 'img') ...[
                        Image.network(
                          '${widget.baseUrl}${widget.portfolio.portfolioFile}',
                          width: double.infinity,
                        ),
                      ] else if (urlType == 'video')
                        Container(
                          padding: const EdgeInsets.only(left: 16, right: 16),
                          width: double.infinity,
                          child: Center(
                            child: ClipRRect(
                                borderRadius: BorderRadius.circular(10),
                                child: VideoPlayerWidget(
                                  videoUrl:
                                      '${widget.baseUrl}${widget.portfolio.portfolioFile}',
                                )),
                          ),
                        )
                    ],
                  ),
                ),
              ),
            )));
  }
}
