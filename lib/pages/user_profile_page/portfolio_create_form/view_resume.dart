import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/widget.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:open_filex/open_filex.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';
import 'dart:isolate';
import 'dart:ui';

class ViewResume extends StatefulWidget {
  final String? resumUrl;
  final int? resumeId;
  final bool? viewOnly;
  const ViewResume(
      {super.key, this.resumUrl, this.resumeId, this.viewOnly = false});

  @override
  State<ViewResume> createState() => _ViewResumeState();
}

class _ViewResumeState extends State<ViewResume> {
  final GlobalKey<ScaffoldState> _key = new GlobalKey<ScaffoldState>();
  bool? updateResume = false;
  bool isDownloading = false;

  final ReceivePort _port = ReceivePort();

  @override
  void initState() {
    super.initState();
    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) {
      String id = data[0];
      DownloadTaskStatus status = data[1];
      int progress = data[2];
      setState(() {});
    });

    // FlutterDownloader.registerCallback(downloadCallback);
  }

  @override
  void dispose() {
    IsolateNameServer.removePortNameMapping('downloader_send_port');
    super.dispose();
  }

  static void downloadCallback(
      String id, DownloadTaskStatus status, int progress) {
    final SendPort send =
        IsolateNameServer.lookupPortByName('downloader_send_port')!;
    send.send([id, status, progress]);
  }

  // static void downloadCallback(
  //     String id, int status, int progress) {
  //   final SendPort send =
  //   IsolateNameServer.lookupPortByName('downloader_send_port')!;
  //   send.send([id, status, progress]);
  // }

  void _download(String url) async {
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    late AndroidDeviceInfo android;
    try {
      android = await plugin.androidInfo;
    } catch (e) {
      Log.v("exception file download $e");
    }
    // return;
    String localPath;

    final status = await Permission.storage.request();
    if (Platform.isIOS || status.isGranted || android.version.sdkInt >= 33) {
      // final externalDir = await getExternalStorageDirectory();

      if (Platform.isAndroid) {
        localPath = "/sdcard/download/";
      } else {
        localPath = (await getApplicationDocumentsDirectory()).path;
      }
      final file = File(localPath + "/" + url.split('/').last);
      if (!file.existsSync()) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('downloading_start').tr(),
        ));
        final id = await FlutterDownloader.enqueue(
          url: url,
          savedDir: localPath,
          showNotification: true,
          openFileFromNotification: true,
        ).then((value) async {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('resume_downloaded').tr(),
          ));

          OpenFilex.open(localPath + "/" + url.split('/').last);
        });
      } else {
        Utility.showSnackBar(
            scaffoldContext: context, message: tr('file_exists'));
        OpenFilex.open(localPath + "/" + url.split('/').last);
      }
    } else {
      launchUrl(Uri.parse('$url'), mode: LaunchMode.externalApplication);
      Log.v('Permission Denied');
    }
  }

  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            elevation: 0.0,
            backgroundColor: Colors.white,
            centerTitle: true,
            title: Text(
              'resume',
              style: Styles.bold(size: 14),
            ).tr(),
            actions: [
              if (!(widget.resumUrl == '' || widget.resumUrl == null) &&
                  widget.viewOnly != true)
                InkWell(
                    onTap: () {
                      // Utility().shareFile(context, url: widget.resumUrl!);
                      // return;
                      _download(widget.resumUrl!);
                    },
                    child: isDownloading == true
                        ? Center(
                            child: Transform.scale(
                                scale: 0.8,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: ColorConstants().gradientLeft(),
                                )),
                          )
                        : SvgPicture.asset(
                            'assets/images/download.svg',
                          )),
              SizedBox(
                width: 20,
              ),
              if (!(widget.resumUrl == '' || widget.resumUrl == null) &&
                  widget.viewOnly != true)
                InkWell(
                    onTap: () {
                      AlertsWidget.showCustomDialog(
                          context: context,
                          title: '',
                          text: tr('confirm_deletion_textone'),
                          icon: 'assets/images/circle_alert_fill.svg',
                          onOkClick: () async {
                            deleteResume(widget.resumeId!);
                          });
                    },
                    child: SvgPicture.asset(
                      'assets/images/delete.svg',
                    )),
              SizedBox(
                width: 20,
              ),
              Padding(
                padding: EdgeInsets.only(right: 8.0),
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context, false);
                  },
                  child: Icon(
                    Icons.close,
                    color: Colors.black,
                  ),
                ),
              ),
            ]),
        persistentFooterButtons: [
          if (widget.viewOnly != true)
            Container(
              width: width(context),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () async {
                      FilePickerResult? result;

                      if (Platform.isIOS) {
                        result = await FilePicker.platform.pickFiles(
                            allowMultiple: false,
                            type: FileType.custom,
                            allowedExtensions: ['pdf']);
                      } else {
                        result = await FilePicker.platform.pickFiles(
                            allowMultiple: false,
                            type: FileType.custom,
                            allowedExtensions: [
                              'pdf',
                            ]);
                      }

                      Map<String, dynamic> data = Map();

                      data['file'] = await MultipartFile.fromFile(
                          '${result?.files.first.path}',
                          filename: result?.files.first.name);
                      //  await Utility().s3UploadFile('${result?.files.first.path}').then((value) => data['file_cdn'] = value);

                      if (result?.files.first.extension == 'pdf') {
                        setState(() {});
                        updateResume = true;
                        addResume(data);
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                          content: Text('plz_upload_only_pdf').tr(),
                        ));
                      }
                      // }
                    },
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/images/upload_icon.svg',
                          colorFilter: ColorFilter.mode(
                              ColorConstants().primaryColor()!,
                              BlendMode.srcIn),
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        GradientText(
                            child: Text('upload_latest_only_text').tr())
                      ],
                    ),
                  ),
                  Text('supported_format_resume',
                          style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: Color(0xff929BA3)))
                      .tr(),
                ],
              ),
            ),
        ],
        body: ScreenWithLoader(
          isLoading: updateResume,
          body: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) async {
              if (state is SingularisDeletePortfolioState) {
                handleDeleteResume(state);
              }
              if (state is AddResumeState) {
                if (state.apiState == ApiStatus.SUCCESS) {
                  updateResume = false;
                  setState(() {});

                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    content: Text('resume_uploaded_successfully').tr(),
                  ));
                  await Future.delayed(Duration(seconds: 1));
                  Navigator.pop(context);
                }
              }
            },
            child: Container(
              color: ColorConstants.WHITE,
              height: height(context) * 0.8,
              width: width(context),
              child: widget.resumUrl == '' || widget.resumUrl == null
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/images/no_resume.svg',
                          width: 80,
                        ),
                        SizedBox(
                          height: 20,
                        ),
                        Text(
                          'not_uploaded_resume',
                          style: Styles.regular(size: 14),
                        ).tr(),
                      ],
                    )
                  : Container(
                      width: double.infinity,
                      height: double.infinity,
                      child: PDF(
                        autoSpacing: false,
                        fitPolicy: FitPolicy.BOTH,
                        enableSwipe: true,
                        gestureRecognizers: [
                          Factory(() => PanGestureRecognizer()),
                          Factory(() => VerticalDragGestureRecognizer())
                        ].toSet(),
                      ).cachedFromUrl(
                        widget.resumUrl!,
                        placeholder: (progress) =>
                            Center(child: Text('$progress %')),
                        errorWidget: (error) =>
                            Center(child: Text(error.toString())),
                      ),
                    ),
            ),
          ),
        ));
  }

  void deleteResume(int id) {
    BlocProvider.of<HomeBloc>(context)
        .add(SingularisDeletePortfolioEvent(portfolioId: id));
  }

  void addResume(Map<String, dynamic> data) {
    BlocProvider.of<HomeBloc>(context).add(AddResumeEvent(data: data));
  }

  void handleDeleteResume(SingularisDeletePortfolioState state) {
    setState(() {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading Add  Certificate....................");
          updateResume = true;
          break;

        case ApiStatus.SUCCESS:
          Log.v("Success Add  Certificate....................");
          updateResume = false;
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('delete_validation_msg').tr(),
          ));
          Navigator.pop(context);
          break;
        case ApiStatus.ERROR:
          Log.v("Error Add Certificate....................");
          updateResume = false;
          FirebaseAnalytics.instance
              .logEvent(name: 'edit_profile_page', parameters: {
            "ERROR": '${state.response?.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void download(String? usersFile) async {
    checkPermission();

    String localPath = "";
    if (Platform.isAndroid) {
      localPath = "/sdcard/download/";

      final file = File(localPath + "/" + usersFile!.split('/').last);
      if (file.existsSync()) {
        await FlutterDownloader.open(taskId: usersFile.split('/').last);
        return;
      }
    } else {
      localPath = (await getApplicationDocumentsDirectory()).path;
    }

    var savedDir = Directory(localPath);
    bool hasExisted = await savedDir.exists();
    if (!hasExisted) {
      savedDir = await savedDir.create();
    }
    download2(usersFile!, localPath);
  }

  Future download2(String url, String savePath) async {
    try {
      setState(() {
        isDownloading = true;
      });

      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('downloading_start').tr(),
      ));

      final taskId = await FlutterDownloader.enqueue(
        url: url,
        savedDir: savePath,
        showNotification: true,
        openFileFromNotification: true,
      ).catchError(() {});
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('resume_downloaded').tr(),
      ));
    } catch (e) {
      launchUrl(Uri.parse('$url'), mode: LaunchMode.externalApplication);
    }
    await Future.delayed(Duration(seconds: 2));
    setState(() {
      isDownloading = false;
    });
  }

  Future<String> downloadFile(String url) async {
    String localPath;
    if (Platform.isAndroid) {
      localPath = "/sdcard/download/";
      //check if file exists
      final file = File(localPath + "/" + url.split('/').last);
      if (file.existsSync()) {
        Utility.showSnackBar(
            scaffoldContext: context, message: tr('file_exists'));

        await FlutterDownloader.open(taskId: url.split('/').last);
      }
    } else {
      localPath = (await getApplicationDocumentsDirectory()).path;
    }
    final fileName = url.split('/').last;
    final filePath = '$localPath/$fileName';

    final response = await http.get(Uri.parse(url));
    final file = File(filePath);
    await file.writeAsBytes(response.bodyBytes);

    return filePath;
  }

  static Future<void> checkPermission() async {
    if (!kIsWeb) {
      if (Platform.isAndroid || Platform.isIOS) {
        var permissionStatus = await Permission.storage.status;

        switch (permissionStatus) {
          case PermissionStatus.denied:
          case PermissionStatus.permanentlyDenied:
            await Permission.storage.request();
            break;
          default:
        }
      }
    }
  }
}
