import 'dart:io';
import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
//import 'package:flutter_html/style.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/new_portfolio_response.dart';
import 'package:masterg/data/models/response/home_response/pi_detail_resp.dart';
import 'package:masterg/data/models/response/home_response/portfolio_competition_response.dart';
import 'package:masterg/data/models/response/home_response/top_score.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/explore_job/explore_job_list_page.dart';
import 'package:masterg/pages/ghome/widget/read_more.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/add_certificate.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/add_education.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/add_experience.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/add_extra_act.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/add_portfolio.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/certificate_list.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/competition_list.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/curve_cclipper.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/education_list.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/experience_list.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/extra_activities_list.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/my_skill.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/social_page.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/view_edit_profile_image.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/view_resume.dart';
import 'package:masterg/pages/user_profile_page/portfolio_detail.dart';
import 'package:masterg/pages/user_profile_page/portfolio_list.dart';
import 'package:masterg/pages/user_profile_page/singularis_profile_edit.dart';
import 'package:masterg/pages/video_resume/sample_resume_page.dart';
import 'package:masterg/pages/video_resume/video_resume.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/resource/size_constants.dart';
import 'package:masterg/utils/str_to_time.dart';
import 'package:masterg/utils/video_screen.dart';
import 'package:open_filex/open_filex.dart';
import 'package:page_transition/page_transition.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import '../../../utils/utility.dart';
import '../../auth_pages/terms_and_condition_page.dart';
import '../../custom_pages/custom_widgets/CommonWebView.dart';
import '../../singularis/recentactivities/recent_activities_page.dart';
import '../../singularis/recentactivities/recent_activities_reels_page.dart';

class NewPortfolioPage extends StatefulWidget {
  final userId;
  final bool? expJobResume;
  const NewPortfolioPage({super.key, this.userId, this.expJobResume = true});

  @override
  State<NewPortfolioPage> createState() => _NewPortfolioPageState();
}

class _NewPortfolioPageState extends State<NewPortfolioPage> {
  bool editModeEnabled = true;
  bool? isPortfolioLoading = true;
  PortfolioResponse? portfolioResponse;
  List<SampleResume>? sampleResumeResp;
  PortfolioCompetitionResponse? competition;
  TopScoringResponse? userRank;
  PiDetailResponse? piDetailResp;
  double dividerMarginTop = 12.0;
  File? pickedFile;
  List<File> pickedList = [];
  ImageFormat _format = ImageFormat.JPEG;
  int _quality = 10;
  String? _tempDir;
  String? filePath;
  String? linkMessage;
  bool isCreatingLink = false;
  FirebaseDynamicLinks dynamicLinks = FirebaseDynamicLinks.instance;
  bool _switchValue = true;

  @override
  void initState() {
    super.initState();
    // getPiDetail();
    getPortfolio();
    getPortfolioCompetition();
    topScoringUser();
  }

  void getPortfolio() {
    BlocProvider.of<HomeBloc>(context)
        .add(PortfolioEvent(userId: widget.userId));
  }

  void getPiDetail() {
    BlocProvider.of<HomeBloc>(context).add(PiDetailEvent(
        userId: widget.userId ?? Preference.getInt(Preference.USER_ID)));
  }

  void topScoringUser() {
    BlocProvider.of<HomeBloc>(context).add(TopScoringUserEvent(
        userId: widget.userId ?? Preference.getInt(Preference.USER_ID)));
  }

  void getPortfolioCompetition() {
    BlocProvider.of<HomeBloc>(context)
        .add(PortfolioCompetitoinEvent(userId: widget.userId));
  }

  List<String> avatarList = [
    "assets/avatar/hero.jpg",
    "assets/avatar/simple.avif",
    "assets/avatar/cowboy.jpg",
    "assets/avatar/devilboy.png",
    "assets/avatar/smart.webp",
    "assets/avatar/benboy.jpg",
    "assets/avatar/download.jpeg",
    "assets/avatar/girls3.png",
    "assets/avatar/girls4.png",
    "assets/avatar/girls5.png",
  ];

  void _openCustomDialog() {
    showGeneralDialog(
        barrierColor: Colors.black.withValues(alpha: 0.5),
        transitionBuilder: (context, a1, a2, widget) {
          return Transform.scale(
            scale: a1.value,
            child: Opacity(
              opacity: a1.value,
              child: AlertDialog(
                shape: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16.0)),
                // shape: RoundedRectangleBorder(
                //     borderRadius: BorderRadius.all(Radius.circular(10.0))),

                content: SingleChildScrollView(
                  child: Column(
                    children: [
                      Text('lbl_choose_avtr').tr(),
                      // SizedBox(height: 10),
                      Container(
                        height: 300,
                        width: 300,
                        child: GridView.builder(
                          itemCount: avatarList.length,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 8.0,
                            mainAxisSpacing: 8.0,
                          ),
                          itemBuilder: (context, index) {
                            return Card(
                              child: Center(
                                child: Container(
                                  padding: EdgeInsets.all(8),
                                  // decoration: BoxDecoration(color: Colors.red, shape: BoxShape.circle),
                                  child: ClipOval(
                                    child: SizedBox.fromSize(
                                        size: Size.fromRadius(40),
                                        child: InkWell(
                                          onTap: () {
                                            Navigator.pop(context);
                                            // Preference.setString(
                                            //     Preference.AVATAR_IMAGE,
                                            //     '${avatarList[index]}');
                                            setState(() {});
                                          },
                                          child: Image.asset(
                                            '${avatarList[index]}',
                                          ),
                                        )),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        },
        transitionDuration: Duration(milliseconds: 200),
        barrierDismissible: true,
        barrierLabel: '',
        context: context,
        pageBuilder: (context, animation1, animation2) {
          return Container();
        });
  }

  List<String> listOfMonths = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ];

  Future<void> initDynamicLinks() async {
    dynamicLinks.onLink.listen((dynamicLinkData) {
      final Uri uri = dynamicLinkData.link;
      final queryParams = uri.queryParameters;

      if (queryParams.isNotEmpty) {
        String? productId = queryParams["id"];
        Navigator.pushNamed(context, dynamicLinkData.link.path,
            arguments: {"productId": int.parse(productId!)});
      } else {
        Navigator.pushNamed(
          context,
          dynamicLinkData.link.path,
        );
      }
    }).onError((error) {
      Log.v(error.message);
    });
  }

  @override
  Widget build(BuildContext context) {
    String? baseUrl = '${APK_DETAILS['domain_url']}/portfolio/';

    return BlocManager(
      initState: (context) {},
      child: BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is PortfolioState) {
              handlePortfolioState(state);
            }
            if (state is PortfoilioCompetitionState) {
              handleCompetition(state);
            }
            if (state is TopScoringUserState) {
              handletopScoring(state);
            }
            if (state is PiDetailState) {
              handlePiDetail(state);
            }
            if (state is OpenToWorkState) handleOpenToWork(state);
          },
          child: Scaffold(
              //backgroundColor: Color(0xffF2F2F2),
              appBar: PreferredSize(
                preferredSize: Size.fromHeight(0),
                child: AppBar(
                    automaticallyImplyLeading: false,
                    elevation: 0,
                    flexibleSpace: Container(
                      decoration: BoxDecoration(
                        color: ColorConstants.WHITE,
                        gradient: LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: <Color>[
                              ColorConstants().gradientLeft(),
                              ColorConstants().gradientRight()
                            ]),
                      ),
                    )),
              ),
              body: ScreenWithLoader(
                isLoading: isPortfolioLoading,
                body: SingleChildScrollView(
                    key: const PageStorageKey<String>('portfolioList'),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Stack(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  width: double.infinity,
                                  height:
                                      MediaQuery.of(context).size.height * 0.27,
                                  padding: EdgeInsets.only(top: 8),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(10),
                                        bottomRight: Radius.circular(10)),
                                    color: ColorConstants.WHITE,
                                    gradient: LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        colors: <Color>[
                                          ColorConstants().gradientLeft(),
                                          ColorConstants().gradientRight()
                                        ]),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        height: 5,
                                      ),
                                      Row(
                                        children: [
                                          IconButton(
                                              onPressed: () {
                                                Navigator.pop(context);
                                              },
                                              icon: Icon(
                                                Icons.arrow_back,
                                                color: ColorConstants.WHITE,
                                              )),
                                          Spacer(),
                                          if (widget.userId == null)
                                            InkWell(
                                                onTap: () async {
                                                  Get.dialog(
                                                    Dialog(
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(16.0),
                                                      ),
                                                      insetPadding:
                                                          EdgeInsets.symmetric(
                                                              horizontal: 16),
                                                      child: Container(
                                                        height: 440,
                                                        child: Column(
                                                          children: [
                                                            InkWell(
                                                              onTap: () {
                                                                Get.back();
                                                              },
                                                              child: Padding(
                                                                padding:
                                                                    const EdgeInsets
                                                                        .all(
                                                                        8.0),
                                                                child: Align(
                                                                  alignment:
                                                                      Alignment
                                                                          .topRight,
                                                                  child: Icon(
                                                                    Icons.close,
                                                                    color: ColorConstants
                                                                        .BLACK,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                            showQRViewDialog(),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                },
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          right: 12.0),
                                                  child: Icon(
                                                    Icons.share,
                                                    color: ColorConstants.WHITE,
                                                  ),
                                                )),
                                        ],
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.all(2.0),
                                        child: Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceEvenly,
                                            children: [
                                              Stack(
                                                children: [
                                                  InkWell(
                                                    onTap: () {
                                                      showModalBottomSheet(
                                                          shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.only(
                                                                  topLeft: Radius
                                                                      .circular(
                                                                          20),
                                                                  topRight: Radius
                                                                      .circular(
                                                                          20))),
                                                          context: context,
                                                          builder: (context) {
                                                            return FractionallySizedBox(
                                                              heightFactor: 0.3,
                                                              child: Container(
                                                                child: Column(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .min,
                                                                  children: <Widget>[
                                                                    Row(
                                                                      children: [
                                                                        Spacer(),
                                                                        IconButton(
                                                                          onPressed: () =>
                                                                              Navigator.pop(context),
                                                                          icon:
                                                                              Icon(
                                                                            Icons.close,
                                                                            color:
                                                                                Colors.black,
                                                                          ),
                                                                        )
                                                                      ],
                                                                    ),
                                                                    ListTile(
                                                                      leading:
                                                                          SvgPicture
                                                                              .asset(
                                                                        'assets/images/camera.svg',
                                                                        colorFilter: APK_DETAILS['gradient_icon'] ==
                                                                                '0'
                                                                            ? ColorFilter.mode(ColorConstants().primaryColor()!,
                                                                                BlendMode.srcIn)
                                                                            : null,
                                                                      ),
                                                                      title:
                                                                          new Text(
                                                                        'View_or_edit_profile_picture',
                                                                        style: Styles.regular(
                                                                            size:
                                                                                14),
                                                                      ).tr(),
                                                                      onTap:
                                                                          () {
                                                                        Navigator.pop(
                                                                            context);
                                                                        Navigator.push(
                                                                            context,
                                                                            NextPageRoute(UploadProfile(
                                                                              editVideo: false,
                                                                              onBack: () {
                                                                                getPortfolio();
                                                                              },
                                                                            )));
                                                                      },
                                                                    ),
                                                                    //TODO: Hide by saksham 12 Jun 2024
                                                                    /*ListTile(
                                                                      leading:
                                                                          SvgPicture
                                                                              .asset(
                                                                        'assets/images/portfolio_video.svg',
                                                                        color: APK_DETAILS['gradient_icon'] ==
                                                                                '0'
                                                                            ? ColorConstants().primaryColor()
                                                                            : null,
                                                                      ),
                                                                      title:
                                                                          new Text(
                                                                        'add_profile_video',
                                                                        style: Styles.regular(
                                                                            size:
                                                                                14),
                                                                      ).tr(),
                                                                      onTap:
                                                                          () {
                                                                        // _initFilePiker();
                                                                        Navigator.pop(
                                                                            context);
                                                                        Navigator.push(
                                                                            context,
                                                                            NextPageRoute(UploadProfile(
                                                                              editVideo: true,
                                                                              onBack: () {
                                                                                getPortfolio();
                                                                              },
                                                                            )));
                                                                      },
                                                                    ),*/
                                                                  ],
                                                                ),
                                                              ),
                                                            );
                                                          });
                                                    },
                                                    child: Transform.rotate(
                                                      angle: Utility()
                                                              .isRTL(context)
                                                          ? (83 *
                                                              (3.14159265359 /
                                                                  180))
                                                          : 0,
                                                      child: ClipPath(
                                                        clipper:
                                                            CircleClipper(),
                                                        child: ClipOval(
                                                          child:
                                                              Transform.rotate(
                                                            angle: Utility()
                                                                    .isRTL(
                                                                        context)
                                                                ? -(83 *
                                                                    (3.14159265359 /
                                                                        180))
                                                                : 0,
                                                            child:
                                                                CachedNetworkImage(
                                                              imageUrl: widget
                                                                          .userId !=
                                                                      null
                                                                  ? '${portfolioResponse?.data.image}'
                                                                  : '${Preference.getString(Preference.PROFILE_IMAGE)}',
                                                              filterQuality:
                                                                  FilterQuality
                                                                      .low,
                                                              width: SizeConstants
                                                                  .USER_PROFILE_IMAGE_SIZE,
                                                              height: SizeConstants
                                                                  .USER_PROFILE_IMAGE_SIZE,
                                                              fit: BoxFit.cover,
                                                              errorWidget: (context,
                                                                      error,
                                                                      stackTrace) =>
                                                                  SvgPicture
                                                                      .asset(
                                                                'assets/images/default_user.svg',
                                                                width: 50,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  Positioned(
                                                    left:
                                                        Utility().isRTL(context)
                                                            ? null
                                                            : 38,
                                                    right:
                                                        Utility().isRTL(context)
                                                            ? 38
                                                            : null,
                                                    top: 41,
                                                    child: InkWell(
                                                      onTap: () {
                                                        //TODO: Hide by saksham 12 Jun 2024
                                                        /*if (Preference.getString(
                                                                    Preference
                                                                        .PROFILE_VIDEO) !=
                                                                null &&
                                                            Preference.getString(
                                                                    Preference
                                                                        .PROFILE_VIDEO) !=
                                                                '')
                                                          Navigator.push(
                                                              context,
                                                              NextPageRoute(
                                                                  UploadProfile(
                                                                playVideo: true,
                                                                onBack: () {
                                                                  getPortfolio();
                                                                },
                                                              )));
                                                        else
                                                          ScaffoldMessenger.of(
                                                                  context)
                                                              .showSnackBar(
                                                                  SnackBar(
                                                            content: Text(
                                                                    'no_profile_video_uploaded')
                                                                .tr(),
                                                          ));*/

                                                        Navigator.push(
                                                            context,
                                                            NextPageRoute(
                                                                VideoResume()));
                                                      },
                                                      child: Container(
                                                        height: 32.0,
                                                        width: 32.0,
                                                        padding:
                                                            EdgeInsets.all(2),
                                                        decoration:
                                                            BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      100),
                                                          border: Border.all(
                                                              width: 0,
                                                              color: Colors
                                                                  .transparent),
                                                          // color: ColorConstants()
                                                          //     .gradientRight(),
                                                        ),
                                                        child: Transform.rotate(
                                                            angle: Utility()
                                                                    .isRTL(
                                                                        context)
                                                                ? -math.pi
                                                                : 0,
                                                            child: SvgPicture
                                                                .asset(
                                                              'assets/images/profile_play.svg',
                                                            )),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              SizedBox(
                                                width: 14,
                                              ),
                                              SizedBox(
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.7,
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Text(
                                                        Utility().decrypted128(widget
                                                                    .userId !=
                                                                null
                                                            ? '${portfolioResponse?.data.name}'
                                                            : '${Preference.getString(Preference.FIRST_NAME)}'),
                                                        maxLines: 1,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        softWrap: false,
                                                        style: Styles.bold(
                                                            size: 17,
                                                            color:
                                                                ColorConstants
                                                                    .WHITE)),

                                                    //check for null and blank
                                                    ![
                                                      '',
                                                      null
                                                    ].contains(Preference
                                                            .getString(
                                                                Preference
                                                                    .MEC_REGD_ID))
                                                        ? Text(
                                                            tr('mec_regd_id') +
                                                                ' : ${Preference.getString(Preference.MEC_REGD_ID)}',
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            softWrap: false,
                                                            style: Styles.regular(
                                                                size: 12,
                                                                color:
                                                                    ColorConstants
                                                                        .WHITE))
                                                        : SizedBox(),
                                                    Text(
                                                        Utility().decrypted128(
                                                            '${Preference.getString(Preference.USER_EMAIL)}'),
                                                        maxLines: 2,
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                        softWrap: false,
                                                        style: Styles.regular(
                                                            size: 12,
                                                            color:
                                                                ColorConstants
                                                                    .WHITE)),
                                                    SizedBox(height: 4),

                                                    Text(
                                                        Preference.getString(
                                                                        Preference
                                                                            .USER_HEADLINE) !=
                                                                    null &&
                                                                Preference.getString(
                                                                        Preference
                                                                            .USER_HEADLINE) !=
                                                                    ''
                                                            ? widget.userId !=
                                                                    null
                                                                ? '${portfolioResponse?.data.portfolioProfile.first.headline}'
                                                                : '${Preference.getString(Preference.USER_HEADLINE)}'
                                                            : tr(
                                                                "add_headline"),
                                                        style: Styles.regular(
                                                            size: 12,
                                                            lineHeight: 1,
                                                            color:
                                                                ColorConstants
                                                                    .WHITE)),
                                                    SizedBox(height: 2),
                                                    Row(
                                                      // crossAxisAlignment:
                                                      //     CrossAxisAlignment
                                                      //         .center,
                                                      // mainAxisAlignment:
                                                      //     MainAxisAlignment
                                                      //         .center,
                                                      children: [
                                                        SvgPicture.asset(
                                                          'assets/images/person_location.svg',
                                                          height: 12,
                                                          width: 12,
                                                        ),
                                                        SizedBox(width: 2),
                                                        Text(
                                                            widget.userId !=
                                                                    null
                                                                ? '${portfolioResponse?.data.portfolioProfile.first.city}, ${portfolioResponse?.data.portfolioProfile.first.country}'
                                                                : '${Preference.getString(Preference.LOCATION) ?? tr('add_location')}',
                                                            maxLines: 1,
                                                            style: Styles.regular(
                                                                size: 12,
                                                                color:
                                                                    ColorConstants
                                                                        .WHITE)),
                                                        Spacer(),
                                                        if (widget.userId ==
                                                            null)
                                                          Transform.scale(
                                                              scale: 1.2,
                                                              child: InkWell(
                                                                onTap:
                                                                    () async {
                                                                  await Navigator.push(
                                                                          context,
                                                                          PageTransition(
                                                                              duration: Duration(milliseconds: 600),
                                                                              reverseDuration: Duration(milliseconds: 600),
                                                                              type: PageTransitionType.bottomToTop,
                                                                              child: EditProfilePage()))
                                                                      .then((value) => getPortfolio());
                                                                },
                                                                child: Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .only(
                                                                          right:
                                                                              8.0),
                                                                  child:
                                                                      SvgPicture
                                                                          .asset(
                                                                    'assets/images/edit.svg',
                                                                  ),
                                                                ),
                                                              ))
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ]),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  color: ColorConstants.WHITE,
                                  height: 50,
                                ),
                                Container(
                                  color: ColorConstants.WHITE,
                                  width: double.infinity,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 20),
                                    child: Text(
                                      widget.userId != null
                                          ? '${portfolioResponse?.data.portfolioProfile.first.aboutMe}'
                                          : '${Preference.getString(Preference.ABOUT_ME) ?? tr('tell_about_yourself')}',
                                      textAlign: TextAlign.center,
                                      style: Styles.regular(
                                          size: 12, color: Color(0xff5A5F73)),
                                    ),
                                  ),
                                ),
                                Container(
                                  color: ColorConstants.WHITE,
                                  child: Center(
                                    child: SizedBox(
                                      width: MediaQuery.of(context).size.width *
                                          0.95,
                                      height: 60,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Preference.getString(
                                                      Preference.PHONE) !=
                                                  ''
                                              ? InkWell(
                                                  onTap: () async {
                                                    await launchUrl(Uri.parse(
                                                        "tel:${Utility().decrypted128('${Preference.getString(Preference.PHONE)}')}"));
                                                  },
                                                  child: ShaderMask(
                                                    blendMode: BlendMode.srcIn,
                                                    shaderCallback:
                                                        (Rect bounds) {
                                                      return LinearGradient(
                                                              begin: Alignment
                                                                  .centerLeft,
                                                              end: Alignment
                                                                  .centerRight,
                                                              colors: <Color>[
                                                            ColorConstants()
                                                                .gradientLeft(),
                                                            ColorConstants()
                                                                .gradientRight()
                                                          ])
                                                          .createShader(bounds);
                                                    },
                                                    child: SvgPicture.asset(
                                                      'assets/images/call.svg',
                                                    ),
                                                  ),
                                                )
                                              : Icon(
                                                  Icons.call,
                                                  color: Color.fromRGBO(
                                                      146, 155, 163, 1),
                                                ),
                                          SizedBox(width: 14),
                                          InkWell(
                                            onTap: () async {
                                              await launchUrl(Uri.parse(
                                                  "mailto:${Utility().decrypted128('${Preference.getString(Preference.USER_EMAIL)}')}"));
                                            },
                                            child: ShaderMask(
                                              blendMode: BlendMode.srcIn,
                                              shaderCallback: (Rect bounds) {
                                                return LinearGradient(
                                                    begin: Alignment.centerLeft,
                                                    end: Alignment.centerRight,
                                                    colors: <Color>[
                                                      ColorConstants()
                                                          .gradientLeft(),
                                                      ColorConstants()
                                                          .gradientRight()
                                                    ]).createShader(bounds);
                                              },
                                              child: SvgPicture.asset(
                                                'assets/images/email.svg',
                                                colorFilter: ColorFilter.mode(
                                                    ColorConstants()
                                                        .primaryColor()!,
                                                    BlendMode.srcIn),
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 14),
                                          VerticalDivider(
                                            color: Color(0xffECECEC),
                                            width: 10,
                                            thickness: 2,
                                            indent: 10,
                                            endIndent: 10,
                                          ),
                                          SizedBox(width: 14),
                                          portfolioResponse?.data
                                                          .portfolioSocial ==
                                                      null ||
                                                  portfolioResponse
                                                          ?.data
                                                          .portfolioSocial
                                                          .length ==
                                                      0
                                              ? Row(
                                                  children: [
                                                    SvgPicture.asset(
                                                      'assets/images/linkedin_un.svg',
                                                      colorFilter:
                                                          ColorFilter.mode(
                                                              ColorConstants
                                                                  .GREY_4,
                                                              BlendMode.srcIn),
                                                    ),
                                                    SizedBox(width: 14),
                                                    SvgPicture.asset(
                                                      'assets/images/insta_un.svg',
                                                      colorFilter:
                                                          ColorFilter.mode(
                                                              ColorConstants
                                                                  .GREY_4,
                                                              BlendMode.srcIn),
                                                    ),
                                                    SizedBox(width: 14),
                                                    SvgPicture.asset(
                                                      'assets/images/twitter_un.svg',
                                                      colorFilter:
                                                          ColorFilter.mode(
                                                              ColorConstants
                                                                  .GREY_4,
                                                              BlendMode.srcIn),
                                                    ),
                                                    SizedBox(width: 14),
                                                    SvgPicture.asset(
                                                      'assets/images/behance_un.svg',
                                                      colorFilter:
                                                          ColorFilter.mode(
                                                              ColorConstants
                                                                  .GREY_4,
                                                              BlendMode.srcIn),
                                                    ),
                                                    SizedBox(width: 3),
                                                  ],
                                                )
                                              : ShowSocialLinks(
                                                  portfolioSocial:
                                                      portfolioResponse
                                                          ?.data
                                                          .portfolioSocial
                                                          .first,
                                                ),
                                          if (widget.userId == null)
                                            SizedBox(width: 10),
                                          if (widget.userId == null)
                                            InkWell(
                                              onTap: () {
                                                if (portfolioResponse
                                                        ?.data
                                                        .portfolioSocial
                                                        .length !=
                                                    0)
                                                  Navigator.push(
                                                      context,
                                                      NextPageRoute(SocialPage(
                                                          social:
                                                              portfolioResponse
                                                                  ?.data
                                                                  .portfolioSocial
                                                                  .first)));
                                                else
                                                  Navigator.push(
                                                      context,
                                                      NextPageRoute(
                                                          SocialPage()));
                                              },
                                              child: ShaderMask(
                                                blendMode: BlendMode.srcIn,
                                                shaderCallback: (Rect bounds) {
                                                  return LinearGradient(
                                                      begin:
                                                          Alignment.centerLeft,
                                                      end:
                                                          Alignment.centerRight,
                                                      colors: <Color>[
                                                        ColorConstants()
                                                            .gradientLeft(),
                                                        ColorConstants()
                                                            .gradientRight()
                                                      ]).createShader(bounds);
                                                },
                                                child: SvgPicture.asset(
                                                  'assets/images/add_icon_gradient.svg',
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            ),
                            Positioned(
                                left: 25,
                                right: 25,
                                top: MediaQuery.of(context).size.height * 0.22,
                                child: Container(
                                  height: 100,
                                  padding: EdgeInsets.symmetric(
                                      vertical: 4, horizontal: 8),
                                  decoration: BoxDecoration(
                                      color: ColorConstants.WHITE,
                                      boxShadow: [
                                        BoxShadow(
                                            color: Color(0xff898989)
                                                .withValues(alpha: 0.1),
                                            offset: Offset(0, 4.0),
                                            blurRadius: 11)
                                      ],
                                      borderRadius: BorderRadius.circular(20)),
                                  child: Center(
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Text(
                                              'rank',
                                              style: Styles.semibold(size: 12),
                                            ).tr(),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                SizedBox(
                                                    height: 28,
                                                    width: 28,
                                                    child: SvgPicture.asset(
                                                      'assets/images/leaderboard.svg',
                                                      // color: APK_DETAILS[
                                                      //             'gradient_icon'] ==
                                                      //         '0'
                                                      //     ? ColorConstants()
                                                      //         .primaryColor()
                                                      //     : null,
                                                    )),
                                                ShaderMask(
                                                  blendMode: BlendMode.srcIn,
                                                  shaderCallback:
                                                      (Rect bounds) {
                                                    return LinearGradient(
                                                        begin: Alignment
                                                            .centerLeft,
                                                        end: Alignment
                                                            .centerRight,
                                                        colors: <Color>[
                                                          ColorConstants()
                                                              .gradientRight(),
                                                          ColorConstants()
                                                              .gradientRight()
                                                        ]).createShader(bounds);
                                                  },
                                                  child: Text(
                                                    userRank?.data?.length != 0
                                                        ? '${userRank?.data?.first?.rank ?? '00'}'
                                                        : '00',
                                                    style:
                                                        Styles.bold(size: 26),
                                                  ),
                                                )
                                              ],
                                            ),
                                            Text(
                                              userRank?.data?.length != 0
                                                  ? tr('rank_out_of', args: [
                                                      '${userRank?.data?.first?.rankOutOf ?? '0'}'
                                                    ])
                                                  : tr(
                                                      'compelete_to_gain_rank'),
                                              style: Styles.regular(
                                                  size: 10,
                                                  color: Color(0xff5A5F73)),
                                            )
                                          ],
                                        ),
                                        SizedBox(width: 20),
                                        VerticalDivider(
                                          color: Color(0xffECECEC),
                                          width: 10,
                                          thickness: 2,
                                          indent: 10,
                                          endIndent: 10,
                                        ),
                                        SizedBox(width: 20),
                                        Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Text(
                                              'points',
                                              style: Styles.semibold(size: 12),
                                            ).tr(),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                SizedBox(
                                                    height: 28,
                                                    width: 28,
                                                    child: SvgPicture.asset(
                                                      'assets/images/coin.svg',
                                                      // color: APK_DETAILS[
                                                      //             'gradient_icon'] ==
                                                      //         '0'
                                                      //     ? ColorConstants()
                                                      //         .primaryColor()
                                                      //     : null,
                                                    )),
                                                SizedBox(
                                                  width: 4,
                                                ),
                                                ShaderMask(
                                                  blendMode: BlendMode.srcIn,
                                                  shaderCallback:
                                                      (Rect bounds) {
                                                    return LinearGradient(
                                                        begin: Alignment
                                                            .centerLeft,
                                                        end: Alignment
                                                            .centerRight,
                                                        colors: <Color>[
                                                          ColorConstants()
                                                              .gradientRight(),
                                                          ColorConstants()
                                                              .gradientRight()
                                                        ]).createShader(bounds);
                                                  },
                                                  child: Text(
                                                    userRank?.data?.length != 0
                                                        ? '${userRank?.data?.first?.score ?? '00'}'
                                                        : '00',
                                                    style:
                                                        Styles.bold(size: 24),
                                                  ),
                                                )
                                              ],
                                            ),
                                            Text(
                                              userRank?.data?.length != 0
                                                  ? tr('gained_from_count_act',
                                                      args: [
                                                          '${userRank?.data?.first?.gainedFrom ?? '0'}'
                                                        ])
                                                  : tr(
                                                      'gained_from_activities'),
                                              style: Styles.regular(
                                                  size: 10,
                                                  color: Color(0xff5A5F73)),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                )),
                          ],
                        ),
                        Center(
                          child: Container(
                            color: Color(0xffF3F3F3),
                            height: 1.5,
                            width: width(context) * 0.9,
                          ),
                        ),
                        widget.expJobResume == true
                            ? Container(
                                color: ColorConstants.WHITE,
                                padding: EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 4),
                                child: portfolioResponse?.data != null
                                    ? Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          //TODO: Semple
                                          InkWell(
                                            onTap: () {
                                              Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                      builder: (context) =>
                                                          SampleResumePage(
                                                              sampleResumeResp:
                                                                  portfolioResponse
                                                                      ?.data
                                                                      .sampleResumes)));
                                            },
                                            child: ShaderMask(
                                              blendMode: BlendMode.srcIn,
                                              shaderCallback: (Rect bounds) {
                                                return LinearGradient(
                                                    begin: Alignment.centerLeft,
                                                    end: Alignment.centerRight,
                                                    colors: <Color>[
                                                      ColorConstants()
                                                          .gradientLeft(),
                                                      ColorConstants()
                                                          .gradientRight()
                                                    ]).createShader(bounds);
                                              },
                                              child: SvgPicture.asset(
                                                'assets/images/cv_sempel.svg',
                                                colorFilter: ColorFilter.mode(
                                                    ColorConstants()
                                                        .primaryColor()!,
                                                    BlendMode.srcIn),
                                                height: 20,
                                                width: 20,
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 20),

                                          InkWell(
                                            onTap: () async {
                                              if (portfolioResponse?.data.resume
                                                          .length ==
                                                      0 &&
                                                  widget.userId == null)
                                                await Navigator.push(
                                                    context,
                                                    NextPageRoute(ViewResume(
                                                      resumeId: null,
                                                      resumUrl: null,
                                                    )));
                                              else
                                                await Navigator.push(
                                                    context,
                                                    NextPageRoute(ViewResume(
                                                      resumeId:
                                                          portfolioResponse
                                                              ?.data
                                                              .resume
                                                              .first
                                                              .id,
                                                      resumUrl:
                                                          '${portfolioResponse?.data.resume.first.url}',
                                                      viewOnly:
                                                          widget.userId != null,
                                                    )));
                                              getPortfolio();
                                            },
                                            child: Row(
                                              children: [
                                                ShaderMask(
                                                  blendMode: BlendMode.srcIn,
                                                  shaderCallback:
                                                      (Rect bounds) {
                                                    return LinearGradient(
                                                        begin: Alignment
                                                            .centerLeft,
                                                        end: Alignment
                                                            .centerRight,
                                                        colors: <Color>[
                                                          ColorConstants()
                                                              .gradientLeft(),
                                                          ColorConstants()
                                                              .gradientRight()
                                                        ]).createShader(bounds);
                                                  },
                                                  child: SvgPicture.asset(
                                                    'assets/images/resume.svg',
                                                    colorFilter:
                                                        ColorFilter.mode(
                                                            ColorConstants()
                                                                .primaryColor()!,
                                                            BlendMode.srcIn),
                                                  ),
                                                ),
                                                SizedBox(width: 6),
                                                ShaderMask(
                                                  blendMode: BlendMode.srcIn,
                                                  shaderCallback:
                                                      (Rect bounds) {
                                                    return LinearGradient(
                                                        begin: Alignment
                                                            .centerLeft,
                                                        end: Alignment
                                                            .centerRight,
                                                        colors: <Color>[
                                                          ColorConstants()
                                                              .gradientLeft(),
                                                          ColorConstants()
                                                              .gradientRight()
                                                        ]).createShader(bounds);
                                                  },
                                                  child: Text(
                                                    portfolioResponse
                                                                    ?.data
                                                                    .resume
                                                                    .first
                                                                    .url
                                                                    .isEmpty ==
                                                                true &&
                                                            portfolioResponse
                                                                    ?.data
                                                                    .resume
                                                                    .first
                                                                    .url
                                                                    .length ==
                                                                0
                                                        ? 'upload_resume'
                                                        : 'view_resume',
                                                    style:
                                                        Styles.bold(size: 12),
                                                  ).tr(),
                                                ),
                                                VerticalDivider(
                                                  color: Colors.black,
                                                  thickness: 2,
                                                ),

                                                //SizedBox(width: 30),
                                                InkWell(
                                                  onTap: () {
                                                    Navigator.push(
                                                        context,
                                                        NextPageRoute(
                                                            VideoResume()));
                                                  },
                                                  child: Row(
                                                    children: [
                                                      ShaderMask(
                                                        blendMode:
                                                            BlendMode.srcIn,
                                                        shaderCallback:
                                                            (Rect bounds) {
                                                          return LinearGradient(
                                                              begin: Alignment
                                                                  .centerLeft,
                                                              end: Alignment
                                                                  .centerRight,
                                                              colors: <Color>[
                                                                ColorConstants()
                                                                    .gradientLeft(),
                                                                ColorConstants()
                                                                    .gradientRight()
                                                              ]).createShader(
                                                              bounds);
                                                        },
                                                        child: SvgPicture.asset(
                                                          'assets/images/ar_on_you.svg',
                                                          colorFilter:
                                                              ColorFilter.mode(
                                                                  ColorConstants()
                                                                      .primaryColor()!,
                                                                  BlendMode
                                                                      .srcIn),
                                                          height: 22,
                                                          width: 22,
                                                        ),
                                                      ),
                                                      SizedBox(width: 6),
                                                      ShaderMask(
                                                        blendMode:
                                                            BlendMode.srcIn,
                                                        shaderCallback:
                                                            (Rect bounds) {
                                                          return LinearGradient(
                                                              begin: Alignment
                                                                  .centerLeft,
                                                              end: Alignment
                                                                  .centerRight,
                                                              colors: <Color>[
                                                                ColorConstants()
                                                                    .gradientLeft(),
                                                                ColorConstants()
                                                                    .gradientRight()
                                                              ]).createShader(
                                                              bounds);
                                                        },
                                                        child: Text(
                                                          'view_video_resume',
                                                          style: Styles.bold(
                                                              size: 12),
                                                        ).tr(),
                                                      ),
                                                    ],
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                        ],
                                      )
                                    : Center(
                                        child: Container(
                                          //width: MediaQuery.of(context).size.width * 1.0,
                                          height: 30,
                                          color: ColorConstants.WHITE,

                                          child: Shimmer.fromColors(
                                            baseColor: Color(0xffe6e4e6),
                                            highlightColor: Color(0xffeaf0f3),
                                            child: Container(
                                                margin:
                                                    EdgeInsets.only(left: 2),
                                                width: 150,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.all(
                                                          Radius.circular(10)),
                                                  color: Colors.white,
                                                )),
                                          ),
                                        ),
                                      ),
                              )
                            : SizedBox(),
                        Container(
                            child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                              // dividerLine(),
                              Container(
                                color: Color(0xffF2F2F2),
                                width: double.infinity,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 10),
                                height: height(context) * 0.1,
                                child: Container(
                                  height: height(context) * 0.06,
                                  margin: EdgeInsets.only(
                                      top: 8, bottom: 8, right: 10, left: 10),
                                  decoration: BoxDecoration(
                                      color: ColorConstants.WHITE,
                                      borderRadius: BorderRadius.circular(30)),
                                  child: Row(
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            left: 10.0, right: 10.0),
                                        child: Text('open_to_work',
                                                style:
                                                    Styles.semibold(size: 14))
                                            .tr(),
                                      ),
                                      Spacer(),
                                      SizedBox(
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.15,
                                        child: Transform.scale(
                                          scale: 0.8,
                                          child: CupertinoSwitch(
                                            activeTrackColor: Color(0xff3EBDA0),
                                            value: _switchValue,
                                            onChanged: (value) {
                                              if (widget.userId == null)
                                                setState(() {
                                                  _switchValue = value;
                                                  BlocProvider.of<HomeBloc>(
                                                          context)
                                                      .add(OpenToWorkEvent(
                                                          openToWork:
                                                              value ? 1 : 0));
                                                });
                                            },
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              //TODO: Job Matching Job Potfolio

                              Preference.getString(Preference
                                              .LEARNERDASHBOARDJOBPORTFOLIO) ==
                                          '1' &&
                                      widget.expJobResume == true
                                  ? Container(
                                      color: Colors.white,
                                      height: 70,
                                      margin: EdgeInsets.only(
                                          top: 0.0, bottom: 0.0),
                                      child: Padding(
                                        padding: const EdgeInsets.all(15.0),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                    '${portfolioResponse?.data.resumeParserDataCount ?? 0}${(portfolioResponse?.data.resumeParserDataCount ?? 0) == 0 ? '' : '+ '}',
                                                    style: Styles.bold(
                                                        color: ColorConstants
                                                            .BLACK,
                                                        size: 17)),
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 2.0),
                                                  child: Row(
                                                    children: [
                                                      Text('jobs',
                                                              style: Styles
                                                                  .textRegular(
                                                                      color: ColorConstants
                                                                          .BLACK,
                                                                      size: 14))
                                                          .tr(),
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                left: 3.0,
                                                                right: 3.0),
                                                        child: Text(
                                                                'matching_your_profile',
                                                                style: Styles
                                                                    .textRegular(
                                                                        color: ColorConstants
                                                                            .BLACK,
                                                                        size:
                                                                            14))
                                                            .tr(),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                Spacer(),
                                                InkWell(
                                                  onTap: () {
                                                    // Navigator.push(
                                                    //     context,
                                                    //     MaterialPageRoute(
                                                    //         builder: (context) =>
                                                    //             TermsAndCondition(
                                                    //               url: '${APK_DETAILS['explore-jobs-webview_url']}' +
                                                    //                   Preference.getInt(
                                                    //                           Preference.USER_ID)
                                                    //                       .toString(),
                                                    //               title: tr(
                                                    //                   'explore_jobs'),
                                                    //             ),
                                                    //         maintainState:
                                                    //             false));
                                                    Navigator.push(
                                                        context,
                                                        NextPageRoute(
                                                            ExploreJobListPage(
                                                              indexNo: null,
                                                            ),
                                                            isMaintainState:
                                                                true));
                                                  },
                                                  child: Row(
                                                    children: [
                                                      Text('explore_jobs',
                                                              style: Styles.bold(
                                                                  color: ColorConstants
                                                                      .PRIMARY_COLOR,
                                                                  size: 15))
                                                          .tr(),
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                left: 2.0),
                                                        child: Icon(
                                                            Icons
                                                                .arrow_forward_ios_rounded,
                                                            size: 14,
                                                            color: ColorConstants
                                                                .PRIMARY_COLOR),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    )
                                  : SizedBox(),

                              dividerLine(),
                              Container(
                                  color: Colors.white,
                                  margin: EdgeInsets.only(bottom: 8),
                                  child: MySkill(
                                      showAddButton: widget.userId == null)),
                              dividerLine(),
                              Container(
                                padding: const EdgeInsets.only(
                                    left: 4, right: 4, top: 8, bottom: 4),
                                color: ColorConstants.WHITE,
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: width(context) * 0.4,
                                      child: Text(
                                        'portfolio',
                                        style: Styles.semibold(size: 16),
                                      ).tr(),
                                    ),
                                    Spacer(),
                                    if (widget.userId == null)
                                      InkWell(
                                          onTap: (() {
                                            Navigator.push(
                                                    context,
                                                    PageTransition(
                                                        duration: Duration(
                                                            milliseconds: 350),
                                                        reverseDuration:
                                                            Duration(
                                                                milliseconds:
                                                                    600),
                                                        type: PageTransitionType
                                                            .bottomToTop,
                                                        child: AddPortfolio()))
                                                .then(
                                                    (value) => getPortfolio());
                                          }),
                                          child: Icon(Icons.add)),
                                    SizedBox(
                                      width: 8,
                                    ),
                                    if (isPortfolioLoading == false &&
                                        portfolioResponse
                                                ?.data.portfolio.length !=
                                            0)
                                      IconButton(
                                          onPressed: () {
                                            if (portfolioResponse
                                                    ?.data.portfolio.length !=
                                                0)
                                              Navigator.push(
                                                  context,
                                                  NextPageRoute(PortfolioList(
                                                      baseUrl: portfolioResponse
                                                          ?.data.baseFileUrl,
                                                      portfolioList:
                                                          portfolioResponse
                                                              ?.data
                                                              .portfolio)));
                                          },
                                          icon: Icon(
                                              Icons.arrow_forward_ios_rounded)),
                                  ],
                                ),
                              ),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8),
                                child: Divider(),
                              ),
                              isPortfolioLoading == false
                                  ? SizedBox(
                                      height: portfolioResponse
                                                  ?.data.portfolio.length !=
                                              0
                                          ? MediaQuery.of(context).size.height *
                                              0.38
                                          : MediaQuery.of(context).size.height *
                                              0.3,
                                      child: portfolioResponse
                                                  ?.data.portfolio.length !=
                                              0
                                          ? ListView.builder(
                                              controller: new ScrollController(
                                                  keepScrollOffset: true),
                                              itemCount: min(
                                                  4,
                                                  int.tryParse(
                                                          '${portfolioResponse?.data.portfolio.length}') ??
                                                      0),
                                              scrollDirection: Axis.horizontal,
                                              itemBuilder: (context, index) {
                                                return InkWell(
                                                  onTap: () {
                                                    Navigator.push(
                                                        context,
                                                        NextPageRoute(
                                                            PortfolioDetail(
                                                          baseUrl:
                                                              '${portfolioResponse!.data.baseFileUrl}',
                                                          portfolio:
                                                              portfolioResponse!
                                                                      .data
                                                                      .portfolio[
                                                                  index],
                                                        )));
                                                  },
                                                  child: Container(
                                                    width:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .width *
                                                            0.8,
                                                    margin:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8,
                                                            vertical: 4),
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        ClipRRect(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(12),
                                                          child:
                                                              CachedNetworkImage(
                                                            progressIndicatorBuilder:
                                                                (context, url,
                                                                    downloadProgress) {
                                                              return Shimmer
                                                                  .fromColors(
                                                                baseColor:
                                                                    Colors.grey[
                                                                        300]!,
                                                                highlightColor:
                                                                    Colors.grey[
                                                                        100]!,
                                                                enabled: true,
                                                                child:
                                                                    Container(
                                                                  width: MediaQuery.of(
                                                                              context)
                                                                          .size
                                                                          .width *
                                                                      0.8,
                                                                  height: MediaQuery.of(
                                                                              context)
                                                                          .size
                                                                          .height *
                                                                      0.3,
                                                                  color: Colors
                                                                      .grey,
                                                                ),
                                                              );
                                                            },
                                                            imageUrl:
                                                                '${portfolioResponse?.data.baseFileUrl}${portfolioResponse?.data.portfolio[index].imageName}',
                                                            width: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .width *
                                                                0.8,
                                                            height: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .height *
                                                                0.3,
                                                            fit: BoxFit.cover,
                                                            errorWidget:
                                                                (context, url,
                                                                    error) {
                                                              return Container(
                                                                width: MediaQuery.of(
                                                                            context)
                                                                        .size
                                                                        .width *
                                                                    0.8,
                                                                height: MediaQuery.of(
                                                                            context)
                                                                        .size
                                                                        .height *
                                                                    0.3,
                                                                padding:
                                                                    EdgeInsets
                                                                        .all(
                                                                            14),
                                                                decoration: BoxDecoration(
                                                                    color: Color(
                                                                        0xffD5D5D5)),
                                                              );
                                                            },
                                                          ),
                                                        ),
                                                        SizedBox(height: 8),
                                                        SizedBox(
                                                            width: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .width *
                                                                0.8,
                                                            child: Text(
                                                              '${portfolioResponse?.data.portfolio[index].portfolioTitle}',
                                                              softWrap: true,
                                                              maxLines: 1,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              style: Styles.bold(
                                                                  size: 14,
                                                                  lineHeight:
                                                                      1.3,
                                                                  color: Color(
                                                                      0xff0E1638)),
                                                            )),
                                                        SizedBox(height: 4),
                                                        SizedBox(
                                                          width: MediaQuery.of(
                                                                      context)
                                                                  .size
                                                                  .width *
                                                              0.8,
                                                          child: Text(
                                                              '${portfolioResponse?.data.portfolio[index].desc}',
                                                              softWrap: true,
                                                              maxLines: 1,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              style: Styles.regular(
                                                                  lineHeight:
                                                                      1.3,
                                                                  size: 12,
                                                                  color: Color(
                                                                      0xff929BA3))),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              })
                                          : portfolioListShimmer(0),
                                    )
                                  : portfolioListShimmer(1),
                              dividerLine(),
                            ])),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            children: [
                              Text(
                                'competitions',
                                style: Styles.bold(size: 16),
                              ).tr(),
                              Spacer(),
                              if (isPortfolioLoading == false &&
                                  competition?.data.length != 0)
                                IconButton(
                                  onPressed: () {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                CompetitionListPortfolio(
                                                    competitionList:
                                                        competition?.data)));
                                  },
                                  icon: Icon(Icons.arrow_forward_ios_outlined),
                                ),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 4),
                                child: Divider(),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Divider(),
                        ),
                        isPortfolioLoading == false
                            ? Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                height: competition?.data.length != 0
                                    ? height(context) * 0.3
                                    : height(context) * 0.15,
                                child: competition?.data.length != 0
                                    ? ListView.builder(
                                        itemCount: min(
                                            4,
                                            int.parse(
                                                '${competition?.data.length ?? 0}')),
                                        scrollDirection: Axis.horizontal,
                                        itemBuilder: (context, index) =>
                                            InkWell(
                                              onTap: () {},
                                              child: Container(
                                                width: width(context) * 0.7,
                                                padding:
                                                    EdgeInsets.only(bottom: 8),
                                                margin:
                                                    EdgeInsets.only(right: 8),
                                                decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                    border: Border.all(
                                                        color: ColorConstants
                                                            .GREY_4)),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.only(
                                                              topLeft: Radius
                                                                  .circular(8),
                                                              topRight: Radius
                                                                  .circular(8)),
                                                      child: CachedNetworkImage(
                                                          imageUrl:
                                                              '${competition?.data[index].pImage}',
                                                          width:
                                                              double.infinity,
                                                          height: MediaQuery.of(
                                                                      context)
                                                                  .size
                                                                  .height *
                                                              0.2,
                                                          errorWidget: (context,
                                                                  url, error) =>
                                                              SizedBox(),
                                                          fit: BoxFit.cover),
                                                    ),
                                                    SizedBox(height: 10),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              left: 8.0,
                                                              right: 8.0),
                                                      child: SizedBox(
                                                        width: width(context) *
                                                            0.84,
                                                        child: Text(
                                                          '${competition?.data[index].pName}',
                                                          softWrap: true,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                          style: Styles.bold(),
                                                        ),
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 6,
                                                    ),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              left: 8.0,
                                                              right: 8.0),
                                                      child: Row(
                                                        children: [
                                                          Text(
                                                              '${tr('rank')} : ${competition?.data[index].rank ?? 0}',
                                                              style: Styles.semibold(
                                                                  size: 12,
                                                                  color: Color(
                                                                      0xff929BA3))),
                                                          SizedBox(width: 8),
                                                          Text(' •  ',
                                                              style: Styles.semibold(
                                                                  size: 12,
                                                                  color: Color(
                                                                      0xff929BA3))),
                                                          SvgPicture.asset(
                                                            'assets/images/coin.svg',
                                                            width:
                                                                width(context) *
                                                                    0.03,
                                                          ),
                                                          Text(
                                                              '  ${competition?.data[index].gScore ?? 0} ${tr('point_earned')}',
                                                              style: Styles.semibold(
                                                                  size: 12,
                                                                  color: ColorConstants
                                                                      .ORANGE)),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ))
                                    : competitionListShimmer(0),
                              )
                            : competitionListShimmer(1),
                        SizedBox(
                          height: 20,
                        ),
                        if (APK_DETAILS['package_name'] ==
                            'com.singulariswow.mec') ...[
                          myEventsParticipation(),
                          SizedBox(
                            height: 20,
                          ),
                        ],
                        topRow(tr('education'),
                            showArrow:
                                portfolioResponse?.data.education.length != 0,
                            arrowAction: () {
                          if (portfolioResponse?.data.education.length != 0)
                            Navigator.push(
                                context,
                                NextPageRoute(EducationList(
                                  baseUrl: portfolioResponse?.data.baseFileUrl,
                                  education: portfolioResponse?.data.education
                                      as List<CommonProfession>,
                                )));
                        }, addAction: () async {
                          await Navigator.push(
                                  context,
                                  PageTransition(
                                      duration: Duration(milliseconds: 350),
                                      reverseDuration:
                                          Duration(milliseconds: 350),
                                      type: PageTransitionType.bottomToTop,
                                      child: AddEducation()))
                              .then((value) => getPortfolio());
                        }),
                        isPortfolioLoading == false
                            ? portfolioResponse?.data.education != null
                                ? educationList(portfolioResponse
                                    ?.data.education as List<CommonProfession>)
                                : educationListShimmer(1)
                            : educationListShimmer(1),
                        dividerLine(),
                        getCertificateWidget(
                            portfolioResponse?.data.certificate, context),
                        dividerLine(),
                        getExperience(
                            portfolioResponse?.data.experience, context),
                        dividerLine(),
                        getRecentActivites(
                            portfolioResponse?.data.recentActivity, context),
                        dividerLine(),
                        getExtraActivitesWidget(
                            portfolioResponse?.data.extraActivities, context),
                      ],
                    )),
              ))),
    );
  }

  Future<void> _shareQrCode(String qrCodeUrl) async {
    try {
      // Download the image
      final response = await http.get(Uri.parse(qrCodeUrl));
      if (response.statusCode == 200) {
        final documentDirectory = await getApplicationDocumentsDirectory();
        final file = File('${documentDirectory.path}/qr_code.png');
        file.writeAsBytesSync(response.bodyBytes);

        // Create XFile from file path
        final xFile = XFile(file.path);

        // Share the XFile
        await Share.shareXFiles([xFile]);
      } else {
        throw Exception('Failed to load image');
      }
    } catch (e) {
      // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      //   content: Text('Failed to share QR code: $e'),
      // ));
    }
  }

  Widget showQRViewDialog() {
    String? baseUrl = '${APK_DETAILS['domain_url']}/portfolio/';
    return Column(children: [
      Stack(children: [
        Container(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            margin: EdgeInsets.only(
              top: 25,
              left: 12,
              right: 12,
            ),
            decoration: BoxDecoration(
              color: ColorConstants.GREY,
              borderRadius: BorderRadius.circular(6.0),
            ),
            child: Padding(
              padding: const EdgeInsets.only(top: 30.0),
              child: Column(children: [
                SizedBox(
                  width: width(context) * 0.5,
                  child: Center(
                    child: Text(
                      Utility().decrypted128(
                          '${Preference.getString(Preference.FIRST_NAME)}'),
                      //Utility().decrypted128('${Preference.getString(Preference.FIRST_NAME)?[0].toUpperCase()} ${Preference.getString(Preference.FIRST_NAME)?.substring(1).toLowerCase()}'),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: Styles.bold(color: ColorConstants.BLACK, size: 14),
                    ),
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 50, vertical: 10),
                  child: Container(
                    decoration: BoxDecoration(
                      color: ColorConstants.ACCENT_COLOR,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    height: 200,
                    width: 200,
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(0),
                        child: CachedNetworkImage(
                          imageUrl: '${portfolioResponse?.data.qrCode}',
                          width: MediaQuery.of(context).size.width * 0.08,
                          height: MediaQuery.of(context).size.height * 0.08,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) {
                            return Container(
                              width: MediaQuery.of(context).size.width * 0.04,
                              height: MediaQuery.of(context).size.height * 0.04,
                              padding: EdgeInsets.all(14),
                              decoration:
                                  BoxDecoration(color: Color(0xffD5D5D5)),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ]),
            )),
        Positioned(
          left: 130,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(200),
            child: SizedBox(
              height: 60,
              width: 60,
              child: CachedNetworkImage(
                imageUrl: '${Preference.getString(Preference.PROFILE_IMAGE)}',
                placeholder: (context, url) => SvgPicture.asset(
                  'assets/images/default_user.svg',
                  width: 40,
                  height: 40,
                  colorFilter: ColorFilter.mode(
                      ColorConstants.PRIMARY_BLUE, BlendMode.srcIn),
                ),
                errorWidget: (context, url, error) => SvgPicture.asset(
                  'assets/images/default_user.svg',
                  width: 40,
                  height: 40,
                  colorFilter: ColorFilter.mode(
                      ColorConstants.PRIMARY_BLUE, BlendMode.srcIn),
                ),
              ),
            ),
          ),
        ),
      ]),
      SizedBox(height: 10),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 50.0),
        child: Transform.rotate(
          angle: Utility().isRTL(context) ? -math.pi : 0,
          child: Container(
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  InkWell(
                    onTap: () {
                      if (portfolioResponse?.data.qrCode != null) {
                        _shareQrCode('${portfolioResponse?.data.qrCode}');
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                          content: Text('no_qr_found').tr(),
                        ));
                      }
                    },
                    child: SvgPicture.asset(
                      'assets/images/share_icon.svg',
                      width: 24,
                      height: 24,
                    ),
                  ),
                  Container(
                      padding: EdgeInsets.only(left: 4),
                      height: 40,
                      width: 2,
                      color: ColorConstants.GREY),
                  InkWell(
                    onTap: () async {
                      String shareUrl = '${baseUrl.split('/portfolio').first}/' +
                          'portfolio-detail?user_id=${Preference.getInt(Preference.USER_ID)}';

                      print("sort url ");
                      await Utility.shortLink(shareUrl).then((value) {
                        setState(() {
                          shareUrl = value;
                        });
                      });

                      Share.share(shareUrl);
                    },
                    child: SvgPicture.asset(
                      'assets/images/copy_link.svg',
                      width: 24,
                      height: 24,
                    ),
                  ),
                ]),
          ),
        ),
      ),
      Transform.rotate(
        angle: Utility().isRTL(context) ? -math.pi : 0,
        child: Container(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 75.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: Text(
                    'share',
                    style: Styles.textSemiBold(size: 12),
                    textDirection: ui.TextDirection.ltr,
                  ).tr(),
                ),
                // SizedBox(width: 110),
                Spacer(),
                Text(
                  'copy_link',
                  style: Styles.textSemiBold(size: 12),
                  textDirection: ui.TextDirection.ltr,
                ).tr(),
              ],
            ),
          ),
        ),
      )
    ]);
  }

  Future<void> downloadQR({String? url}) async {
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    late AndroidDeviceInfo android;
    try {
      android = await plugin.androidInfo;
    } catch (e) {
      Log.v("exception file download $e");
    }
    // return;
    String localPath;

    final status = await Permission.storage.request();
    if (Platform.isIOS ||
        status.isGranted ||
        android.version.sdkInt >= 33 ||
        await Permission.storage.request().isGranted) {
      //  final externalDir = await getExternalStorageDirectory();
      final status = await Permission.storage.status;

      if (Platform.isAndroid) {
        localPath = "/sdcard/download/";
      } else {
        localPath = (await getApplicationDocumentsDirectory()).path;
      }
      final file = File("$localPath/${url?.split('/').last}");
      if (!file.existsSync()) {
        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
          content: Text('Downloading Start'),
        ));

        final id = await FlutterDownloader.enqueue(
          url: url!,
          savedDir: localPath,
          showNotification: true,
          openFileFromNotification: true,
        ).then((value) async {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Successfully Downloaded'),
          ));

          OpenFilex.open("$localPath/${url.split('/').last}");
        });
      } else {
        Utility.showSnackBar(scaffoldContext: context, message: 'file exists');
        OpenFilex.open("$localPath/${url?.split('/').last}");
      }
    } else {
      launchUrl(Uri.parse(url!), mode: LaunchMode.externalApplication);
      Log.v('Permission Denied');
    }
    return;
  }

  Widget getRecentActivites(List<RecentActivity>? recentActivites, context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        children: [
          topRow(tr('recent_activities'),
              showArrow: recentActivites?.length != 0,
              addAction: () {}, arrowAction: () {
            if (recentActivites?.length != 0)
              Navigator.push(
                  context,
                  NextPageRoute(RecentActivitiesPage(),
                      isMaintainState: false));
          }, showAddButton: false),
          isPortfolioLoading == false
              ? recentActivites?.length != 0 && recentActivites != null
                  ? SizedBox(
                      height: height(context) * 0.515,
                      child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: min(4, recentActivites.length),
                          itemBuilder: (context, index) {
                            return postCard(recentActivites[index]);
                          }),
                    )
                  : recentActivitiesListShimmer(0)
              : recentActivitiesListShimmer(1),
        ],
      ),
    );
  }

  Widget postCard(RecentActivity recentActivites) {
    // var now = DateTime.now();
    // var past = DateTime.parse("${recentActivites.createdAt}");

    // var millis = int.parse(recentActivites.createdAtTs.toString());
    // DateTime date = DateTime.fromMillisecondsSinceEpoch(
    //   millis * 1000,
    // );

    return InkWell(
      onTap: () {
        Navigator.push(
            context,
            NextPageRoute(
                RecentActivitiesPage(
                  animateToIndex: recentActivites.reserved != 'reels' ? 0 : 1,
                ),
                isMaintainState: false));
      },
      child: Container(
        width: width(context) * 0.75,
        height: height(context) * 0.35,
        padding: EdgeInsets.symmetric(horizontal: 12),
        margin: EdgeInsets.all(4),
        decoration: BoxDecoration(
            border: recentActivites.reserved == 'reels'
                ? null
                : Border.all(color: ColorConstants.DIVIDER),
            borderRadius: BorderRadius.circular(8),
            color: ColorConstants.WHITE),
        child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (recentActivites.reserved != 'reels')
                SizedBox(
                  height: 20,
                ),
              if (recentActivites.reserved != 'reels')
                Row(
                  children: [
                    ClipOval(
                      child: Image.network(
                        '${recentActivites.profileImage}',
                        errorBuilder: (context, url, error) {
                          return SvgPicture.asset(
                            'assets/images/default_user.svg',
                            colorFilter: APK_DETAILS['gradient_icon'] == '0'
                                ? ColorFilter.mode(
                                    ColorConstants().primaryColor()!,
                                    BlendMode.srcIn)
                                : null,
                            width: width(context) * 0.13,
                            height: width(context) * 0.13,
                            allowDrawingOutsideViewBox: true,
                          );
                        },
                        width: width(context) * 0.13,
                        height: width(context) * 0.13,
                      ),
                    ),
                    SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: width(context) * 0.5,
                          child: Text(
                            '${Utility().decrypted128('${recentActivites.name}')}',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        DateToTimeago(time: recentActivites.createdAt!),
                        // Text(
                        //     '${Utility().calculateTimeDifferenceBetween(DateTime.parse(date.toString().substring(0, 19)), now, context)}')
                        // Text(
                        //     '${Utility().calculateTimeDifferenceBetween(DateTime.parse(past.toString().substring(0, 19)), now, context)}')
                      ],
                    ),
                  ],
                ),
              SizedBox(
                height: height(context) * 0.01,
              ),
              recentActivites.reserved != 'reels'
                  ? recentActivites.resourcePath!.contains('png') ||
                          recentActivites.resourcePath!.contains('jpeg') ||
                          recentActivites.resourcePath!.contains('jpg')
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            '${recentActivites.resourcePath}',
                            width: width(context),
                            height: height(context) * 0.300,
                            fit: BoxFit.fill,
                          ))
                      : Container(
                          height: recentActivites.reserved != 'reels'
                              ? height(context) * 0.33
                              : height(context) * 0.43,
                          child: VideoPlayerWidget(
                            videoUrl: '${recentActivites.resourcePath}',
                          ),
                        )
                  : SizedBox(
                      height: height(context) * 0.488,
                      child: Stack(
                        children: [
                          ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: CachedNetworkImage(
                                  width: width(context) * 0.65,
                                  height: height(context) * 0.488,
                                  fit: BoxFit.cover,
                                  imageUrl:
                                      '${recentActivites.resourcePathThumbnail}',
                                  errorWidget: (context, url, error) =>
                                      CreateThumnail(
                                        path: recentActivites
                                            .resourcePathThumbnail,
                                      ))),
                          Center(
                            child: SvgPicture.asset(
                              'assets/images/play.svg',
                              height: 40.0,
                              width: 40.0,
                              allowDrawingOutsideViewBox: true,
                            ),
                          ),
                          Positioned(
                              bottom: 10,
                              left: 10,
                              child: Text(
                                  '${recentActivites.viewCount} ${tr('views')}',
                                  style: Styles.regular(
                                      size: 12, color: ColorConstants.WHITE)))
                        ],
                      ),
                    ),
              if (recentActivites.reserved != 'reels')
                SizedBox(
                    //singh
                    height: 80,
                    child: Column(
                      children: [
                        SizedBox(
                          height: 6,
                        ),
                        /*Text(
                          '${recentActivites.description ?? ''}',
                          style: Styles.regular(size: 14),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),*/
                        Padding(
                            padding: recentActivites.description != null
                                ? const EdgeInsets.only(
                                    bottom: 7, left: 10, right: 10, top: 13)
                                : const EdgeInsets.only(
                                    bottom: 0, left: 10, right: 10, top: 0),
                            child: ReadMoreText(
                                // height: height(context) * 0.4,
                                text: '${recentActivites.description ?? ''}')),
                      ],
                    )),
            ]),
      ),
    );
  }

  Widget getCertificateWidget(
      List<CommonProfession>? certificateList, context) {
    certificateList?.sort((b, a) => a.startDate.compareTo(b.startDate));
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          topRow(tr('certificates'), showArrow: certificateList?.length != 0,
              arrowAction: () {
            if (certificateList?.length != 0)
              Navigator.push(
                  context,
                  NextPageRoute(CertificateList(
                      baseUrl: '${portfolioResponse?.data.baseFileUrl}',
                      certificates: certificateList)));
          }, addAction: () async {
            await Navigator.push(
                    context,
                    PageTransition(
                        duration: Duration(milliseconds: 350),
                        reverseDuration: Duration(milliseconds: 350),
                        type: PageTransitionType.bottomToTop,
                        child: AddCertificate()))
                .then((value) => getPortfolio());
          }),
          isPortfolioLoading == false
              ? Container(
                  padding: EdgeInsets.all(8),
                  margin: EdgeInsets.only(right: 10),
                  height: certificateList?.length != 0
                      ? height(context) * 0.34
                      : height(context) * 0.15,
                  child: certificateList?.length != 0 && certificateList != null
                      ? ListView.builder(
                          itemCount: certificateList.length,
                          scrollDirection: Axis.horizontal,
                          itemBuilder: (context, index) {
                            String startDateString =
                                "${portfolioResponse?.data.certificate[index].startDate}";
                            DateTime startDate =
                                DateFormat("yyyy-MM-dd", 'en_IN')
                                    .parse(startDateString);
                            return InkWell(
                              onTap: () {
                                showModalBottomSheet(
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(20)),
                                    context: context,
                                    enableDrag: true,
                                    isScrollControlled: true,
                                    builder: (context) {
                                      return FractionallySizedBox(
                                        heightFactor: 0.9,
                                        child: CachedNetworkImage(
                                          imageUrl:
                                              '${portfolioResponse?.data.baseFileUrl}${certificateList[index].imageName}',
                                          width: double.infinity,
                                          height: double.infinity,
                                          errorWidget: (context, url, data) =>
                                              Image.asset(
                                            "assets/images/certificate_dummy.png",
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      );
                                    });
                              },
                              child: Container(
                                margin: EdgeInsets.only(right: 10),
                                width: width(context) * 0.7,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(10),
                                      child: SizedBox(
                                        width: width(context) * 0.7,
                                        height: width(context) * 0.45,
                                        child: CachedNetworkImage(
                                          width: width(context) * 0.7,
                                          height: width(context) * 0.45,
                                          imageUrl:
                                              '${portfolioResponse?.data.baseFileUrl}${certificateList[index].imageName}',
                                          fit: BoxFit.cover,
                                          errorWidget: (context, url, data) =>
                                              Image.asset(
                                            "assets/images/certificate_dummy.png",
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 8,
                                    ),
                                    Text('${certificateList[index].title}',
                                        maxLines: 2,
                                        style: Styles.bold(size: 14)),
                                    if (!Utility().isRTL(context))
                                      SizedBox(
                                        height: 8,
                                      ),
                                    Text(
                                      '${listOfMonths[startDate.month - 1].substring(0, 3)} ${startDate.year.toString().substring(2, 4)}',
                                      style: Styles.regular(),
                                      textDirection: ui.TextDirection.ltr,
                                    ),
                                  ],
                                ),
                              ),
                            );
                          })
                      : certificatesListShimmer(0),
                )
              : certificatesListShimmer(1)
        ],
      ),
    );
  }

  Widget getExperience(List<CommonProfession>? experience, context) {
    List<CommonProfession> l1 = [], l2 = [];
    if (experience?.length != 0) {
      for (int i = 0; i < int.parse('${experience?.length ?? 0}'); i++) {
        if (experience?[i].currentlyWorkHere == 'true' ||
            experience?[i].currentlyWorkHere == 'on')
          l1.insert(0, experience![i]);
        else
          l2.insert(0, experience![i]);
      }
      l2.sort((a, b) => b.endDate.compareTo(a.startDate));

      experience = l1 + l2;
    }
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          topRow(tr('experience'), showArrow: experience?.length != 0,
              arrowAction: () {
            if (experience?.length != 0)
              Navigator.push(
                      context,
                      PageTransition(
                          duration: Duration(milliseconds: 350),
                          reverseDuration: Duration(milliseconds: 350),
                          type: PageTransitionType.bottomToTop,
                          child: ExperienceList(
                              baseUrl: portfolioResponse?.data.baseFileUrl,
                              experience: experience)))
                  .then((value) => getPortfolio());
          }, addAction: () {
            Navigator.push(
                    context,
                    PageTransition(
                        duration: Duration(milliseconds: 350),
                        reverseDuration: Duration(milliseconds: 350),
                        type: PageTransitionType.bottomToTop,
                        child: AddExperience()))
                .then((value) => getPortfolio());
          }),
          isPortfolioLoading == false
              ? Container(
                  padding: EdgeInsets.all(8),
                  child: experience?.length != 0
                      ? ListView.builder(
                          itemCount: min(2, int.parse('${experience?.length}')),
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            String startDateString =
                                "${experience?[index].startDate}";
                            DateTime startDate =
                                DateFormat("yyyy-MM-dd").parse(startDateString);

                            DateTime endDate = DateTime.now();
                            if (experience?[index].endDate != '') {
                              String endDateString =
                                  "${experience?[index].endDate}";
                              endDate =
                                  DateFormat("yyyy-MM-dd").parse(endDateString);
                            }

                            return Transform.translate(
                              offset: Offset(0, -10),
                              child: Container(
                                margin: EdgeInsets.only(right: 10),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          child: SizedBox(
                                            width: width(context) * 0.2,
                                            height: width(context) * 0.2,
                                            child: CachedNetworkImage(
                                              imageUrl:
                                                  '${portfolioResponse?.data.baseFileUrl}${experience?[index].imageName}',
                                              fit: BoxFit.cover,
                                              errorWidget:
                                                  (context, url, data) =>
                                                      Image.asset(
                                                "assets/images/certificate_dummy.png",
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          width: 10,
                                        ),
                                        SizedBox(
                                          width: width(context) * 0.6,
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                '${experience?[index].title}',
                                                style: Styles.bold(size: 14),
                                              ),
                                              SizedBox(
                                                height: 10,
                                              ),
                                              Text(
                                                '${experience?[index].institute}',
                                                style: Styles.regular(size: 12),
                                              ),
                                              SizedBox(
                                                height: 10,
                                              ),
                                              experience?[index]
                                                              .currentlyWorkHere ==
                                                          'true' ||
                                                      experience?[index]
                                                              .currentlyWorkHere ==
                                                          'on'
                                                  ? Text(
                                                      '${tr('${experience?[index].employmentType}')} •  ${listOfMonths[startDate.month - 1].substring(0, 3)} ${startDate.year.toString().substring(2, 4)}  -  ${tr('present')}',
                                                      style: Styles.regular(
                                                          size: 12),
                                                      textDirection:
                                                          ui.TextDirection.ltr,
                                                    )
                                                  : Text(
                                                      '${tr('${experience?[index].employmentType}')} • ${calculateTimeDifferenceBetween(startDate, endDate)} • ${listOfMonths[startDate.month - 1].substring(0, 3)} ${startDate.year.toString().substring(2, 4)}  -  ' +
                                                          ' ${listOfMonths[endDate.month - 1].substring(0, 3)} ${endDate.year.toString().substring(2, 4)}',
                                                      style: Styles.regular(
                                                          size: 12),
                                                      textDirection:
                                                          ui.TextDirection.ltr,
                                                    )
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                    SizedBox(
                                      height: 8,
                                    ),
                                    ReadMoreText(
                                      text: '${experience?[index].description}',
                                      color: ColorConstants.GREY_3,
                                    ),
                                    if (index + 1 !=
                                        min(2,
                                            int.parse('${experience?.length}')))
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 12),
                                        child: Divider(),
                                      )
                                  ],
                                ),
                              ),
                            );
                          })
                      : experienceListShimmer(0),
                )
              : experienceListShimmer(1)
        ],
      ),
    );
  }

  Widget getExtraActivitesWidget(
      List<CommonProfession>? extraActivities, context) {
    extraActivities?.sort((a, b) => b.startDate.compareTo(a.startDate));

    return Container(
      color: Colors.white,
      child: Column(
        children: [
          topRow(tr('extra_curricular'),
              showArrow: extraActivities?.length != 0, arrowAction: () {
            if (extraActivities?.length != 0)
              // Navigator.push(
              //     context,
              //     NextPageRoute());
              Navigator.push(
                  context,
                  PageTransition(
                      duration: Duration(milliseconds: 350),
                      reverseDuration: Duration(milliseconds: 350),
                      type: PageTransitionType.bottomToTop,
                      child: ExtraActivitiesList(
                        baseUrl: '${portfolioResponse?.data.baseFileUrl}',
                        activities: extraActivities!,
                      ))).then((value) => getPortfolio());
          }, addAction: () {
            Navigator.push(
                    context,
                    PageTransition(
                        duration: Duration(milliseconds: 350),
                        reverseDuration: Duration(milliseconds: 350),
                        type: PageTransitionType.bottomToTop,
                        child: AddActivities()))
                .then((value) => getPortfolio());
          }),
          isPortfolioLoading == false
              ? Container(
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  child: extraActivities?.length != 0 && extraActivities != null
                      ? ListView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: min(2, extraActivities.length),
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            String startDateString =
                                "${extraActivities[index].startDate}";

                            DateTime startDate =
                                DateFormat("yyyy-MM-dd", 'en_IN')
                                    .parse(startDateString);

                            return Container(
                              margin: EdgeInsets.only(right: 10),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: index != 0 ? 10 : 6,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: width(context) * 0.2,
                                        height: width(context) * 0.2,
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          child: CachedNetworkImage(
                                            imageUrl:
                                                "${portfolioResponse?.data.baseFileUrl}${extraActivities[index].imageName}",
                                            fit: BoxFit.cover,
                                            progressIndicatorBuilder: (context,
                                                    url, downloadProgress) =>
                                                Shimmer.fromColors(
                                              baseColor: Colors.grey[300]!,
                                              highlightColor: Colors.grey[100]!,
                                              enabled: true,
                                              child: Container(
                                                width: width(context) * 0.2,
                                                height: width(context) * 0.2,
                                                color: Colors.grey,
                                              ),
                                            ),
                                            errorWidget: (context, url,
                                                    error) =>
                                                Container(
                                                    width: width(context) * 0.2,
                                                    height:
                                                        width(context) * 0.2,
                                                    padding: EdgeInsets.all(8),
                                                    decoration: BoxDecoration(
                                                        color: ColorConstants
                                                            .DIVIDER,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8)),
                                                    child: SvgPicture.asset(
                                                      'assets/images/extra.svg',
                                                      colorFilter: APK_DETAILS[
                                                                  'gradient_icon'] ==
                                                              '0'
                                                          ? ColorFilter.mode(
                                                              ColorConstants()
                                                                  .primaryColor()!,
                                                              BlendMode.srcIn)
                                                          : null,
                                                    )),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 6),
                                      Container(
                                        width: width(context) * 0.7,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            Transform.translate(
                                              offset: Offset(0, -3),
                                              child: Text(
                                                '${extraActivities[index].title}',
                                                style: Styles.bold(size: 14),
                                              ),
                                            ),
                                            SizedBox(
                                              height: 4,
                                            ),
                                            Text(
                                              '${extraActivities[index].institute}',
                                              style: Styles.regular(size: 14),
                                            ),
                                            SizedBox(
                                              height: 4,
                                            ),
                                            Row(
                                              children: [
                                                Text(
                                                  '${extraActivities[index].curricularType} • ',
                                                  style:
                                                      Styles.regular(size: 14),
                                                ),
                                                Text(
                                                  '${Utility.ordinal(startDate.day)} ${listOfMonths[startDate.month - 1]} ${startDate.year}',
                                                  style:
                                                      Styles.regular(size: 14),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                  SizedBox(
                                    height: 4,
                                  ),
                                  ReadMoreText(
                                    viewMore: tr('view_more'),
                                    text:
                                        '${extraActivities[index].description}',
                                    color: Color(0xff929BA3),
                                  ),
                                  SizedBox(
                                    height: 10,
                                  ),
                                  if (index != extraActivities.length) Divider()
                                ],
                              ),
                            );
                          })
                      : extraActivitiesListShimmer(0),
                )
              : extraActivitiesListShimmer(1)
        ],
      ),
    );
  }

  Widget dividerLine() {
    return const Divider(
      thickness: 18,
      color: Color(0xffF2F2F2),
    );
  }

  Widget skillProgess(String title, int rating, double percent) {
    String? position;
    String? image = 'assets/images/';
    switch (rating) {
      case 5:
        position = 'LEADER';
        image += 'leader.svg';
        break;

      case 4:
        position = 'EXPERT';
        image += 'expert.svg';
        break;

      case 3:
        position = 'MASTER';
        image += 'master.svg';

        break;

      case 2:
        position = 'Learner';
        image += 'learner.svg';

        break;

      case 1:
        position = 'NOVICE';
        image += 'novice.svg';

        break;
    }
    return Column(
      children: [
        ShaderMask(
          blendMode: BlendMode.srcIn,
          shaderCallback: (Rect bounds) {
            return LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: <Color>[
                  ColorConstants().gradientLeft(),
                  ColorConstants().gradientRight()
                ]).createShader(bounds);
          },
          child: Text(
            title,
            style: Styles.bold(size: 12),
          ),
        ),
        Transform.scale(
          scale: 0.75,
          child: CircularPercentIndicator(
            radius: 50.0,
            circularStrokeCap: CircularStrokeCap.round,
            lineWidth: 5.0,
            percent: percent / 100,
            center: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                      color: Color(0xff000000).withValues(alpha: 0.25),
                      blurRadius: 4.36274)
                ],
                color: ColorConstants.WHITE,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  SizedBox(height: 2),
                  SvgPicture.asset(
                    image,
                    width: 30,
                    height: 30,
                    fit: BoxFit.cover,
                  ),
                  ShaderMask(
                    blendMode: BlendMode.srcIn,
                    shaderCallback: (Rect bounds) {
                      return LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: <Color>[
                            ColorConstants().gradientLeft(),
                            ColorConstants().gradientRight()
                          ]).createShader(bounds);
                    },
                    child: Text(
                      '$position',
                      style: Styles.bold(size: 9.19),
                    ),
                  ),
                  Stack(
                    children: [
                      Transform.translate(
                        offset: Offset(-1, 9),
                        child: SvgPicture.asset(
                          'assets/images/half_circle.svg',
                          colorFilter: APK_DETAILS['gradient_icon'] == '0'
                              ? ColorFilter.mode(
                                  ColorConstants().primaryColor()!,
                                  BlendMode.srcIn)
                              : null,
                          width: 78,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned.fill(
                        child: Align(
                          alignment: Alignment.center,
                          child: SizedBox(
                            width: 60,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                for (int i = 0; i < 5; i++)
                                  Opacity(
                                    opacity: i < rating ? 1 : 0.5,
                                    child: SvgPicture.asset(
                                      'assets/images/star_portfolio_unselected.svg',
                                      colorFilter:
                                          APK_DETAILS['gradient_icon'] == '0'
                                              ? ColorFilter.mode(
                                                  ColorConstants()
                                                      .primaryColor()!,
                                                  BlendMode.srcIn)
                                              : null,
                                      width: 10,
                                      fit: BoxFit.cover,
                                    ),
                                  )
                              ],
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
            backgroundColor: Color(0xffEFEFEF),
            linearGradient: LinearGradient(
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
                colors: <Color>[
                  ColorConstants().gradientLeft(),
                  ColorConstants().gradientRight()
                ]),
          ),
        ),
      ],
    );
  }

  void handlePortfolioState(PortfolioState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PortfolioState Loading...................");
          isPortfolioLoading = true;
          break;
        case ApiStatus.SUCCESS:
          setState(() {
            isPortfolioLoading = false;
          });
          try {
            Log.v(
                "PortfolioStatedone ................... ${portfolioState.response?.toJson()}");
            portfolioResponse = portfolioState.response;
            sampleResumeResp = portfolioState.response!.data.sampleResumes;
            Log.v(
                'sample resume is .........................${portfolioState.response?.data.toJson()}');
            _switchValue =
                portfolioState.response?.data.openToWork == "1" ? true : false;

            //if page for other user don't update value
            if (widget.userId != null) return;

            if ('${portfolioState.response?.data.name}' != '')
              Preference.setString(Preference.FIRST_NAME,
                  '${portfolioState.response?.data.name}');
            if (portfolioState.response?.data.image.contains(
                    '${Preference.getString(Preference.PROFILE_IMAGE)}') ==
                true) {
              Preference.setString(Preference.PROFILE_IMAGE,
                  '${portfolioState.response?.data.image}');
            }

            if (portfolioResponse?.data.portfolioSocial != null &&
                portfolioResponse?.data.portfolioSocial != null) {
              Preference.setString(Preference.USER_EMAIL,
                  '${portfolioResponse?.data.portfolioSocial.first['email']}');
              Preference.setString(Preference.PHONE,
                  '${portfolioResponse?.data.portfolioSocial.first['mob_num']}');
            }

            if ('${portfolioState.response?.data.image}' != '')
              Preference.setString(Preference.PROFILE_IMAGE,
                  '${portfolioState.response?.data.image}');

            Preference.setString(Preference.PROFILE_VIDEO,
                '${portfolioState.response?.data.profileVideo}');

            Preference.setInt(Preference.PROFILE_PERCENT,
                portfolioState.response!.data.profileCompletion);

            if (portfolioState.response?.data.resume != null &&
                portfolioState.response?.data.resume.isNotEmpty == true) {
              Preference.setString(Preference.RESUME_URL,
                  '${portfolioState.response?.data.resume.first.url}');
            }

            Preference.setInt(Preference.RESUME_PARSER_DATA_COUNT,
                portfolioState.response!.data.resumeParserDataCount!);

            if (portfolioState.response?.data.portfolioProfile.isNotEmpty ==
                true) {
              Preference.setString(Preference.ABOUT_ME,
                  '${portfolioState.response?.data.portfolioProfile.first.aboutMe}');

              Preference.setString(Preference.USER_HEADLINE,
                  '${portfolioState.response?.data.portfolioProfile.first.headline}');
              Preference.setString(Preference.LOCATION,
                  '${portfolioState.response?.data.portfolioProfile.first.city}, ${portfolioState.response?.data.portfolioProfile.first.country}');
            }

            Log.v("PortfolioState Success....................");
          } catch (e, stack) {
            debugPrint('stack is $stack');
          }
          setState(() {
            isPortfolioLoading = false;
          });
          break;

        case ApiStatus.ERROR:
          isPortfolioLoading = false;
          Log.v("PortfolioState Error..........................");
          Log.v(
              "PortfolioState Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'portfolio_page', parameters: {
            "ERROR": '${portfolioState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handletopScoring(TopScoringUserState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Portfolio TopScoring Loading....................");

          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "PortfolioState TopScoring Success................... ${portfolioState.response?.toJson()}.");

          userRank = portfolioState.response;

          isPortfolioLoading = false;
          setState(() {});
          break;

        case ApiStatus.ERROR:
          isPortfolioLoading = false;
          Log.v("TopScoring Error..........................");
          Log.v(
              "TopScoring Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'portfolio_page', parameters: {
            "ERROR": '${portfolioState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleOpenToWork(OpenToWorkState state) {
    setState(() async {
      switch (state.apiState) {
        case ApiStatus.LOADING:
          Log.v("Open To Work Loading....................");
          isPortfolioLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Open To Work Success....................");
          // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          //   duration: const Duration(milliseconds: 600),
          //   content: Text('${state.response?.data?.list}',
          //       style: Styles.regular(color: ColorConstants.WHITE)),
          // ));

          isPortfolioLoading = false;
          setState(() {});
          break;

        case ApiStatus.ERROR:
          isPortfolioLoading = false;
          Log.v("Open To Work  Error..........................");
          Log.v("Open To Work  Error..........................${state.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'portfolio_page', parameters: {
            "ERROR": '${state.response?.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handlePiDetail(PiDetailState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PI Detail Loading....................");
          //isPortfolioLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "PI Detail Success.................... ${portfolioState.response?.data?.mobile}");

          piDetailResp = portfolioState.response;
          if (widget.userId != null) return;

          if (piDetailResp?.data?.name != '' &&
              piDetailResp?.data?.name != null)
            Preference.setString(
                Preference.FIRST_NAME, '${piDetailResp?.data?.name}');
          if (piDetailResp?.data?.email != '' &&
              piDetailResp?.data?.email != null)
            Preference.setString(
                Preference.USER_EMAIL, '${piDetailResp?.data?.email}');

          if (piDetailResp?.data?.mobile != '' &&
              piDetailResp?.data?.mobile != null)
            Preference.setString(
                Preference.PHONE, '${portfolioState.response!.data!.mobile!}');

          isPortfolioLoading = false;
          setState(() {});
          break;

        case ApiStatus.ERROR:
          isPortfolioLoading = false;
          Log.v("PI Detail Error..........................");
          Log.v(
              "PI Detail Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'portfolio_page', parameters: {
            "ERROR": '${state.response?.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleCompetition(PortfoilioCompetitionState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Portfolio Competition Loading....................");

          break;
        case ApiStatus.SUCCESS:
          Log.v("PortfolioState Competition Success....................");
          competition = portfolioState.response;

          isPortfolioLoading = false;
          setState(() {});
          break;

        case ApiStatus.ERROR:
          isPortfolioLoading = false;
          Log.v("PortfolioState Error..........................");
          Log.v(
              "PortfolioState Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'portfolio_page', parameters: {
            "ERROR": '${state.response?.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Widget topRow(String title,
      {required Function addAction,
      required Function arrowAction,
      bool showAddButton = true,
      bool showArrow = false}) {
    return Container(
      color: ColorConstants.WHITE,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Column(
          children: [
            if (!showAddButton)
              SizedBox(
                height: 10,
              ),
            Row(
              children: [
                Text(
                  '$title',
                  style: Styles.bold(size: 14),
                ),
                Spacer(),
                if (showAddButton && widget.userId == null)
                  IconButton(
                      onPressed: () {
                        addAction();
                      },
                      icon: Icon(Icons.add)),
                if (showArrow && widget.userId == null)
                  InkWell(
                      onTap: (() {
                        arrowAction();
                      }),
                      child: Icon(Icons.arrow_forward_ios_outlined)),
              ],
            ),
            Divider(),
          ],
        ),
      ),
    );
  }

  String calculateTimeDifferenceBetween(DateTime startDate, DateTime endDate) {
    int seconds = endDate.difference(startDate).inSeconds;
    if (seconds < 60) {
      if (seconds.abs() < 4) return '${tr('now')}';
      return '${seconds.abs()} ${tr('second')}';
    } else if (seconds >= 60 && seconds < 3600)
      return '${startDate.difference(endDate).inMinutes.abs()} ${tr('mins')}';
    else if (seconds >= 3600 && seconds < 86400)
      return '${startDate.difference(endDate).inHours.abs()} ${tr('hour')}';
    else {
      // convert day to month
      int days = startDate.difference(endDate).inDays.abs();
      if (days < 30 && days > 7) {
        return '${(startDate.difference(endDate).inDays ~/ 7).abs()} ${tr('week')}';
      }
      if (days > 30) {
        int month = (startDate.difference(endDate).inDays ~/ 30).abs();
        return '$month ${tr('month')}';
      } else
        return '${startDate.difference(endDate).inDays.abs()} ${tr('day')}';
    }
  }

  // skill shimer
  Widget skillListShimmer(var listLength) {
    return listLength == 1
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 1.0,
                  height: MediaQuery.of(context).size.height * 0.3,
                  color: Colors.grey,
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.7,
                  height: 13,
                  color: Colors.grey,
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.9,
                  height: 13,
                  color: Colors.grey,
                ),
              ),
            ],
          )
        : InkWell(
            onTap: () {},
            child: Container(
              width: MediaQuery.of(context).size.width,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        'assets/images/skill_set.png',
                        scale: 10,
                        opacity: AlwaysStoppedAnimation(0.5),
                      ),
                      Padding(
                          padding: const EdgeInsets.only(
                            top: 8.0,
                          ),
                          child: Text('add_your_skill').tr()),
                    ],
                  ),
                ],
              ),
            ),
          );
  }

  Widget portfolioListShimmer(var listLength) {
    return listLength == 1
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 1.0,
                  height: MediaQuery.of(context).size.height * 0.3,
                  color: Colors.grey,
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.7,
                  height: 13,
                  color: Colors.grey,
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.9,
                  height: 13,
                  color: Colors.grey,
                ),
              ),
            ],
          )
        : InkWell(
            onTap: () {},
            child: Container(
              width: MediaQuery.of(context).size.width,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Image.asset(
                    'assets/images/portfolio_emp_bg.png',
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        'assets/images/portfolio_emp.png',
                      ),
                      Padding(
                          padding: const EdgeInsets.only(
                            top: 8.0,
                          ),
                          child: SizedBox(
                            width: width(context),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text('add_your').tr(),
                                SizedBox(width: 3),
                                SizedBox(
                                  width: Utility().isRTL(context)
                                      ? width(context) * 0.08
                                      : width(context) * 0.2,
                                  child: ShaderMask(
                                      blendMode: BlendMode.srcIn,
                                      shaderCallback: (Rect bounds) {
                                        return LinearGradient(
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight,
                                            colors: <Color>[
                                              ColorConstants().gradientLeft(),
                                              ColorConstants().gradientRight()
                                            ]).createShader(bounds);
                                      },
                                      child: InkWell(
                                          onTap: (() {
                                            Navigator.push(
                                                    context,
                                                    PageTransition(
                                                        duration: Duration(
                                                            milliseconds: 350),
                                                        reverseDuration:
                                                            Duration(
                                                                milliseconds:
                                                                    600),
                                                        type: PageTransitionType
                                                            .bottomToTop,
                                                        child: AddPortfolio()))
                                                .then(
                                                    (value) => getPortfolio());
                                          }),
                                          child: Text('portfolio',
                                                  style: Styles.bold(size: 14))
                                              .tr())),
                                )
                              ],
                            ),
                          )),
                    ],
                  ),
                ],
              ),
            ),
          );
  }

  Widget competitionListShimmer(int listLength) {
    return listLength == 1
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 1.0,
                  height: MediaQuery.of(context).size.height * 0.3,
                  color: Colors.grey,
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.7,
                  height: 13,
                  color: Colors.grey,
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Row(
                children: [
                  Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    enabled: true,
                    child: Container(
                      width: 130,
                      height: 13,
                      color: Colors.grey,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 10.0),
                    child: Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      enabled: true,
                      child: Container(
                        width: 100,
                        height: 13,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          )
        : InkWell(
            onTap: () {
              Navigator.pop(context, '/g-competitions');
            },
            child: Container(
              width: MediaQuery.of(context).size.width,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset('assets/images/comp_emp.png'),
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text('participate_in').tr(),
                  ),
                  ShaderMask(
                      blendMode: BlendMode.srcIn,
                      shaderCallback: (Rect bounds) {
                        return LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: <Color>[
                              ColorConstants().gradientLeft(),
                              ColorConstants().gradientRight()
                            ]).createShader(bounds);
                      },
                      child: Text('competitions', style: Styles.bold()).tr()),
                ],
              ),
            ),
          );
  }

  Widget educationListShimmer(var listLength) {
    return listLength == 1
        ? Container(
            margin: EdgeInsets.symmetric(
              horizontal: 8,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      enabled: true,
                      child: Container(
                        width: 100,
                        height: 100,
                        color: Colors.grey,
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        SizedBox(
                          width: width(context) * 0.5,
                          child: Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            enabled: true,
                            child: Container(
                              width: 100,
                              height: 13,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        SizedBox(
                          width: width(context) * 0.4,
                          child: Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            enabled: true,
                            child: Container(
                              width: 80,
                              height: 13,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        Row(
                          children: [
                            Shimmer.fromColors(
                              baseColor: Colors.grey[300]!,
                              highlightColor: Colors.grey[100]!,
                              enabled: true,
                              child: Container(
                                width: 40,
                                height: 13,
                                color: Colors.grey,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                enabled: true,
                                child: Container(
                                  width: 100,
                                  height: 13,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ],
                        )
                      ],
                    )
                  ],
                ),
                SizedBox(
                  height: 10,
                ),
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  enabled: true,
                  child: Container(
                    width: width(context) * 0.9,
                    height: 20,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          )
        : Container(
            color: Colors.white,
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.2 - 20,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                    onTap: () {},
                    child: Image.asset('assets/images/edu_empty.png')),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('add_your').tr(),
                      SizedBox(
                        width: 3,
                      ),
                      ShaderMask(
                          blendMode: BlendMode.srcIn,
                          shaderCallback: (Rect bounds) {
                            return LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                  ColorConstants().gradientLeft(),
                                  ColorConstants().gradientRight()
                                ]).createShader(bounds);
                          },
                          child: InkWell(
                              onTap: () {
                                if (widget.userId == null)
                                  Navigator.push(
                                          context,
                                          PageTransition(
                                              duration:
                                                  Duration(milliseconds: 350),
                                              reverseDuration:
                                                  Duration(milliseconds: 350),
                                              type: PageTransitionType
                                                  .bottomToTop,
                                              child: AddEducation()))
                                      .then((value) => getPortfolio());
                              },
                              child: Text('education',
                                      style: Styles.bold(size: 14))
                                  .tr()))
                    ],
                  ),
                ),
              ],
            ),
          );
  }

  ///add new
  Widget certificatesListShimmer(int listLength) {
    return listLength == 1
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 1.0,
                  height: MediaQuery.of(context).size.height * 0.3,
                  color: Colors.grey,
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.7,
                  height: 13,
                  color: Colors.grey,
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Row(
                children: [
                  Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    enabled: true,
                    child: Container(
                      width: 130,
                      height: 13,
                      color: Colors.grey,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 10.0),
                    child: Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      enabled: true,
                      child: Container(
                        width: 100,
                        height: 13,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          )
        : Container(
            color: Colors.white,
            height: MediaQuery.of(context).size.height * 0.2 - 20,
            width: MediaQuery.of(context).size.width,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset('assets/images/certi_emp.png'),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('add').tr(),
                      SizedBox(width: 3),
                      ShaderMask(
                          blendMode: BlendMode.srcIn,
                          shaderCallback: (Rect bounds) {
                            return LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                  ColorConstants().gradientLeft(),
                                  ColorConstants().gradientRight()
                                ]).createShader(bounds);
                          },
                          child: InkWell(
                              onTap: () {
                                if (widget.userId == null)
                                  Navigator.push(
                                          context,
                                          PageTransition(
                                              duration:
                                                  Duration(milliseconds: 350),
                                              reverseDuration:
                                                  Duration(milliseconds: 350),
                                              type: PageTransitionType
                                                  .bottomToTop,
                                              child: AddCertificate()))
                                      .then((value) => getPortfolio());
                              },
                              child: Text('certificate',
                                      style: Styles.bold(size: 14))
                                  .tr()))
                    ],
                  ),
                ),
              ],
            ),
          );
  }

  Widget experienceListShimmer(var listLength) {
    return listLength == 1
        ? Container(
            //width: width(context) * 0.3,
            margin: EdgeInsets.symmetric(
              horizontal: 8,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      enabled: true,
                      child: Container(
                        width: 100,
                        height: 100,
                        color: Colors.grey,
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        SizedBox(
                          width: width(context) * 0.5,
                          child: Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            enabled: true,
                            child: Container(
                              width: 100,
                              height: 13,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        SizedBox(
                          width: width(context) * 0.4,
                          child: Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            enabled: true,
                            child: Container(
                              width: 80,
                              height: 13,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        Row(
                          children: [
                            Shimmer.fromColors(
                              baseColor: Colors.grey[300]!,
                              highlightColor: Colors.grey[100]!,
                              enabled: true,
                              child: Container(
                                width: 40,
                                height: 13,
                                color: Colors.grey,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                enabled: true,
                                child: Container(
                                  width: 100,
                                  height: 13,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ],
                        )
                      ],
                    )
                  ],
                ),
                SizedBox(
                  height: 10,
                ),
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  enabled: true,
                  child: Container(
                    width: width(context) * 0.9,
                    height: 20,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          )
        : Container(
            color: Colors.white,
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.2 - 20,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                    onTap: () {},
                    child: Image.asset('assets/images/exp_emp.png')),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('add_your').tr(),
                      SizedBox(width: 5),
                      ShaderMask(
                        blendMode: BlendMode.srcIn,
                        shaderCallback: (Rect bounds) {
                          return LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: <Color>[
                                ColorConstants().gradientLeft(),
                                ColorConstants().gradientRight()
                              ]).createShader(bounds);
                        },
                        child: InkWell(
                            onTap: () {
                              if (widget.userId == null)
                                Navigator.push(
                                        context,
                                        PageTransition(
                                            duration:
                                                Duration(milliseconds: 350),
                                            reverseDuration:
                                                Duration(milliseconds: 350),
                                            type:
                                                PageTransitionType.bottomToTop,
                                            child: AddExperience()))
                                    .then((value) => getPortfolio());
                            },
                            child:
                                Text('work_exp', style: Styles.bold(size: 14))
                                    .tr()),
                      )
                    ],
                  ),
                ),
              ],
            ),
          );
  }

  Widget recentActivitiesListShimmer(var listLength) {
    return listLength == 1
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 1.0,
                  height: MediaQuery.of(context).size.height * 0.2,
                  color: Colors.grey,
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.7,
                  height: 13,
                  color: Colors.grey,
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                enabled: true,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.9,
                  height: 13,
                  color: Colors.grey,
                ),
              ),
            ],
          )
        : InkWell(
            onTap: () {
              if (widget.userId == null) Navigator.pop(context, '/g-carvaan');
            },
            child: Container(
              color: Colors.white,
              width: MediaQuery.of(context).size.width,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Image.asset(
                    'assets/images/recentactiv_bg.png',
                  ),
                  Positioned(
                    left: 20,
                    right: 20,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/recentactiv_emp.png',
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 8.0,
                          ),
                          child: Text('no_community_activity',
                                  style: Styles.semibold(
                                      size: 14, color: Color(0xff0E1638)))
                              .tr(),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 8.0,
                          ),
                          child: ShaderMask(
                              blendMode: BlendMode.srcIn,
                              shaderCallback: (Rect bounds) {
                                return LinearGradient(
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                    colors: <Color>[
                                      ColorConstants().gradientLeft(),
                                      ColorConstants().gradientRight()
                                    ]).createShader(bounds);
                              },
                              child: Text('explore_community',
                                  style: Styles.bold(
                                    size: 14,
                                  )).tr()),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
  }

  Widget educationList(List<CommonProfession> education) {
    education.sort((a, b) => b.endDate.compareTo(a.endDate));

    return Container(
      color: ColorConstants.WHITE,
      child: education.length != 0
          ? ListView.builder(
              shrinkWrap: true,
              physics: ScrollPhysics(),
              itemCount: min(2, portfolioResponse!.data.education.length),
              itemBuilder: (context, index) {
                int len = min(2, portfolioResponse!.data.education.length);
                DateTime endDate = DateTime.now();

                if (portfolioResponse?.data.education[index].endDate != null ||
                    portfolioResponse?.data.education[index].endDate != '') {
                  String endDateString = "${education[index].endDate}";
                  endDate =
                      DateFormat("yyyy-MM-dd", 'en_IN').parse(endDateString);
                }
                String startDateString = "${education[index].startDate}";

                DateTime startDate =
                    DateFormat("yyyy-MM-dd", 'en_IN').parse(startDateString);

                return Container(
                  width: width(context) * 0.3,
                  color: ColorConstants.WHITE,
                  margin: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (index != 0)
                        SizedBox(
                          height: 18,
                        ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: CachedNetworkImage(
                                imageUrl:
                                    '${portfolioResponse?.data.baseFileUrl}${education[index].imageName}',
                                height: width(context) * 0.2,
                                width: width(context) * 0.2,
                                fit: BoxFit.cover,
                                errorWidget: (context, url, error) {
                                  return Container(
                                    padding: EdgeInsets.all(14),
                                    decoration:
                                        BoxDecoration(color: Color(0xffD5D5D5)),
                                    child: SvgPicture.asset(
                                      'assets/images/default_education.svg',
                                      height: 40,
                                      width: 40,
                                      colorFilter: ColorFilter.mode(
                                          ColorConstants.GREY_5,
                                          BlendMode.srcIn),
                                      allowDrawingOutsideViewBox: true,
                                    ),
                                  );
                                },
                                placeholder:
                                    (BuildContext context, loadingProgress) {
                                  return Container(
                                    padding: EdgeInsets.all(14),
                                    decoration:
                                        BoxDecoration(color: Color(0xffD5D5D5)),
                                    child: SvgPicture.asset(
                                      'assets/images/default_education.svg',
                                      height: 40,
                                      width: 40,
                                      colorFilter: ColorFilter.mode(
                                          ColorConstants.GREY_5,
                                          BlendMode.srcIn),
                                      allowDrawingOutsideViewBox: true,
                                    ),
                                  );
                                },
                              )),
                          SizedBox(
                            width: 10,
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              SizedBox(
                                width: width(context) * 0.71,
                                child: Text(
                                  '${education[index].title}',
                                  maxLines: 2,
                                  style: Styles.bold(size: 14),
                                ),
                              ),
                              SizedBox(height: 4),
                              SizedBox(
                                width: width(context) * 0.71,
                                child: Text(
                                  maxLines: 2,
                                  '${education[index].institute}',
                                  style: Styles.regular(size: 14),
                                ),
                              ),
                              SizedBox(height: 4),
                              Row(
                                children: [
                                  Text(
                                    '${listOfMonths[startDate.month - 1].substring(0, 3)} ${startDate.year.toString().substring(2, 4)} - ',
                                    style: Styles.regular(size: 14),
                                  ),
                                  if (portfolioResponse
                                              ?.data.education[index].endDate !=
                                          null ||
                                      portfolioResponse
                                              ?.data.education[index].endDate !=
                                          '')
                                    Text(
                                      '${listOfMonths[endDate.month - 1].substring(0, 3)} ${endDate.year.toString().substring(2, 4)}',
                                      style: Styles.regular(size: 14),
                                    ),
                                ],
                              )
                            ],
                          )
                        ],
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      SizedBox(
                        child: ReadMoreText(
                          viewMore: tr('view_more'),
                          text: '${education[index].description}',
                          color: Color(0xff929BA3),
                        ),
                      ),
                      SizedBox(
                        height: 22,
                      ),
                      if (index + 1 != len) Divider(),
                    ],
                  ),
                );
              })
          : educationListShimmer(0),
    );
  }

  Widget extraActivitiesListShimmer(var listLength) {
    return listLength == 1
        ? Container(
            margin: EdgeInsets.symmetric(
              horizontal: 8,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      enabled: true,
                      child: Container(
                        width: 100,
                        height: 100,
                        color: Colors.grey,
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        SizedBox(
                          width: width(context) * 0.5,
                          child: Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            enabled: true,
                            child: Container(
                              width: 100,
                              height: 13,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        SizedBox(
                          width: width(context) * 0.4,
                          child: Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            enabled: true,
                            child: Container(
                              width: 80,
                              height: 13,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        Row(
                          children: [
                            Shimmer.fromColors(
                              baseColor: Colors.grey[300]!,
                              highlightColor: Colors.grey[100]!,
                              enabled: true,
                              child: Container(
                                width: 40,
                                height: 13,
                                color: Colors.grey,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                enabled: true,
                                child: Container(
                                  width: 100,
                                  height: 13,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ],
                        )
                      ],
                    )
                  ],
                ),
                SizedBox(
                  height: 10,
                ),
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  enabled: true,
                  child: Container(
                    width: width(context) * 0.9,
                    height: 20,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          )
        : Container(
            color: Colors.white,
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.2 - 20,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                    onTap: () {},
                    child: Image.asset('assets/images/extraacti_emp.png')),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('add_your').tr(),
                      SizedBox(width: 3),
                      ShaderMask(
                          blendMode: BlendMode.srcIn,
                          shaderCallback: (Rect bounds) {
                            return LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: <Color>[
                                  ColorConstants().gradientLeft(),
                                  ColorConstants().gradientRight()
                                ]).createShader(bounds);
                          },
                          child: InkWell(
                              onTap: () {
                                if (widget.userId == null)
                                  Navigator.push(
                                          context,
                                          PageTransition(
                                              duration:
                                                  Duration(milliseconds: 350),
                                              reverseDuration:
                                                  Duration(milliseconds: 350),
                                              type: PageTransitionType
                                                  .bottomToTop,
                                              child: AddActivities()))
                                      .then((value) => getPortfolio());
                              },
                              child: Text('extra_activities',
                                      style: Styles.bold(size: 14))
                                  .tr()))
                    ],
                  ),
                ),
              ],
            ),
          );
  }

  void _initFilePiker() async {
    FilePickerResult? result;
    if (await Permission.storage.request().isGranted) {
      if (Platform.isIOS) {
        result = await FilePicker.platform.pickFiles(
            allowMultiple: false, type: FileType.video, allowedExtensions: []);
      } else {
        result = await FilePicker.platform.pickFiles(
          allowMultiple: false,
          type: FileType.video,
        );
      }

      if (result != null) {
        if (File(result.paths[0]!).lengthSync() / 1000000 > 50.0) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('imageVideoSizeLarge').tr(),
          ));
        } else {
          pickedList.add(File(result.paths[0]!));
        }
        pickedFile = pickedList.first;

        if (result.paths.toString().contains('mp4')) {
          final thumbnail = await VideoThumbnail.thumbnailFile(
              video: result.paths[0].toString(),
              thumbnailPath: _tempDir,
              imageFormat: _format,
              quality: _quality);
          setState(() {
            final file = File(thumbnail!);
            filePath = file.path;
          });
        }
      }
    }
  }

  Widget myEventsParticipation() {
    return Container(
      color: Colors.white,
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'my_event_participation',
              style: Styles.bold(size: 14),
            ).tr(),
            Divider(),
            GestureDetector(
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => TermsAndCondition(
                              url: '${APK_DETAILS['my_event_participation']}' +
                                  Preference.getString(Preference.SSOTOKEN)
                                      .toString(),
                              title: tr('my_event_participation'),
                            ),
                        maintainState: false));
              },
              child: Text(
                'event_participation',
                style: Styles.regular(size: 14, color: Colors.red),
              ).tr(),
            ),
          ],
        ),
      ),
    );
  }
}

class ShowSocialLinks extends StatefulWidget {
  final dynamic portfolioSocial;

  const ShowSocialLinks({Key? key, this.portfolioSocial}) : super(key: key);

  @override
  State<ShowSocialLinks> createState() => _ShowSocialLinksState();
}

class _ShowSocialLinksState extends State<ShowSocialLinks> {
  @override
  Widget build(BuildContext context) {
    dynamic data = widget.portfolioSocial;
    List<String> socialKey = [];
    List<String> socialValue = [];
    List<String> socialKeyUnselected = [];
    List<String> socialValueUnselected = [];
    print("current json is ${widget.portfolioSocial} ");

    for (final key in data.keys) {
      if (key != 'mob_num' &&
          key != 'email' &&
          key != 'mob_num_hidden' &&
          key != 'email_hidden' &&
          key != 'id' &&
          key != 'other') {
        if (data[key] != null && data[key].toString().isNotEmpty) {
          socialKey.add('$key');
          socialValue.add('${data[key]}');
        } else {
          socialKeyUnselected.add('$key');
          socialValueUnselected.add('${data[key]}');
        }
      }
    }

    return SizedBox(
      height: height(context) * 0.1,
      child: Row(
        children: [
          ListView.builder(
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              itemCount: min(4, socialKey.length),
              itemBuilder: (context, index) => InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) {
                            return CommonWebView(
                              url: socialValue[index],
                            );
                          },
                        ),
                      );
                    },
                    child: Container(
                        height: 20,
                        margin: EdgeInsets.only(right: 10),
                        child: SvgPicture.asset(
                          'assets/images/${socialKey[index]}' + '.svg',
                          colorFilter: APK_DETAILS['gradient_icon'] == '0'
                              ? ColorFilter.mode(
                                  ColorConstants().primaryColor()!,
                                  BlendMode.srcIn)
                              : null,
                        )),
                  )),
          ListView.builder(
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              itemCount: max(0, 4 - socialKey.length),
              itemBuilder: (context, index) => Container(
                  height: 20,
                  margin: EdgeInsets.only(right: 10),
                  child: SvgPicture.asset(
                    'assets/images/${socialKeyUnselected[index]}' + '.svg',
                    colorFilter: ColorFilter.mode(
                      ColorConstants.GREY_4,
                      BlendMode.srcIn,
                    ),
                  ))),
          GestureDetector(
            onTapDown: (TapDownDetails detail) {
              _showPopupMenu(detail.globalPosition, socialKey, socialValue,
                  socialKeyUnselected, socialValueUnselected);
            },
            child: ShaderMask(
              blendMode: BlendMode.srcIn,
              shaderCallback: (Rect bounds) {
                return LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: <Color>[
                      ColorConstants().gradientLeft(),
                      ColorConstants().gradientRight()
                    ]).createShader(bounds);
              },
              child: SvgPicture.asset(
                'assets/images/vertical_menu.svg',
              ),
            ),
          )
        ],
      ),
    );
  }

  void _showPopupMenu(Offset offset, socialKey, socialValue,
      socialKeyUnselected, socialValueUnselected) async {
    final screenSize = MediaQuery.of(context).size;
    double left = offset.dx;
    double top = offset.dy;
    double right = screenSize.width - offset.dx;
    double bottom = screenSize.height - offset.dy;
    List<PopupMenuItem> selectedMenuList = List.generate(
        max(0, socialKey.length - 4),
        (index) => PopupMenuItem(
              child: InkWell(
                onTap: () async {
                  Navigator.of(context).pop();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) {
                        return CommonWebView(
                          url: socialValue[index + 4],
                        );
                      },
                    ),
                  );
                },
                child: Row(
                  children: [
                    Container(
                        height: 25,
                        margin: EdgeInsets.only(right: 6),
                        child: SvgPicture.asset(
                          'assets/images/${socialKey[index + 4]}' + '.svg',
                          colorFilter: APK_DETAILS['gradient_icon'] == '0'
                              ? ColorFilter.mode(
                                  ColorConstants().primaryColor()!,
                                  BlendMode.srcIn)
                              : null,
                        )),
                    Text(
                      '${socialKey[index + 4]}'.capital(),
                      style: Styles.regular(),
                    )
                  ],
                ),
              ),
              value: '${socialKey[index + 4]}',
            ));

    List<PopupMenuItem> unselectedMenuList = List.generate(
        max(0, socialKeyUnselected.length - max(0, 4 - socialKey.length)),
        (index) => PopupMenuItem(
              child: Row(
                children: [
                  Container(
                      height: 25,
                      margin: EdgeInsets.only(right: 6),
                      child: SvgPicture.asset(
                        'assets/images/${socialKeyUnselected[index + max(0, 4 - socialKey.length)]}' +
                            '.svg',
                        colorFilter: ColorFilter.mode(
                            ColorConstants.GREY_4, BlendMode.srcIn),
                      )),
                  Text(
                    '${socialKeyUnselected[index + max(0, 4 - socialKey.length)]}'
                        .capital(),
                  )
                ],
              ),
              value:
                  '${socialValueUnselected[index + max(0, 4 - socialKey.length)]}',
            ));

    List<PopupMenuItem> finalList = [
      ...selectedMenuList,
      ...unselectedMenuList
    ];
    await showMenu(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(20.0),
        ),
      ),
      context: context,
      position: RelativeRect.fromLTRB(left, top, right, bottom),
      items: finalList,
      elevation: 10.0,
    );
  }
}

extension on String {
  String capital() {
    return this[0].toUpperCase() + this.substring(1);
  }
}
