import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/utils/config.dart';
import 'package:flutter_countdown_timer/current_remaining_time.dart';
import 'package:flutter_countdown_timer/flutter_countdown_timer.dart';
import 'package:masterg/blocs/auth_bloc.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/request/auth_request/email_request.dart';
import 'package:masterg/data/models/request/auth_request/login_request.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/auth_pages/self_details_page.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/ghome/home_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:otp_autofill/otp_autofill.dart';
import 'package:pinput/pinput.dart';

import '../../utils/constant.dart';
import '../custom_pages/custom_widgets/CommonWebView.dart';

// ignore: must_be_immutable
class NewVerifyOtp extends StatefulWidget {
  String username;

  NewVerifyOtp(this.username);

  @override
  _NewVerifyOtpState createState() => _NewVerifyOtpState();
}

class _NewVerifyOtpState extends State<NewVerifyOtp> {
  int endTime = DateTime.now().millisecondsSinceEpoch + 1000 * 30;
  bool _isLoading = false;
  FocusNode focusNode = FocusNode();
  String _pin = "";
  String? deviceId;
  bool resendFlag = false;
  List<Menu>? menuList;
  bool isFocused = false;
  late OTPTextEditController otpController;
  late OTPInteractor _otpInteractor;
  String otpCode = '';

  Future<Null> _getId() async {
    var deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      // import 'dart:io'
      var iosDeviceInfo = await deviceInfo.iosInfo;
      deviceId = iosDeviceInfo.identifierForVendor; // unique ID on iOS
    } else if (Platform.isAndroid) {
      var androidDeviceInfo = await deviceInfo.androidInfo;
      deviceId = androidDeviceInfo.id; // unique ID on Android
    }
  }

  @override
  void initState() {
    super.initState();
    focusNode.addListener(_onFocusChange);
    _getId();
    if (!kIsWeb) {
      _otpInteractor = OTPInteractor();
      _otpInteractor
          .getAppSignature()
          .then((value) => Log.v('signature - $value'));
      otpController = OTPTextEditController(
        codeLength: 4,
        onCodeReceive: (code) => Log.v('Your Application receive code - $code'),
        otpInteractor: _otpInteractor,
      )..startListenUserConsent(
          (code) {
            final exp = RegExp(r'(\d{4})');
            return exp.stringMatch(code ?? '') ?? '';
          },
        );
    }
  }

  void _onFocusChange() {
    setState(() {
      isFocused = focusNode.hasFocus;
    });
  }

  @override
  Future<void> dispose() async {
    super.dispose();
  }

  void _handleLoginResponseOTP(LoginState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................");
          endTime = DateTime.now().millisecondsSinceEpoch + 1000 * 30;
          Utility.showSnackBar(
              miliSec: 3000,
              scaffoldContext: context,
              message: tr('OTP_sent_on_mobile'));
          _isLoading = false;
          break;

        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${loginState.error}");
          AlertsWidget.alertWithOkBtn(
              context: context,
              text: loginState.error,
              onOkClick: () {
                FocusScope.of(context).unfocus();
              });
          FirebaseAnalytics.instance.logEvent(name: 'otp_page', parameters: {
            "otp_failed": "true",
            "ERROR": loginState.response?.error?[0] ?? '',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void getBottomNavigationBar() {
    BlocProvider.of<HomeBloc>(context).add((GetBottomNavigationBarEvent()));
    getPortfolio();
    getPiDetail();
  }

  void getPiDetail() {
    BlocProvider.of<HomeBloc>(context)
        .add(PiDetailEvent(userId: Preference.getInt(Preference.USER_ID)));
  }

  void getPortfolio() {
    BlocProvider.of<HomeBloc>(context).add(PortfolioEvent());
  }

  void handlePortfolioState(PortfolioState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PortfolioState Loading...................");
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "PortfolioStatedone ................... ${portfolioState.response?.toJson()}");

          portfolioState.response = portfolioState.response;

          if ('${portfolioState.response?.data.name}' != '')
            Preference.setString(
                Preference.FIRST_NAME, '${portfolioState.response?.data.name}');
          if (portfolioState.response?.data.image.contains(
                  '${Preference.getString(Preference.PROFILE_IMAGE)}') ==
              true) {
            Preference.setString(Preference.PROFILE_IMAGE,
                '${portfolioState.response?.data.image}');
          }

          Preference.setString(Preference.USER_EMAIL,
              '${portfolioState.response?.data.portfolioSocial.first['email']}');
          Preference.setString(Preference.PHONE,
              '${portfolioState.response?.data.portfolioSocial.first['mob_num']}');

          if ('${portfolioState.response?.data.image}' != '')
            Preference.setString(Preference.PROFILE_IMAGE,
                '${portfolioState.response?.data.image}');

          Preference.setString(Preference.PROFILE_VIDEO,
              '${portfolioState.response?.data.profileVideo}');
          Preference.setInt(Preference.PROFILE_PERCENT,
              portfolioState.response!.data.profileCompletion);
          Preference.setInt(Preference.RESUME_PARSER_DATA_COUNT,
              portfolioState.response!.data.resumeParserDataCount!);

          if (portfolioState.response?.data.portfolioProfile.isNotEmpty ==
              true) {
            Preference.setString(Preference.ABOUT_ME,
                '${portfolioState.response?.data.portfolioProfile.first.aboutMe}');

            Preference.setString(Preference.USER_HEADLINE,
                '${portfolioState.response?.data.portfolioProfile.first.headline}');
            Preference.setString(Preference.LOCATION,
                '${portfolioState.response?.data.portfolioProfile.first.city}, ${portfolioState.response?.data.portfolioProfile.first.country}');
          }
          Log.v("PortfolioState Success....................");
          setState(() {});
          break;

        case ApiStatus.ERROR:
          Log.v("PortfolioState Error..........................");
          Log.v(
              "PortfolioState Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance.logEvent(name: 'otp_page', parameters: {
            "get_details_failed": "true",
            "ERROR": '${portfolioState.error}',
          });
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handlePiDetail(PiDetailState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PI Detail Loading....................");
          //isPortfolioLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("PI Detail Success....................");
          if (portfolioState.response?.data?.name != '' &&
              portfolioState.response?.data?.name != null)
            Preference.setString(Preference.FIRST_NAME,
                '${portfolioState.response?.data?.name}');
          if (portfolioState.response?.data?.email != '' &&
              portfolioState.response?.data?.email != null)
            Preference.setString(Preference.USER_EMAIL,
                '${portfolioState.response?.data?.email}');

          if (portfolioState.response?.data?.mobile != '' &&
              portfolioState.response?.data?.mobile != null)
            Preference.setString(
                Preference.PHONE, '${portfolioState.response?.data?.mobile}');

          setState(() {});
          break;

        case ApiStatus.ERROR:
          Log.v("PI Detail Error..........................");
          Log.v(
              "PI Detail Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance.logEvent(name: 'PI_details', parameters: {
            "ERROR": '${portfolioState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handelBottomNavigationBar(GetBottomBarState state) {
    var getBottomBarState = state;
    setState(() {
      switch (getBottomBarState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................");
          menuList = state.response!.data!.menu;
          menuList = menuList!.where((element) {
            bool containRole = element.role.toString().toLowerCase().contains(
                '${Preference.getString(Preference.ROLE)?.toLowerCase()}');
            return containRole;
          }).toList();

          if (menuList?.length == 0) {
            AlertsWidget.alertWithOkBtn(
                context: context,
                text: tr('menu_not_found_msg'),
                onOkClick: () {
                  FocusScope.of(context).unfocus();
                });
          } else {
            // menuList?.sort((a, b) => a.inAppOrder!.compareTo(b.inAppOrder!));
            menuList?.sort((a, b) => (int.tryParse('${a.inAppOrder}') ?? 0)
                .compareTo(int.tryParse('${b.inAppOrder}') ?? 0));

            Navigator.pushAndRemoveUntil(
                context,
                NextPageRoute(
                    homePage(
                      bottomMenu: menuList,
                    ),
                    isMaintainState: true),
                (route) => false);
          }
          _isLoading = false;
          break;

        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................");
          Log.v("Error..........................${getBottomBarState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'bottom_navigatorBar', parameters: {
            "ERROR": '${getBottomBarState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handleVerifyResponse(VerifyOtpState state) {
    var loginState = state;
    setState(() {
      switch (loginState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          _isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success ....................");
          if (state.response!.isCompleted == null)
            state.response!.isCompleted = 0;
          userIDEvent(state.response!.data!.user!.id.toString());
          Preference.setInt(Preference.APP_LANGUAGE,
              Preference.getInt(Preference.APP_LANGUAGE) ?? 1);
          Preference.setInt(Preference.CONTENT_LANGUAGE, 1);
          Preference.setString(
              Preference.FIRST_NAME, '${state.response!.data!.user!.name}');
          Preference.setInt(
              Preference.USER_ID, state.response!.data!.user!.id!);
          Preference.setString(
              Preference.USER_TOKEN, '${state.response!.data!.token}');

          Preference.setString(
              Preference.ROLE, '${state.response!.data!.user!.role}');
          Preference.setString(Preference.PROFILE_IMAGE,
              '${state.response!.data!.user!.profileImage}');
          Preference.setString(
              'interestCategory', '${state.response!.data!.user!.categoryIds}');
          Preference.setString(Preference.DEFAULT_VIDEO_URL_CATEGORY,
              '${state.response!.data!.user!.defaultVideoUrlOnCategory}');
          Preference.setString(
              Preference.ORG_URL, '${state.response!.data!.user!.orgLogo}');
          Preference.setString(Preference.ORGANIZATION_ID,
              '${state.response!.data!.user!.orgId}');
          getBottomNavigationBar();

          break;
        case ApiStatus.ERROR:
          _isLoading = false;
          Log.v("Error..........................${loginState.error}");
          FirebaseAnalytics.instance.logEvent(name: 'verify_otp', parameters: {
            "ERROR": '${loginState.error}',
          });

          if (loginState.status == 2) {
            if (APK_DETAILS["register_in_app"] == "0") {
              Navigator.push(
                  context,
                  NextPageRoute(CommonWebView(
                    url: "https://mec.edu.om/" +
                        "${Preference.getString(Preference.LANGUAGE)}",
                  )));
            } else {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => SelfDetailsPage(
                            phoneNumber: widget.username,
                          )));
            }
          } else {
            AlertsWidget.showCustomDialog(
                context: context,
                title: "${loginState.error}",
                text: "",
                icon: 'assets/images/circle_alert_fill.svg',
                oKText: '${tr('ok')}',
                showCancel: false,
                onOkClick: () async {});
          }

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  final defaultPinTheme = PinTheme(
    width: 50,
    height: 50,
    textStyle: TextStyle(
        fontSize: 20, color: Color(0xff0E1638), fontWeight: FontWeight.w600),
    decoration: BoxDecoration(
      border: Border.all(color: Color(0xff929BA3)),
      borderRadius: BorderRadius.circular(10),
    ),
  );

  void userIDEvent(String userid) async {
    print('Analytics userid:- $userid');
    await FirebaseAnalytics.instance.setUserId(id: userid);
  }

  @override
  Widget build(BuildContext context) {
    String appBarImagePath = 'assets/images/${APK_DETAILS['theme_image_url']}';

    return MultiBlocListener(
      listeners: [
        BlocListener<AuthBloc, AuthState>(
          listener: (BuildContext context, state) {
            if (state is LoginState) _handleLoginResponseOTP(state);
            if (state is VerifyOtpState) _handleVerifyResponse(state);
          },
        ),
        BlocListener<HomeBloc, HomeState>(
          listener: (BuildContext context, state) {
            if (state is GetBottomBarState) _handelBottomNavigationBar(state);
            if (state is PiDetailState) {
              handlePiDetail(state);
            }
            if (state is PortfolioState) {
              handlePortfolioState(state);
            }
          },
        ),
      ],
      child: WillPopScope(
        onWillPop: () async {
          Navigator.pop(context, true);
          return true;
        },
        child: Scaffold(
          backgroundColor: ColorConstants.WHITE,
          appBar: AppBar(
            elevation: 0,
            backgroundColor: ColorConstants.WHITE,
            title: Text(otpCode),
            iconTheme: IconThemeData(color: ColorConstants.BLACK),
          ),
          body: ScreenWithLoader(
            isLoading: _isLoading,
            body: SingleChildScrollView(
              child: Container(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 20.0),
                      child: Text("welcome_msg",
                              style: Styles.regular(
                                  size: 18, color: Color(0xff5A5F73)))
                          .tr(),
                    ),
                    APK_DETAILS['package_name'] == 'com.singularis.mesc'
                        ? SizedBox(
                            height: 20,
                          )
                        : SizedBox(),
                    Center(
                      child: Column(
                        children: [
                          Transform.scale(
                            scale: 1.0,
                            child: appBarImagePath.split('.').last == 'svg'
                                ? SvgPicture.asset(
                                    appBarImagePath,
                                    fit: BoxFit.cover,
                                    width: width(context) * 0.5,
                                  )
                                : Padding(
                                    padding: const EdgeInsets.only(
                                        left: 80.0,
                                        top: 2.0,
                                        right: 80.0,
                                        bottom: 20.0),
                                    child: Image.asset(
                                      appBarImagePath,
                                      width: width(context) * 0.5,
                                      //height: 150,
                                      //width: 150,
                                    ),
                                  ),
                          ),
                          SizedBox(height: 60),

                          // Padding(
                          //   padding: const EdgeInsets.only(left: 16.0, top: 8.0),
                          //   child: Row(
                          //     mainAxisAlignment: MainAxisAlignment.start,
                          //     children: [
                          //       Text('Login with',
                          //           style: TextStyle(
                          //               fontSize: 16,
                          //               fontWeight: FontWeight.w400,
                          //               color: Color(0xff0E1638))),
                          //     ],
                          //   ),
                          // ),
                          // Container(
                          //   padding: EdgeInsets.all(10.0),
                          //   child: ToggleButton(
                          //     width: 200.0,
                          //     height: 47.0,
                          //     toggleBackgroundColor: Color(0xffF3F3F3),
                          //     toggleBorderColor: (Colors.grey[350])!,
                          //     toggleColor: (Colors.white),
                          //     activeTextColor: Color(0xff0E1638),
                          //     inactiveTextColor: Colors.grey,
                          //     leftDescription: 'Email',
                          //     rightDescription: 'Mobile',
                          //     onLeftToggleActive: () {
                          //       // Navigator.push(
                          //       //     context,
                          //       //     MaterialPageRoute(
                          //       //         builder: (context) => SignUpScreen()));
                          //     },
                          //     onRightToggleActive: () {
                          //       // Navigator.push(
                          //       //     context,
                          //       //     MaterialPageRoute(
                          //       //         builder: (context) => SingularisLogin()));
                          //     },
                          //   ),
                          // ),
                          Center(
                            child: Padding(
                                padding: const EdgeInsets.only(top: 30.0),
                                child: Text(
                                  'Verify_mob_number',
                                  style: Styles.bold(color: Color(0xff0E1638)),
                                  textAlign: TextAlign.center,
                                ).tr()),
                          ),
                          _size(height: 25),
                          kIsWeb
                              ? Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: TextFormField(
                                    maxLength: 4,
                                    cursorColor: ColorConstants.GREY_3,
                                    autofocus: false,
                                    keyboardType: TextInputType.number,
                                    style: Styles.bold(
                                      color: ColorConstants.GREY_3,
                                      size: 14,
                                    ),

                                    // maxLength: 10,
                                    decoration: InputDecoration(
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                        borderSide: const BorderSide(
                                          color: ColorConstants.GREY_3,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                        borderSide: const BorderSide(
                                          color: ColorConstants.GREY_3,
                                          width: 0.7,
                                        ),
                                      ),
                                      fillColor: ColorConstants.GREY_3,
                                      hintText: '*    *    *    *',
                                      hintStyle: Styles.regular(
                                        color: ColorConstants.GREY_3,
                                        size: 14,
                                      ),
                                      isDense: true,
                                      prefixIconConstraints:
                                          const BoxConstraints(
                                              minWidth: 0, minHeight: 0),
                                      border: OutlineInputBorder(
                                          borderSide: const BorderSide(
                                              width: 0.7,
                                              color: ColorConstants.GREY_3),
                                          borderRadius:
                                              BorderRadius.circular(10)),
                                      helperStyle: Styles.regular(
                                          size: 14,
                                          color: ColorConstants.GREY_3
                                              .withValues(alpha: 0.1)),
                                      counterText: "",
                                    ),
                                    onChanged: (value) {
                                      setState(() {
                                        _pin = value;
                                      });
                                    },
                                  ),
                                )
                              : Center(
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 45),
                                    child: Pinput(
                                      defaultPinTheme: defaultPinTheme,
                                      isCursorAnimationEnabled: true,
                                      length: 4,
                                      controller: otpController,
                                      onChanged: (String code) {
                                        setState(() {
                                          _pin = code;
                                        });
                                      },
                                      focusNode: focusNode,
                                      onSubmitted: (String pin) {
                                        setState(() {
                                          _pin = pin;
                                        });
                                      },
                                    ),
                                  ),
                                ),
                          _size(height: 150),
                          // _size(height: height(context) * 0.10),
                          InkWell(
                            onTap: () {
                              if (_pin.isNotEmpty) {
                                if (_pin.length == 4) {
                                  verifyOTP();
                                } else {
                                  Utility.showSnackBar(
                                      scaffoldContext: context,
                                      message: tr('enter_valid_otp'));
                                }
                              } else {
                                Utility.showSnackBar(
                                    scaffoldContext: context,
                                    message: tr('enter_otp'));
                              }
                            },
                            child: Container(
                              margin: EdgeInsets.symmetric(
                                horizontal: 14,
                              ),
                              width: double.infinity,
                              height: MediaQuery.of(context).size.height * 0.06,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                gradient: LinearGradient(colors: [
                                  ColorConstants().gradientLeft(),
                                  ColorConstants().gradientRight(),
                                ]),
                                boxShadow: [
                                  BoxShadow(
                                      color: ColorConstants()
                                          .gradientRight()
                                          .withValues(alpha: 0.3),
                                      offset: Offset(2.5, 10),
                                      blurRadius: 20)
                                ],
                              ),
                              child: Center(
                                child: Text(
                                  'login',
                                  style: Styles.bold(
                                    size: 16,
                                    color: ColorConstants.WHITE,
                                  ),
                                ).tr(),
                              ),
                            ),
                          ),
                          SizedBox(height: 20),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 10.0),
                            child: Row(
                              children: [
                                Icon(
                                  Utility().isRTL(context)
                                      ? Icons.arrow_back_ios_sharp
                                      : Icons.arrow_back_ios_new,
                                  color: Color(0xff0E1638),
                                  size: 15,
                                ),
                                InkWell(
                                  onTap: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: Text(
                                    'go_back',
                                    style: Styles.semibold(
                                        size: 12, color: Color(0xff0E1638)),
                                  ).tr(),
                                ),
                                Expanded(
                                  child: SizedBox(),
                                ),
                                CountdownTimer(
                                  endTime: endTime,
                                  widgetBuilder:
                                      (_, CurrentRemainingTime? time) {
                                    return RichText(
                                      text: TextSpan(
                                          text: '',
                                          style: TextStyle(
                                            fontSize: 3,
                                          ),
                                          children: <TextSpan>[
                                            time == null
                                                ? TextSpan(
                                                    text: tr(
                                                      'resend_otp',
                                                    ),
                                                    recognizer:
                                                        TapGestureRecognizer()
                                                          ..onTap = () {
                                                            resendOTP();
                                                          },
                                                    style: Styles.semibold(
                                                        size: 12,
                                                        color:
                                                            Color(0xff0E1638)))
                                                : TextSpan(
                                                    text:
                                                        '${tr('resend_app_secs')} ${time.sec} ${tr('secs')}',
                                                    style: Styles.regular(
                                                        size: 12,
                                                        color:
                                                            Color(0xff0E1638))),
                                          ]),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    _size(height: 60),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  _size({double height = 20}) {
    return SizedBox(
      height: height,
    );
  }

  void verifyOTP() {
    if (!kIsWeb) Utility.hideKeyboard();
    if (_pin.length != 4) {
      return;
    }

    Utility.checkNetwork().then((isConnected) {
      if (isConnected) {
        var verifyOtp = EmailRequest(
            mobileNo: widget.username,
            optKey: _pin,
            deviceType: Utility.getDeviceType(),
            deviceId: deviceId,
            locale:
                Preference.getString(Preference.APP_ENGLISH_NAME).toString(),
            deviceToken: UserSession.firebaseToken);

        BlocProvider.of<AuthBloc>(context)
            .add(VerifyOtpEvent(request: verifyOtp));
      } else {
        // AlertsWidget.alertWithOkBtn(
        //     context: context,
        //     text: Strings.NO_INTERNET_MESSAGE,
        //     onOkClick: () {
        //       FocusScope.of(context).unfocus();
        //     });
      }
    });
  }

  void resendOTP() {
    setState(() {
      _isLoading = true;
    });
    BlocProvider.of<AuthBloc>(context).add(LoginUser(
        request:
            LoginRequest(mobileNo: widget.username, mobile_exist_skip: '1')));
  }
}
